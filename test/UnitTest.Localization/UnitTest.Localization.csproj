<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <Compile Include="..\..\src\BootstrapBlazor.Server\Data\Foo.cs" Link="Mics\Foo.cs" />
    <Compile Include="..\UnitTest\Core\BootstrapBlazorTestBase.cs" Link="Mics\BootstrapBlazorTestBase.cs" />
    <Compile Include="..\UnitTest\Core\TestBase.cs" Link="Mics\TestBase.cs" />
    <Compile Include="..\UnitTest\Extensions\IServiceCollectionExtensions.cs" Link="Mics\IServiceCollectionExtensions.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="bunit" Version="1.*" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.*" />
    <PackageReference Include="xunit" Version="2.*" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.*">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.*">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Content Remove="Locales\*.json" />
    <EmbeddedResource Include="Locales\*.json" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Microsoft.Extensions.DependencyInjection" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\UnitTest\appsettings.json" Link="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\BootstrapBlazor\BootstrapBlazor.csproj" />
  </ItemGroup>

</Project>
