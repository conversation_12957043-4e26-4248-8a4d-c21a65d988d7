{"BootstrapBlazor.Server.Data.Foo": {"Name": "姓名", "DateTime": "日期", "Address": "地址", "Count": "数量", "Complete": "是/否", "Education": "学历", "Hobby": "爱好", "Name.Required": "{0}是必填项", "Address.Required": "{0}是必填项", "Education.Required": "{0}是必选项", "Hobby.Required": "请选择一种{0}", "Name.PlaceHolder": "不可为空", "Hobbys": "游泳,登山,打球,下棋", "Foo.Name": "张三 {0}", "Foo.Address": "上海市普陀区金沙江路 {0} 弄", "Foo.Address2": "地球、中国、上海市普陀区金沙江路 {0} 弄 这里是超长单元格示例", "Foo.BindValue": "绑定值", "True": "通过", "False": "未通过", "NullItemText": "未设置"}, "BootstrapBlazor..Server.Data.EnumEducation": {"PlaceHolder": "请选择 ...", "Primary": "小学", "Middle": "中学"}}