// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the Apache 2.0 License
// See the LICENSE file in the project root for more information.
// Maintainer: <PERSON><PERSON>(<EMAIL>) Website: https://www.blazor.zone

namespace UnitTest.Performance;

public class TaskTest
{
    [Fact]
    public async Task Cancel_Ok()
    {
        var cts = new CancellationTokenSource();
        cts.Cancel();
        await Assert.ThrowsAnyAsync<TaskCanceledException>(() => Task.Delay(10, cts.Token));
    }
}
