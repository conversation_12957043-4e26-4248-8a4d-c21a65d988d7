// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the Apache 2.0 License
// See the LICENSE file in the project root for more information.
// Maintainer: <PERSON><PERSON>(<EMAIL>) Website: https://www.blazor.zone

using Microsoft.Extensions.FileProviders;

namespace Microsoft.AspNetCore.Builder;

static class WebApplicationExtensions
{
    public static void UseUploaderStaticFiles(this WebApplication app)
    {
        var uploader = Path.Combine(app.Environment.WebRootPath, "images", "uploader");
        Directory.CreateDirectory(uploader);

        app.UseStaticFiles(new StaticFileOptions
        {
            FileProvider = new PhysicalFileProvider(uploader),
            RequestPath = "/images/uploader"
        });
    }
}
