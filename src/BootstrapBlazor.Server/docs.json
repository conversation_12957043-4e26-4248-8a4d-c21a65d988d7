{"src": {"ajax": "Ajaxs", "alert": "<PERSON><PERSON><PERSON>", "anchor-link": "AnchorLinks", "anchor": "Anchors", "auto-complete": "AutoCompletes", "auto-fill": "AutoFills", "auto-redirect": "AutoRedirects", "avatar": "Avatars", "badge": "Badges", "barcode-reader": "BarcodeReaders", "block": "Blocks", "blue-tooth": "Bluetooth", "breadcrumb": "Breadcrumbs", "browser-finger": "Browser<PERSON><PERSON><PERSON>", "button": "Buttons", "calendar": "Calendars", "camera": "Cameras", "captcha": "<PERSON><PERSON><PERSON>", "card": "Cards", "carousel": "Carousels", "cascader": "Cascaders", "chart/bar": "Charts\\Bar", "chart/bubble": "Charts\\Bubble", "chart/doughnut": "Charts\\Doughnut", "chart/line": "Charts\\Line", "chart/pie": "Charts\\Pie", "checkbox-list": "CheckboxLists", "checkbox": "Checkboxs", "cherry-markdown": "CherryMarkdowns", "circle": "Circles", "client": "Client", "clock-picker": "ClockPickers", "code-editors": "CodeEditors", "collapse": "Collapses", "color-picker": "ColorPickers", "console": "<PERSON><PERSON><PERSON>", "context-menu": "ContextMenus", "count-up": "CountUps", "count-button": "CountButtons", "datetime-picker": "DateTimePickers", "datetime-range": "DateTimeRanges", "dialog": "Dialogs", "dial-button": "DialButtons", "dispatch": "Dispatches", "display": "Displays", "divider": "Dividers", "dock-view/col": "DockViews\\DockViewCol", "dock-view/complex": "DockViews\\DockViewComplex", "dock-view/nest": "DockViews\\DockViewNest", "dock-view/row": "DockViews\\DockViewRow", "dock-view/stack": "DockViews\\DockViewStack", "dock-view/lock": "DockViews\\DockViewLock", "dock-view/visible": "DockViews\\DockViewVisible", "dock-view/layout": "DockViews\\DockViewLayout", "dock-view2/col": "DockViews2\\DockViewCol", "dock-view2/complex": "DockViews2\\DockViewComplex", "dock-view2/nest": "DockViews2\\DockViewNest", "dock-view2/row": "DockViews2\\DockViewRow", "dock-view2/group": "DockViews2\\DockViewGroup", "dock-view2/lock": "DockViews2\\DockViewLock", "dock-view2/visible": "DockViews2\\DockViewVisible", "dock-view2/layout": "DockViews2\\DockViewLayout", "dock-view2/title": "DockViews2\\DockViewTitle", "download": "Downloads", "drag-drop": "DragDrops", "drawer": "Drawers", "driver-js": "DriverDotnetJs", "dropdown": "Dropdowns", "dropdown-widget": "DropdownWidgets", "edit-dialog": "EditDialogs", "editor-form": "EditorForms", "editor": "Editors", "empty": "Empties", "eye-dropper": "EyeDroppers", "export-pdf-button": "ExportPdfButtons", "file-icon": "FileIcons", "flip-clock": "FlipClocks", "file-viewer": "FileViewers", "floating-label": "Floating<PERSON>abels", "footer": "Footers", "fullscreen": "FullScreens", "gantt": "Gantts", "geolocation": "Geolocations", "global-exception": "GlobalException", "go-top": "GoTops", "group-box": "GroupBoxes", "handwritten": "Handwrittens", "html-renderer": "HtmlRenderers", "html2image": "Html2Images", "html2pdf": "Html2Pdfs", "label": "Labels", "layout": "Layouts", "light": "Lights", "link-button": "LinkButtons", "list-group": "ListGroups", "list-view": "ListViews", "live2d-display": "Live2DDisplays", "locator": "Locators", "logout": "Logouts", "icon": "BootstrapBlazorIcons", "iframe": "IFrames", "image-viewer": "ImageViewers", "input-number": "InputNumbers", "input": "Inputs", "input-group": "InputGroups", "ip": "<PERSON><PERSON>", "intersection-observer": "IntersectionObservers", "mask": "Masks", "markdown": "Markdowns", "marquee": "Marquees", "menu": "Menus", "message": "Messages", "mind-map": "MindMaps", "mermaid": "Mermaids", "modal": "Modals", "mouse-follower": "MouseFollowers", "multi-select": "MultiSelects", "navigation": "Navigation", "notification": "Notifications", "ocr": "BaiduOcr", "onscreen-keyboard": "OnScreenKeyboards", "pagination": "Paginations", "pdf-reader": "PdfReaders", "pdf-viewer": "PdfViewers", "pop-confirm": "PopoverConfirms", "popover": "Popovers", "progress": "Progress", "print": "Print", "pulse-button": "PulseButtons", "qr-code": "QRCodes", "query-builder": "QueryBuilders", "web-serial": "WebSerials", "radio": "Radios", "rate": "Rates", "reconnector": "Reconnectors", "repeater": "Repeaters", "responsive": "Responsives", "ribbon-tab": "RibbonTabs", "row": "Rows", "scroll": "Scrolls", "search-dialog": "SearchDialogs", "search": "Searches", "select": "Selects", "select-generic": "SelectGenerics", "select-object": "SelectObjects", "select-tree": "SelectTrees", "select-table": "SelectTables", "segmented": "Segmenteds", "slider": "Sliders", "slide-button": "SlideButtons", "signature-pad": "SignaturePads", "signature-responsive": "SignaturePadPageResponsive", "skeleton": "Skeletons", "speech/wave": "Speeches\\SpeechWaves", "speech/recognizer": "Speeches\\Recognizers", "speech/synthesizer": "Speeches\\Synthesizers", "speech/web-speech": "Speeches\\WebSpeeches", "spinner": "Spinners", "splitting": "Splittings", "split": "Splits", "stack": "Stacks", "step": "Steps", "sweet-alert": "<PERSON><PERSON><PERSON><PERSON>", "switch-button": "SwitchButtons", "switch": "Switches", "svg-editors": "SvgEditors", "tab": "Tabs", "tag": "Tags", "textarea": "TextAreas", "time-picker": "TimePickers", "timeline": "Timelines", "timer": "Timers", "title": "Titles", "toast": "Toasts", "toggle": "Toggles", "tooltip": "Tooltips", "topology": "Topologies", "transfer": "Transfers", "transition": "Transitions", "tree-view": "TreeViews", "upload-input": "UploadInputs", "upload-button": "UploadButtons", "upload-card": "UploadCards", "upload-avatar": "UploadAvatars", "upload-drop": "UploadDrops", "validate-form": "ValidateForms", "video-player": "VideoPlayers", "table": "Table\\Tables", "table/cell": "Table\\TablesCell", "table/auto-refresh": "Table\\TablesAutoRefresh", "table/column": "Table\\TablesColumn", "table/column/drag": "Table\\TablesColumnDrag", "table/column/list": "Table\\TablesColumnList", "table/column/resizing": "Table\\TablesColumnResizing", "table/column/template": "Table\\TablesColumnTemplate", "table/detail": "Table\\TablesDetailRow", "table/dialog": "Table\\TablesDialog", "table/dynamic": "Table\\TablesDynamic", "table/dynamic-excel": "Table\\TablesDynamicExcel", "table/dynamic-object": "Table\\TablesDynamicObject", "table/edit": "Table\\TablesEdit", "table/excel": "Table\\TablesExcel", "table/export": "Table\\TablesExport", "table/filter": "Table\\TablesFilter", "table/fix-column": "Table\\TablesFixedColumn", "table/header": "Table\\TablesFixedHeader", "table/footer": "Table\\TablesFooter", "table/multi-header": "Table\\TablesHeader", "table/loading": "Table\\TablesLoading", "table/lookup": "Table\\TablesLookup", "table/page": "Table\\TablesPages", "table/row": "Table\\TablesRow", "table/search": "Table\\TablesSearch", "table/selection": "Table\\TablesSelection", "table/toolbar": "Table\\TablesToolbar", "table/tracking": "Table\\TablesTracking", "table/tree": "Table\\TablesTree", "table/virtualization": "Table\\TablesVirtualization", "table/wrap": "Table\\TablesWrap", "translator": "Translators", "js-extensions": "JSRuntimeExtensions", "clipboard-service": "Clipboards", "image-cropper": "ImageCroppers", "barcode-generator": "BarcodeGenerators", "sortable-list": "SortableLists", "win-box": "WinBoxes", "player": "Players", "rdkit": "Rdkits", "smiles-drawer": "SmilesDrawers", "affix": "Affixs", "watermark": "Watermarks", "typed": "Typeds", "univer-sheet": "UniverSheets", "shield-badge": "ShieldBadges", "otp-input": "OtpInputs", "otp-service": "OtpServices", "video-device": "VideoDevices", "audio-device": "AudioDevices", "fullscreen-button": "FullScreenButtons", "meet": "Meets", "vditor": "Vditors", "socket-factory": "SocketFactories", "office-viewer": "OfficeViewers", "socket/manual-receive": "Sockets\\ManualReceives", "socket/auto-receive": "Sockets\\AutoReceives", "socket/adapter": "Sockets\\Adapters", "socket/auto-connect": "Sockets\\AutoReconnects", "socket/data-entity": "Sockets\\DataEntities", "network-monitor": "NetworkMonitors"}, "video": {"table": "BV1ap4y1x7Qn?p=1", "table/auto-refresh": "BV1ap4y1x7Qn?p=8", "table/column": "BV1ap4y1x7Qn?p=2;BV1ZH4y1w7S2", "table/edit": "BV1ap4y1x7Qn?p=9;BV1ap4y1x7Qn?p=10;BV1ap4y1x7Qn?p=11;BV1ap4y1x7Qn?p=12;BV12P4y137Ar", "table/detail": "BV1ap4y1x7Qn?p=3", "table/dialog": "BV1bT4y1N78e?p=1", "table/export": "BV1ap4y1x7Qn?p=6;BV1nN411V7W9;BV1Nb4y1L7p9", "table/header": "BV15o4y1f7eN", "table/filter": "BV1ap4y1x7Qn?p=4", "table/fix-column": "BV1ap4y1x7Qn?p=5", "table/multi-header": "BV15o4y1f7eN", "table/footer": "BV15o4y1f7eN", "table/row": "BV1ap4y1x7Qn?p=3;BV1sE421P7BG", "table/search": "BV1ap4y1x7Qn?p=4;BV1E34y1R7ia", "table/selection": "BV1jh41127U6;BV1Y4421U7u6", "table/toolbar": "BV1ap4y1x7Qn?p=6", "table/wrap": "BV1ap4y1x7Qn?p=7", "table/dynamic": "BV1Eb4y1z7cY", "table/excel": "BV1QL411x7v4", "tale/dynamic-excel": "BV1p3411278A", "table/attribute": "BV1CS421X75U", "editor": "BV13B4y1y7cS", "edit-dialog": "BV1bT4y1N78e?p=10", "datetime-picker": "BV1W1421b7du", "drawer": "BV1bT4y1N78e?p=7", "dialog": "BV1bT4y1N78e?p=1;BV17v4y1K7Ho", "flip-clock": "BV1QH4y1w7e5", "message": "BV1bT4y1N78e?p=3", "modal": "BV1bT4y1N78e?p=4", "pagination": "BV1Et4y1r7qr", "pop-confirm": "BV1bT4y1N78e?p=5", "search-dialog": "BV1bT4y1N78e?p=9", "sweet-alert": "BV1bT4y1N78e?p=8", "toast": "BV1bT4y1N78e?p=6", "tree-view": "BV1ap4y1x7Qn?p=14;BV1ZW4y1z7bB;BV15c411v7w7", "upload": "BV1hK4y157Rj", "validate-form": "BV1TU4y1Y7CM;BV19E421c7tH", "speech/speechwave": "BV1Dr4y1J7Z5", "speech/recognizer": "BV1aR4y1N7UP", "speech/synthesizer": "BV1aR4y1N7UP", "reconnector": "BV1Dr4y1J7Z5;BV193411P7Dz", "topology": "BV1eY4y167jn;BV13Z4y1h7MA", "select-object": "BV1Zw411j7Ea", "select-table": "BV1f64y1A7AL;BV1Ye411n7XR", "step": "BV1oN4y1y75m", "context-menu": "BV1gk4y1w7Ab", "globalexception": "BV1xq4y1z7K2", "localizer": "BV1Kz4y1U7FR?p=1", "template": "BV1Kp4y1B7pY", "theme-provider": "BV144421U786", "dockview2/col": "BV1sW421d74o", "dockview2/row": "BV1sW421d74o", "dockview2/group": "BV1sW421d74o", "dockview2/complex": "BV1sW421d74o", "dockview2/nest": "BV1sW421d74o", "dockview2/visible": "BV1sW421d74o", "dockview2/lock": "BV1sW421d74o", "dockview2/title": "BV1sW421d74o", "dockview2/layout": "BV1sW421d74o", "icon": "BV1jx4y1s73i", "mask": "BV16E421w7vD", "global-option": "BV17T421k7U1"}, "link": {"AntDesign": "http://www.antblazor.com/", "Pear Admin": "http://www.pearadmin.com/", "SAPHP": "https://www.swiftadmin.net/", "Veterinary Hospital": "http://animal.jucun.zone/"}}