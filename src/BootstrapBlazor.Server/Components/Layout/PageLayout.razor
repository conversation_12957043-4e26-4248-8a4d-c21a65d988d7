@inherits LayoutComponentBase
@inject IOptions<WebsiteOptions> WebsiteOption

<HeadContent>
    <link href="@WebsiteOption.Value.GetAssetUrl("css/layout.css")" rel="stylesheet" />
</HeadContent>

<CascadingValue Value="this" IsFixed="true">
    <Layout IsPage="true" IsFullSide="@IsFullSide" IsFixedHeader="@IsFixedHeader"
            IsFixedFooter="@IsFixedFooter" IsFixedTabHeader="IsFixedTabHeader"
            ShowFooter="@ShowFooter" ShowGotoTop="true" ShowCollapseBar="true" Menus="@Menus"
            UseTabSet="@UseTabSet" TabDefaultUrl="layout-page" AdditionalAssemblies="new[] { GetType().Assembly }"
            class="@LayoutClassString">
        <Header>
            <span class="ms-3 flex-fill">Bootstrap of Blazor</span>
            <Widget></Widget>
            <Logout ImageUrl="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" DisplayName="超级管理员" UserName="Admin">
                <LinkTemplate>
                    <a href="#"><i class="fa-solid fa-suitcase"></i>个人中心</a>
                    <a href="#"><i class="fa-solid fa-cog"></i>设置</a>
                    <a href="#"><i class="fa-solid fa-bell"></i>通知<span class="badge badge-pill badge-success"></span></a>
                    <LogoutLink />
                </LinkTemplate>
            </Logout>
        </Header>
        <Side>
            <div class="layout-banner">
                <img alt="logo" class="layout-logo" src="@WebsiteOption.Value.GetAssetUrl("images/logo.png")" />
                <div class="layout-title">
                    <span>后台管理</span>
                </div>
            </div>
        </Side>
        <Main>
            @Body
        </Main>
        <Footer>
            <div class="text-center flex-fill">
                <a class="page-layout-demo-footer-link" href="@WebsiteOption.Value.BootstrapAdminLink" target="_blank">Bootstrap Admin</a>
            </div>
        </Footer>
    </Layout>
</CascadingValue>
