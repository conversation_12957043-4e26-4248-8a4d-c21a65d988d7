@inherits LayoutComponentBase
@layout BaseLayout

<section class="section">
    <aside class="sidebar">
        <div class="sidebar-title">
            <a href="/">
                <BBLogo />
            </a>
            <span class="sidebar-text">Bootstrap Blazor</span>
        </div>
        <NavMenu />
        <LayoutSplitBar Min="250" Max="380" ContainerSelector=".section"></LayoutSplitBar>
    </aside>

    <section class="main">
        <Wwads />
        @Body
    </section>

    <DialButton DialMode="DialMode.Radial" Icon="fa-solid fa-gear" Radius="100" Placement="Placement.BottomEnd" class="bb-dial-gear">
        <ButtonTemplate>
            <div class="btn-circle btn-fade dial-button-gear">
                <img src="@WebsiteOption.Value.GetAssetUrl("images/logo.png")" />
            </div>
        </ButtonTemplate>
        <ChildContent>
            <DialButtonItem>
                <ThemeMode></ThemeMode>
            </DialButtonItem>
            <DialButtonItem>
                <LinkButton TooltipPlacement="Placement.Left" ButtonStyle="ButtonStyle.Circle" Color="Color.None" TooltipText="@ChatTooltip" class="btn-fade btn-chat" Url="./ai-chat" Icon="fa-solid fa-comments"></LinkButton>
            </DialButtonItem>
            <DialButtonItem>
                <ThemeChooser></ThemeChooser>
            </DialButtonItem>
            <DialButtonItem>
                <LinkButton TooltipPlacement="Placement.Left" ButtonStyle="ButtonStyle.Circle" Color="Color.None" TooltipText="@Title" class="btn-fade btn-update" Url="@WebsiteOption.Value.WikiUrl" Target="_blank" ImageUrl="@WebsiteOption.Value.GetAssetUrl("images/log.svg")"></LinkButton>
            </DialButtonItem>
        </ChildContent>
    </DialButton>
</section>
