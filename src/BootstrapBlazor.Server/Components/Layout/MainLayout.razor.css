.section {
    position: relative;
}

.main {
    padding: var(--bb-main-pading);
    position: relative;
    z-index: 5;
}

.sidebar-title {
    height: 50px;
    align-items: center;
    padding: 1rem;
    border-bottom: solid 1px var(--bs-border-color);
    display: none;
}

.sidebar-text {
    font-weight: 700;
}

::deep p:last-child {
    margin-bottom: 0;
}

::deep .code-label {
    font-weight: bold;
}

::deep h3 {
    font-size: var(--bb-title-font-size);
    font-weight: var(--bb-font-weight);
}

::deep h4 {
    margin-top: 1rem;
    font-size: var(--bb-sub-font-size);
}

::deep h5 {
    font-size: var(--bb-sub-font-size);
    font-weight: var(--bb-font-weight);
}

::deep .alert:last-child {
    margin-bottom: 0;
}

::deep .alert h4 {
    margin-top: 0;
}

.sidebar-bar {
    width: 1px;
    position: absolute;
    top: 0;
    right: -1px;
    bottom: 0;
    background-color: var(--bs-border-color);
    display: none;
}

    .sidebar-bar .sidebar-body {
        position: absolute;
        inset: 0px -2px;
        cursor: col-resize;
        background-color: transparent;
        border-radius: 4px;
    }

        .sidebar-bar .sidebar-body:hover {
            background-color: var(--bb-sidebar-body-hover-bg);
        }

        .sidebar-bar .sidebar-body.drag,
        .sidebar-bar .sidebar-body.drag:hover {
            background-color: var(--bb-sidebar-body-drag-hover-bg);
        }

::deep .bb-dial-gear {
    display: none;
    position: fixed;
    z-index: 10;
    right: 1rem;
    bottom: 1rem;
}

::deep .btn-fade {
    opacity: 0.7;
    box-shadow: var(--bb-layout-button-shadow);
    transition: opacity .3s linear;
}

    ::deep .btn-fade:hover {
        opacity: 1;
    }

::deep .btn-update {
    --bs-btn-bg: var(--bb-layout-button-update-bg);
    --bs-btn-hover-bg: var(--bs-btn-bg);
    --bs-btn-active-bg: var(--bs-btn-bg);
    display: flex;
    justify-content: center;
    align-items: center;
}

    ::deep .btn-update img {
        width: 55%;
        margin-inline-start: 2px;
    }

::deep .btn-chat {
    --bs-btn-bg: var(--bb-layout-button-bg);
    --bs-btn-hover-bg: var(--bb-layout-button-hover-bg);
    --bs-btn-active-bg: var(--bb-layout-button-active-bg);
    box-shadow: var(--bb-layout-button-shadow);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bb-button-chat-color);
}

::deep .btn-theme-mode {
    --bs-btn-bg: var(--bb-layout-button-bg);
    --bs-btn-hover-bg: var(--bb-layout-button-hover-bg);
    --bs-btn-active-bg: var(--bb-layout-button-active-bg);
    box-shadow: var(--bb-layout-button-shadow);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--bb-button-chat-color);
}

.dial-button-gear {
    background-color: var(--bb-primary-color);
    border-radius: 50%;
    padding: 4px;
    cursor: pointer;
}

    .dial-button-gear img {
        width: 100%;
    }

@media (min-width: 768px) {
    .section {
        --bb-layout-sidebar-width: 300px;
        display: flex;
        flex-direction: row;
        -webkit-font-smoothing: antialiased;
    }

    .main {
        flex: 1;
        width: 1%;
        min-width: 0;
    }

    .sidebar-title {
        display: flex;
    }

    .sidebar {
        width: var(--bb-layout-sidebar-width);
        height: calc(100vh);
        position: sticky;
        top: 0;
        border-right: solid 1px var(--bs-border-color);
        margin-top: calc(var(--bs-header-height) * -1);
    }

    .sidebar-bar {
        display: block;
    }

    ::deep .bb-dial-gear {
        display: block;
    }
}
