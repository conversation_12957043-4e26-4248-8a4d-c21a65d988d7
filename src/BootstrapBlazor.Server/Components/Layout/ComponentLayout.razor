@inherits LayoutComponentBase
@layout MainLayout
@inject IOptions<WebsiteOptions> WebsiteOption

<div class="tabs-coms-header">
    <div class="bb-title">
        <BBLogo />
        <span>@Title</span>
    </div>

    <div class="text-center">
        <ImageViewer Alt="gitee" IsAsync="true" Url="@GVPUrl" />
        <span class="d-none d-sm-inline">
            <ImageViewer Alt="version" IsAsync="true" Url="https://img.shields.io/nuget/vpre/BootstrapBlazor.svg?logo=nuget&logoColor=green" />
            <ImageViewer Alt="license" IsAsync="true" Url="https://img.shields.io/github/license/dotnetcore/BootstrapBlazor.svg?logo=git&logoColor=red" />
            <ImageViewer Alt="download" IsAsync="true" Url="https://img.shields.io/nuget/dt/BootstrapBlazor.svg?logo=nuget&logoColor=green" />
            <ImageViewer Alt="repo" IsAsync="true" Url="https://img.shields.io/github/repo-size/dotnetcore/BootstrapBlazor.svg?logo=github&logoColor=green&label=repo" />
            <ImageViewer Alt="commit" IsAsync="true" Url="https://img.shields.io/github/last-commit/dotnetcore/BootstrapBlazor/main.svg?logo=github&logoColor=green&label=commit" />
            <ImageViewer Alt="build" IsAsync="true" Url="https://img.shields.io/github/actions/workflow/status/dotnetcore/BootstrapBlazor/build.yml?branch=main&?label=main&logo=github" />
        </span>
        <ImageViewer Alt="coverage" IsAsync="true" Url="https://codecov.io/gh/dotnetcore/BootstrapBlazor/branch/main/graph/badge.svg?token=5SXIWHXZC3" />
    </div>

    <div class="d-flex justify-content-center align-items-center">
        <div class="bb-theme-label">@Localizer["IconTheme"]</div>
        <div><RadioList TValue="string" Items="IconThemes" Value="@IconThemeKey" OnValueChanged="OnIconThemeChanged"></RadioList></div>
    </div>

    <hr />
</div>

<div class="tabs-coms">
    <Tab IsBorderCard="true" IsLazyLoadTabItem="true" TabStyle="TabStyle.Chrome" ShowToolbar="true" @ref="Tab">
        <TabItem Text="Example" Icon="fa-solid fa-desktop">
            <CascadingValue Value="@RazorFileName" Name="RazorFileName">
                @Body
            </CascadingValue>
        </TabItem>
        <TabItem Text="Razor" Icon="fa-brands fa-html5">
            <Pre @key="@RazorFileName" CodeFile="@RazorFileName" class="code-razor"></Pre>
        </TabItem>
        <TabItem Text="C#" Icon="fa-regular fa-file-code">
            <Pre @key="@CSharpFileName" CodeFile="@CSharpFileName" class="code-cs"></Pre>
        </TabItem>
        <TabItem Text="@Video" Icon="fa-regular fa-file-video">
            <Video @key="@VideoFileName" Name="@VideoFileName"></Video>
            <p><b>交流群</b></p>
            <QQGroup />
        </TabItem>
    </Tab>
</div>
