@page "/input-group"
@inject IStringLocalizer<InputGroups> Localizer
@inject IStringLocalizer<Foo> LocalizerFoo
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["InputGroupsTitle"]</h3>

<h4>@Localizer["InputGroupsDescription"]</h4>

<DemoBlock Title="@Localizer["InputGroupsNormalTitle"]" Introduction="@Localizer["InputGroupsNormalIntro"]" Name="Normal">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="@Localizer["InputGroupsNormalUserName"]" />
                <BootstrapInput @bind-Value="@BindValue" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInput @bind-Value="@BindValue" />
                <Button Icon="fa-solid fa-magnifying-glass"></Button>
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsWidthTitle"]" Introduction="@Localizer["InputGroupsWidthIntro"]" Name="Width">
    <div class="row g-3">
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="LongLongLongText" Width="146" />
                <BootstrapInput TValue="string" />
                <BootstrapInputGroupLabel DisplayText="%" Width="78" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="LongLongText" Width="146" />
                <BootstrapInput TValue="string" />
                <BootstrapInputGroupLabel DisplayText="kPa/min" Width="78" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="LongText" Width="146" />
                <BootstrapInput TValue="string" />
                <BootstrapInputGroupLabel DisplayText="km" Width="78" />
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsMultipleTitle"]" Introduction="@Localizer["InputGroupsMultipleIntro"]" Name="Multiple">
    <BootstrapInputGroup>
        <BootstrapInputGroupLabel DisplayText="@Localizer["InputGroupsMultipleDistance"]" />
        <BootstrapInput @bind-Value="@BindValue" />
        <BootstrapInputGroupLabel DisplayText="km" />
    </BootstrapInputGroup>
    <BootstrapInputGroup class="mt-2">
        <BootstrapInput @bind-Value="@BindValue" />
        <BootstrapInputGroupLabel DisplayText="@StringAt" />
        <BootstrapInput @bind-Value="@StringMailServer" />
        <Button Icon="fa-solid fa-print" IsOutline="true"></Button>
        <Button Icon="fa-solid fa-magnifying-glass" IsOutline="true"></Button>
    </BootstrapInputGroup>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsSelectTitle"]" Introduction="@Localizer["InputGroupsSelectIntro"]" Name="Select">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="Select" />
                <Select TValue="string" Items="@Items"></Select>
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="MultiSelect" />
                <MultiSelect TValue="string" Items="@Items2" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="AutoComplete" />
                <AutoComplete Items="@StaticItems" IsLikeMatch="true" IgnoreCase="false" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="AutoFill" />
                <AutoFill Items="AufoFillItems" IsLikeMatch="true" OnGetDisplayText="@(foo => foo?.Name)">
                    <ItemTemplate>
                        <div class="d-flex">
                            <div>
                                <img src="@WebsiteOption.Value.GetAvatarUrl(context.Id)" class="bb-avatar" />
                            </div>
                            <div class="ps-2">
                                <div>@context.Name</div>
                                <div class="bb-sub">@Foo.GetTitle(context.Id)</div>
                            </div>
                        </div>
                    </ItemTemplate>
                </AutoFill>
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsValidateFormTitle"]"
           Introduction="@Localizer["InputGroupsValidateFormIntro"]"
           Name="ValidateForm">
    <FormInlineSwitch @bind-Value="@FormRowType" class="mb-3"></FormInlineSwitch>
    <ValidateForm Model="@Model">
        <div class="@GroupFormClassString">
            <div class="col-12 col-sm-6">
                <BootstrapInputGroupLabel @bind-Value="@Model.Name" ShowRequiredMark="true" />
                <BootstrapInputGroup>
                    <Display @bind-Value="@Model.Name"></Display>
                    <BootstrapInputGroupLabel @bind-Value="@Model.Name" />
                </BootstrapInputGroup>
            </div>
            <div class="col-12 col-sm-6">
                <BootstrapInputGroupLabel @bind-Value="@Model.Address" ShowRequiredMark="true" />
                <BootstrapInputGroup>
                    <Display @bind-Value="@Model.Address"></Display>
                    <BootstrapInputGroupLabel @bind-Value="@Model.Address" />
                </BootstrapInputGroup>
            </div>
        </div>
    </ValidateForm>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsCheckboxTitle"]" Introduction="@Localizer["InputGroupsCheckboxIntro"]" Name="Checkbox">
    <div class="row g-3">
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="Checkbox" />
                <Checkbox TValue="string" Color="Color.Danger" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="Checkbox" />
                <div class="row form-inline input-group-checkbox-list">
                    <div class="col-12 col-sm-4">
                        <Checkbox TValue="string" DisplayText="@Localizer["InputGroupsStatusText1"]" ShowAfterLabel="true" Color="Color.Info" />
                    </div>
                    <div class="col-12 col-sm-4">
                        <Checkbox TValue="string" DisplayText="@Localizer["InputGroupsStatusText1"]" ShowAfterLabel="true" Color="Color.Primary" />
                    </div>
                    <div class="col-12 col-sm-4">
                        <Checkbox TValue="string" DisplayText="@Localizer["InputGroupsStatusText1"]" ShowAfterLabel="true" Color="Color.Success" />
                    </div>
                </div>
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="CheckboxList" />
                <CheckboxList TValue="string" Items="Foo.GenerateHobbies(LocalizerFoo)" />
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsRadioTitle"]" Introduction="@Localizer["InputGroupsRadioIntro"]" Name="Radio">
    <div class="row g-2">
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="RadioList" />
                <RadioList TValue="string" Items="Foo.GenerateHobbies(LocalizerFoo)"></RadioList>
            </BootstrapInputGroup>
        </div>
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="RadioList" />
                <RadioList TValue="string" Items="Foo.GenerateHobbies(LocalizerFoo)" IsButton="true"></RadioList>
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsSlideButtonTitle"]" Introduction="@Localizer["InputGroupsSlideButtonIntro"]" Name="SlideButton">
    <div class="row">
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="SlideButton" />
                <SlideButton Icon="fa-solid fa-flag" ShowHeader="true" HeaderText="SlideButton Header">
                    <SlideButtonItems>
                        <SlideButtonItem Value="1" Text="Text 1"></SlideButtonItem>
                        <SlideButtonItem Value="2" Text="Text 2"></SlideButtonItem>
                        <SlideButtonItem Value="3" Text="Text 3"></SlideButtonItem>
                    </SlideButtonItems>
                </SlideButton>
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsDateTimePickerTitle"]" Introduction="@Localizer["InputGroupsDateTimePickerIntro"]" Name="DateTimePicker">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="DateTimePicker" />
                <DateTimePicker TValue="DateTime?" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="DateTimeRange" />
                <DateTimeRange />
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsDropdownTitle"]" Introduction="@Localizer["InputGroupsDropdownIntro"]" Name="Dropdown">
    <div class="row">
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="Dropdown" />
                <Dropdown TValue="string" Items="Foo.GenerateHobbies(LocalizerFoo)" Color="Color.Success" />
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["InputGroupsSwitchTitle"]" Introduction="@Localizer["InputGroupsSwitchIntro"]" Name="Switch">
    <div class="row">
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="Switch" />
                <Switch Value="@SwitchValue" />
            </BootstrapInputGroup>
        </div>
    </div>
</DemoBlock>
