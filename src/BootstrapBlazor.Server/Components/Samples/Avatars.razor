@page "/avatar"
@inject IStringLocalizer<Avatars> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["Title"]</h3>

<h4>@Localizer["SubTitle"]</h4>

<DemoBlock Title="@Localizer["BasicUsageTitle"]" Introduction="@Localizer["BasicUsageIntro"]" Name="Normal">
    <div class="d-flex flex-column">
        <p class="text-center">Circle</p>
        <div class="d-flex justify-content-between align-items-center">
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" IsCircle="true" Size="Size.ExtraExtraLarge" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" IsCircle="true" Size="Size.ExtraLarge" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" IsCircle="true" Size="Size.Large" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" IsCircle="true" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" IsCircle="true" Size="Size.Small" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" IsCircle="true" Size="Size.ExtraSmall" />
        </div>
    </div>
    <Divider Text="@Localizer["BasicUsageDivider"]" />
    <div class="d-flex flex-column">
        <p class="text-center mt-3">Square</p>
        <div class="d-flex justify-content-between align-items-center">
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo.png")" Size="Size.ExtraExtraLarge" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo.png")" Size="Size.ExtraLarge" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo.png")" Size="Size.Large" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo.png")" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo.png")" Size="Size.Small" />
            <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo.png")" Size="Size.ExtraSmall" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["IconTitle"]" Introduction="@Localizer["IconIntro"]" Name="Icon">
    <div class="d-flex justify-content-between align-items-center">
        <Avatar IsCircle="true" IsIcon="true" Icon="fa-solid fa-user" />
        <Avatar IsCircle="true" Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" />
        <Avatar IsCircle="true" IsText="true" Text="User" />
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["BorderTitle"]" Introduction="@Localizer["BorderIntro"]" Name="Border">
    <div class="d-flex justify-content-between align-items-center">
        <Avatar IsCircle="false" IsBorder="true" Url="@WebsiteOption.Value.GetAssetUrl("images/avatar2.png")" />
        <Avatar IsCircle="true" IsBorder="true" Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-c1.png")" />
        <Avatar IsCircle="true" IsBorder="true" IsIcon="true" Icon="fa-solid fa-tv" />
        <Avatar IsCircle="true" IsBorder="true" IsText="true" Text="AZ" />
    </div>
    <div class="mt-3">@((MarkupString)Localizer["BorderDiv1"].Value)</div>
    <div>@((MarkupString)Localizer["BorderDiv2"].Value)</div>
</DemoBlock>

<DemoBlock Title="@Localizer["CircletTitle"]" Introduction="@Localizer["CircleIntro"]" Name="Circle">
    <div class="d-flex justify-content-between align-items-center">
        <Avatar IsCircle="true" GetUrlAsync="@GetUrlAsync" />
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
