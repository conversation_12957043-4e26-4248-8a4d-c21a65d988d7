@page "/skeleton"
@inject IStringLocalizer<Skeletons> Localizer

<h3>@Localizer["SkeletonsTitle"]</h3>

<h4>@Localizer["SkeletonsDescription"]</h4>

<p><b>@Localizer["SkeletonsTipsTitle"]</b></p>

<ul class="ul-demo">
    <li>@Localizer["SkeletonsTips1"]</li>
    <li>@Localizer["SkeletonsTips2"]</li>
    <li>@Localizer["SkeletonsTips3"]</li>
    <li>@Localizer["SkeletonsTips4"]</li>
</ul>

<DemoBlock Title="@Localizer["SkeletonsImgTitle"]"
           Introduction="@Localizer["SkeletonsImgIntro"]"
           Name="Img">
    <p>@((MarkupString)Localizer["SkeletonsImgDescription"].Value)</p>
    <div class="row g-3 skeleton-demo">
        <div class="col-4">
            <SkeletonAvatar Round="false" />
        </div>
        <div class="col-4">
            <SkeletonAvatar />
        </div>
        <div class="col-4">
            <SkeletonAvatar Circle="true" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SkeletonsParagraphTitle"]"
           Introduction="@Localizer["SkeletonsParagraphIntro"]"
           Name="Paragraph">
    <p>@((MarkupString)Localizer["SkeletonsParagraphDescription"].Value)</p>
    <SkeletonParagraph />
</DemoBlock>

<DemoBlock Title="@Localizer["SkeletonsFormTitle"]"
           Introduction="@Localizer["SkeletonsFormIntro"]"
           Name="Form">
    <SkeletonEditor />
</DemoBlock>

<DemoBlock Title="@Localizer["SkeletonsTableTitle"]"
           Introduction="@Localizer["SkeletonsTableIntro"]"
           Name="Table">
    <SkeletonTable />
</DemoBlock>

<DemoBlock Title="@Localizer["SkeletonsTreeTitle"]"
           Introduction="@Localizer["SkeletonsTreeIntro"]"
           Name="Tree">
    <SkeletonTree />
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
