@page "/vditor"
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["VditorTitle"]</h3>

<h4>@Localizer["VditorSubTitle"]</h4>

<PackageTips Name="BootstrapBlazor.Vditor" />

<p>@((MarkupString)Localizer["MarkdownsNote"].Value)</p>

<Pre class="no-highlight">builder.Services.Configure&lt;HubOptions&gt;(option => option.MaximumReceiveMessageSize = null);</Pre>

<DemoBlock Title="@Localizer["BaseUsageTitle"]" Introduction="@Localizer["BaseUsageIntro"]" Name="Normal">
    <section ignore class="row g-3">
        <div class="col-12">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="Mode"></BootstrapInputGroupLabel>
                <Segmented Value="_mode" OnValueChanged="OnModeChanged">
                    @foreach (var item in Enum.GetValues(typeof(VditorMode)).Cast<VditorMode>())
                    {
                        <SegmentedItem Value="@item" Text="@item.ToString()" />
                    }
                </Segmented>
            </BootstrapInputGroup>
        </div>
        <div class="col-12">
            <Button Text="GetValue" OnClick="OnTriggerGetValueAsync" IsDisabled="_isDisabled"></Button>
            <Button Text="InsertValue" OnClick="OnTriggerInsertValueAsync" IsDisabled="_isDisabled"></Button>
            <Button Text="GetHtml" OnClick="OnTriggerGetHtmlAsync" IsDisabled="_isDisabled"></Button>
            <Button Text="GetSelection" OnClick="OnTriggerGetSelectionAsync" IsDisabled="_isDisabled"></Button>
            <Button Text="Enable" OnClick="OnTriggerEnableAsync" IsDisabled="!_isDisabled"></Button>
            <Button Text="Disable" OnClick="OnTriggerDisableAsync" IsDisabled="_isDisabled"></Button>
            <Button Text="Focus" OnClick="OnTriggerFocusAsync" IsDisabled="_isDisabled"></Button>
            <Button Text="Blur" OnClick="OnTriggerBlurAsync" IsDisabled="_isDisabled"></Button>
        </div>
    </section>
    <Vditor Value="@_vditorValueString" Options="_vditorOptions" @ref="_vditor"
            OnRenderedAsync="OnRenderAsync"
            OnFocusAsync="OnFocusAsync" OnBlurAsync="OnBlurAsync"
            OnEscapeAsync="OnEscapeAsync" OnCtrlEnterAsync="OnCtrlEnterAsync"
            OnSelectAsync="OnSelectAsync" OnInputAsync="OnInputAsync"></Vditor>
    <section ignore class="mt-3">
        <p>
            <textarea class="form-control" rows="6" disabled="disabled">@_vditorValueString</textarea>
        </p>
        <p>
            <textarea class="form-control" rows="6" disabled="disabled"> @_htmlString</textarea>
        </p>
        <ConsoleLogger @ref="_logger"></ConsoleLogger>
    </section>
</DemoBlock>

<AttributeTable Items="GetAttributes()" />
