@page "/image-viewer"
@inject IStringLocalizer<ImageViewers> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["ImageViewerTitle"]</h3>

<h4>@Localizer["ImageViewerDescription"]</h4>

<DemoBlock Title="@Localizer["ImageViewerNormalTitle"]"
           Introduction="@Localizer["ImageViewerNormalIntro"]"
           Name="Normal">
    <ul class="ul-demo">
        <li>@((MarkupString)Localizer["ImageViewerNormalTips1"].Value)</li>
        <li>@((MarkupString)Localizer["ImageViewerNormalTips2"].Value)</li>
        <li>@((MarkupString)Localizer["ImageViewerNormalTips3"].Value)</li>
        <li>@((MarkupString)Localizer["ImageViewerNormalTips4"].Value)</li>
        <li>@((MarkupString)Localizer["ImageViewerNormalTips5"].Value)</li>
    </ul>
    <div class="images mt-3">
        <div class="images-item">
            <div>Fill</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/bird.jpeg")" FitMode="ObjectFitMode.Fill" />
        </div>
        <div class="images-item">
            <div>Contain</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/bird.jpeg")" FitMode="ObjectFitMode.Contain" />
        </div>
        <div class="images-item">
            <div>Cover</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/bird.jpeg")" FitMode="ObjectFitMode.Cover" />
        </div>
        <div class="images-item">
            <div>None</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/bird.jpeg")" FitMode="ObjectFitMode.None" />
        </div>
        <div class="images-item">
            <div>ScaleDown</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/bird.jpeg")" FitMode="ObjectFitMode.ScaleDown" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ImageViewerPlaceHolderTitle"]"
           Introduction="@Localizer["ImageViewerPlaceHolderIntro"]"
           Name="PlaceHolder">
    <div>@((MarkupString)Localizer["ImageViewerPlaceHolderTips1"].Value)</div>
    <div class="images img-ph mt-3">
        <div class="images-item">
            <div>@Localizer["ImageViewerPlaceHolderDefault"]</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/image-ph.jpeg")" ShowPlaceHolder="true" />
        </div>
        <div class="images-item">
            <div>@Localizer["ImageViewerPlaceHolderCustom"]</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/image-ph.jpeg")" ShowPlaceHolder="true">
                <PlaceHolderTemplate>
                    <div class="bb-img-holder">
                        <div class="bb-img-loading">@Localizer["ImageViewerPlaceHolderLoading"]</div>
                    </div>
                </PlaceHolderTemplate>
            </ImageViewer>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ImageViewerPlaceHolderTemplateTitle"]"
           Introduction="@Localizer["ImageViewerPlaceHolderTemplateIntro"]"
           Name="PlaceHolderTemplate">
    <div>@((MarkupString)Localizer["ImageViewerPlaceHolderTemplateTips1"].Value)</div>
    <div class="images img-ph mt-3">
        <div class="images-item">
            <div>@Localizer["ImageViewerPlaceHolderTemplateUrl"]</div>
            <ImageViewer Url="">
                <PlaceHolderTemplate>
                    <div class="bb-img-holder">
                        <div class="bb-img-loading">@Localizer["ImageViewerPlaceHolderTemplatePlaceholder"]</div>
                    </div>
                </PlaceHolderTemplate>
            </ImageViewer>
        </div>
        <div class="images-item">
            <div>@Localizer["ImageViewerPlaceHolderTemplateLoadingShow"]</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/image-ph.jpeg")" ShowPlaceHolder="true">
                <PlaceHolderTemplate>
                    <div class="bb-img-holder">
                        <div class="bb-img-loading">@Localizer["ImageViewerPlaceHolderTemplatePlaceholder"]</div>
                    </div>
                </PlaceHolderTemplate>
            </ImageViewer>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ImageViewerErrorTemplateTitle"]"
           Introduction="@Localizer["ImageViewerErrorTemplateIntro"]"
           Name="ErrorTemplate">
    <div class="images img-ph mt-3">
        <div class="images-item">
            <div>@Localizer["ImageViewerErrorTemplateUrlError"]</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/error-image.jpeg")" HandleError="true" />
        </div>
        <div class="images-item">
            <div>@Localizer["ImageViewerErrorTemplateCustom"]</div>
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/error-image.jpeg")">
                <ErrorTemplate>
                    <div class="bb-img-holder">
                        <div class="bb-img-loading">@Localizer["ImageViewerErrorTemplateLoadFailed"]</div>
                    </div>
                </ErrorTemplate>
            </ImageViewer>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ImageViewerPreviewListTitle"]"
           Introduction="@Localizer["ImageViewerPreviewListIntro"]"
           Name="PreviewList">
    <div class="images img-ph mt-3">
        <div class="images-item">
            <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/bird.jpeg")" PreviewList="PreviewList" ZoomSpeed="0.5" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ImagePreviewerTitle"]"
           Introduction="@Localizer["ImagePreviewerIntro"]"
           Name="Show">
    <Button OnClick="ShowImagePreviewer" Text="@Localizer["ImagePreviewerButton"]"></Button>
    <ImagePreviewer @ref="ImagePreviewer" PreviewList="PreviewList"></ImagePreviewer>
</DemoBlock>

<DemoBlock Title="@Localizer["IntersectionObserverTitle"]"
           Introduction="@Localizer["IntersectionObserverIntro"]"
           Name="IsIntersectionObserver">
    <ImageViewer Url="@WebsiteOption.Value.GetAssetUrl("images/tutorials/waterfall.png")" IsIntersectionObserver="true" IsAsync="true" style="height: 400px;"></ImageViewer>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
