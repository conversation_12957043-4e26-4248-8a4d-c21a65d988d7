@page "/code-editors"
@inject IStringLocalizer<CodeEditors> Localizer

<h3>@Localizer["Title"]</h3>

<PackageTips Name="BootstrapBlazor.CodeEditor"></PackageTips>

<DemoBlock Title="@Localizer["BasicTitle"]" Introduction="@Localizer["BasicIntro"]">
    <div class="row form-inline g-3">
        <div class="col-12 col-sm-6">
            <Select @bind-Value="_language" OnSelectedItemChanged="OnSelectedItemChanged" ShowLabel="true" DisplayText="Language">
                <Options>
                    <SelectOption Text="JavaScript" Value="javascript"></SelectOption>
                    <SelectOption Text="CSharp" Value="csharp"></SelectOption>
                    <SelectOption Text="Razor" Value="razor"></SelectOption>
                    <SelectOption Text="Json" Value="json"></SelectOption>
                </Options>
            </Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select @bind-Value="@_theme" ShowLabel="true" DisplayText="Theme">
                <Options>
                    <SelectOption Text="Visual Studio" Value="vs"></SelectOption>
                    <SelectOption Text="Visual Studio Dark" Value="vs-dark"></SelectOption>
                    <SelectOption Text="High Contrast Dark" Value="hc-black"></SelectOption>
                </Options>
            </Select>
        </div>
        <div class="col-12">
            <Pre>&lt;CodeEditor Value="@@_code" Language="@@_language" Theme="@@_theme" /&gt;</Pre>
        </div>
        <div class="col-12">
            <div style="border: 1px solid var(--bs-border-color); height: 200px; width: 100%; overflow: hidden;">
                <CodeEditor ShowLineNo="true" Value="@_code" Language="@_language" Theme="@_theme"></CodeEditor>
            </div>
        </div>
    </div>
</DemoBlock>

<AttributeTable Items="GetAttributeItems()" />
