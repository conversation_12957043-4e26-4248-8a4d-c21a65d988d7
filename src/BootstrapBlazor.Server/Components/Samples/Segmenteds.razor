@page "/segmented"
@inject IStringLocalizer<Segmenteds> Localizer

<h3>@Localizer["Title"]</h3>

<h4>@Localizer["SubTitle"]</h4>

<DemoBlock Title="@Localizer["BasicTitle"]" Introduction="@Localizer["BasicIntro"]" Name="Normal">
    <Segmented Items="@Items" Value="@Value" OnValueChanged="OnValueChanged">
    </Segmented>
    <ConsoleLogger @ref="Logger" />
</DemoBlock>

<DemoBlock Title="@Localizer["IsDisabledTitle"]" Introduction="@Localizer["IsDisabledIntro"]" Name="IsDisabled">
    <Segmented Items="@DisabledItems"></Segmented>
</DemoBlock>

<DemoBlock Title="@Localizer["BlockTitle"]" Introduction="@Localizer["BlockIntro"]" Name="IsBlock">
    <Segmented TValue="int" IsBlock="true" ShowTooltip="true">
        <SegmentedItem Text="Daily" Value="12" />
        <SegmentedItem Text="Weekly" Value="34" IsDisabled="true" />
        <SegmentedItem Text="Monthly" Value="56" IsActive="true" />
        <SegmentedItem Text="Quarterly" Value="78" IsDisabled="true" />
        <SegmentedItem Text="Yearly" Value="90" />
        <SegmentedItem Text="long-text1-long-text-long-text-long-text" Value="11" />
        <SegmentedItem Text="long-text2-long-text-long-text-long-text" Value="22" />
    </Segmented>
</DemoBlock>

<DemoBlock Title="@Localizer["ItemTemplateTitle"]" Introduction="@Localizer["ItemTemplateIntro"]" Name="ItemTemplate">
    <Segmented Items="@ItemTemplateItems">
        <ItemTemplate>
            <div class="d-flex flex-column">
                <span class="segmented-item-icon">
                    <i class="@context.Icon"></i>
                </span>
                <span>
                    @context.Text
                </span>
            </div>
        </ItemTemplate>
    </Segmented>
</DemoBlock>

<DemoBlock Title="@Localizer["IconTitle"]" Introduction="@Localizer["IconIntro"]" Name="Icon">
    <Segmented Items="@IconItems"></Segmented>
</DemoBlock>

<DemoBlock Title="@Localizer["SizeTitle"]" Introduction="@Localizer["SizeIntro"]" Name="Size">
    <div class="row g-3">
        <div class="col-12">
            <Segmented Items="@SizeItems" Size="Size.Large"></Segmented>
        </div>
        <div class="col-12">
            <Segmented Items="@SizeItems"></Segmented>
        </div>
        <div class="col-12">
            <Segmented Items="@SizeItems" Size="Size.Small"></Segmented>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SegmentItemTitle"]" Introduction="@Localizer["SegmentItemIntro"]" Name="SegmentItem">
    <Segmented TValue="int">
        <SegmentedItem Text="Daily" Value="12" />
        <SegmentedItem Text="Weekly" Value="34" />
        <SegmentedItem Text="Monthly" Value="56" IsActive="true" />
        <SegmentedItem Text="Quarterly" Value="78" />
        <SegmentedItem Text="Yearly" Value="90" />
    </Segmented>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
