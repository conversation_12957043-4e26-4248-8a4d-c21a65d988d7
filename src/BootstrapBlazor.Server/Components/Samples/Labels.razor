@page "/label"
@layout ComponentLayout
@inject IStringLocalizer<Labels> Localizer
@inject IStringLocalizer<Foo> LocalizerFoo
@inject IStringLocalizer<ValidateForms> LocalizerForm

<h3>@Localizer["LabelsTitle"]</h3>

<p>@((MarkupString)Localizer["LabelsDescription"].Value)</p>

<ul class="ul-demo">
    <li>@((MarkupString)Localizer["LabelsDescriptionTips1"].Value)</li>
    <li>@((MarkupString)Localizer["LabelsDescriptionTips2"].Value)</li>
</ul>

<p>@((MarkupString)Localizer["LabelsDescriptionTips3"].Value)</p>

<Tips>
    <p>@((MarkupString)Localizer["LabelsTips"].Value)</p>
</Tips>

<DemoBlock Title="@Localizer["LabelsNormalTitle"]" Introduction="@Localizer["LabelsNormalIntro"]" Name="Normal">
    <p><b>@Localizer["LabelsNormalDescription"]</b></p>
    <ul class="ul-demo mb-3">
        <li>@((MarkupString)Localizer["LabelsNormalTips1"].Value)</li>
        <li>@((MarkupString)Localizer["LabelsNormalTips2"].Value)</li>
        <li>@((MarkupString)Localizer["LabelsNormalTips3"].Value)</li>
        <li>@((MarkupString)Localizer["LabelsNormalTips4"].Value)</li>
    </ul>

    <GroupBox Title="@Localizer["LabelsNormalGroupBox1Title"]" style="margin-top: 1.5rem;">
        <div>@Localizer["LabelsNormalGroupBox1Tips1"]</div>
        <div>@((MarkupString)Localizer["LabelsNormalGroupBox1Tips2"].Value)</div>
        <div>@((MarkupString)Localizer["LabelsNormalGroupBox1Tips3"].Value)</div>
        <div>@((MarkupString)Localizer["LabelsNormalGroupBox1Tips4"].Value)</div>
        <div class="row g-3 mt-3">
            <div class="col-sm-12 col-md-3">
                <BootstrapInput TValue="string" />
            </div>
            <div class="col-sm-12 col-md-3">
                <BootstrapInput TValue="string" ShowLabel="true" DisplayText="" />
            </div>
            <div class="col-sm-12 col-md-3">
                <BootstrapInput TValue="string" ShowLabel="true" DisplayText="@LocalizerFoo[nameof(Foo.Name)]" />
            </div>
            <div class="col-sm-12 col-md-3">
                <BootstrapInput TValue="string" ShowLabel="true" DisplayText="@null" />
            </div>
        </div>
    </GroupBox>

    <p class="mt-3"><b>@Localizer["LabelsNormalDescription2"]</b></p>
    <GroupBox Title="@Localizer["LabelsNormalGroupBox2Title"]" class="mt-3">
        <div>@((MarkupString)Localizer["LabelsNormalGroupBox2Tips1"].Value)</div>
        <div>@((MarkupString)Localizer["LabelsNormalGroupBox2Tips2"].Value)</div>
        <div>@((MarkupString)Localizer["LabelsNormalGroupBox2Tips3"].Value)</div>
        <div>@((MarkupString)Localizer["LabelsNormalGroupBox2Tips4"].Value)</div>
        <div class="row g-3 mt-3">
            <div class="col-sm-12 col-md-3">
                <BootstrapInput @bind-Value="Dummy1.Name" />
            </div>
            <div class="col-sm-12 col-md-3">
                <BootstrapInput @bind-Value="Dummy1.Name" ShowLabel="true" DisplayText="@LocalizerFoo[nameof(Foo.Address)]" />
            </div>
            <div class="col-sm-12 col-md-3">
                <BootstrapInput @bind-Value="Dummy1.Name" ShowLabel="true" DisplayText="" />
            </div>
            <div class="col-sm-12 col-md-3">
                <BootstrapInput @bind-Value="Dummy1.Name" ShowLabel="true" DisplayText="@null" />
            </div>
        </div>
    </GroupBox>
</DemoBlock>

<DemoBlock Title="@Localizer["LabelsEditorFormTitle"]" Introduction="@Localizer["LabelsEditorFormIntro"]" Name="EditorForm">
    <p>@((MarkupString)Localizer["LabelsEditorFormDescription1"].Value)</p>
    <GroupBox>
        <EditorForm Model="@Dummy2" ShowLabel="false" RowType="RowType.Inline" ItemsPerRow="2">
            <FieldItems>
                <EditorItem @bind-Field="context.Hobby" Items="@Foo.GenerateHobbies(LocalizerFoo)">
                </EditorItem>
            </FieldItems>
        </EditorForm>
    </GroupBox>

    <p class="mt-3">@((MarkupString)Localizer["LabelsEditorFormDescription2"].Value)</p>
    <GroupBox>
        <EditorForm Model="@Dummy2" RowType="RowType.Inline" ItemsPerRow="2">
            <FieldItems>
                <EditorItem @bind-Field="context.Hobby" Items="@Foo.GenerateHobbies(LocalizerFoo)">
                </EditorItem>
            </FieldItems>
        </EditorForm>
    </GroupBox>

    <p class="mt-3">@((MarkupString)Localizer["EditorFormLabelAlignRight"].Value)</p>
    <GroupBox>
        <EditorForm Model="@Dummy2" ShowLabel="true" RowType="RowType.Inline" ItemsPerRow="2" LabelAlign="Alignment.Right">
            <FieldItems>
                <EditorItem @bind-Field="context.Hobby" Items="@Foo.GenerateHobbies(LocalizerFoo)">
                </EditorItem>
            </FieldItems>
        </EditorForm>
    </GroupBox>

    <p class="mt-3">@((MarkupString)Localizer["EditorFormLabelAlignCenter"].Value)</p>
    <GroupBox>
        <EditorForm Model="@Dummy2" ShowLabel="true" RowType="RowType.Inline" ItemsPerRow="2" LabelAlign="Alignment.Center">
            <FieldItems>
                <EditorItem @bind-Field="context.Hobby" Items="@Foo.GenerateHobbies(LocalizerFoo)">
                </EditorItem>
            </FieldItems>
        </EditorForm>
    </GroupBox>
</DemoBlock>

<DemoBlock Title="@Localizer["LabelsValidateForm1Title"]" Introduction="@Localizer["LabelsValidateForm1Intro"]" Name="ValidateForm">
    <p>@((MarkupString)Localizer["LabelsValidateForm1Description1"].Value)</p>

    <GroupBox>
        <ValidateForm Model="@Dummy3">
            <EditorForm TModel="Foo" RowType="RowType.Inline" ItemsPerRow="2">
                <FieldItems>
                    <EditorItem @bind-Field="context.Hobby" Items="@Foo.GenerateHobbies(LocalizerFoo)">
                    </EditorItem>
                </FieldItems>
            </EditorForm>
        </ValidateForm>
    </GroupBox>

    <p class="mt-3">@((MarkupString)Localizer["LabelsValidateForm1Description2"].Value)</p>
    <GroupBox>
        <ValidateForm Model="@Dummy3" ShowLabel="false">
            <EditorForm TModel="Foo" RowType="RowType.Inline" ItemsPerRow="2">
                <FieldItems>
                    <EditorItem @bind-Field="context.Hobby" Items="@Foo.GenerateHobbies(LocalizerFoo)">
                    </EditorItem>
                </FieldItems>
            </EditorForm>
        </ValidateForm>
    </GroupBox>
</DemoBlock>

<DemoBlock Title="@Localizer["LabelsValidateForm2Title"]" Introduction="@Localizer["LabelsValidateForm2Intro"]" Name="ShowLabel">
    <p>@((MarkupString)Localizer["LabelsValidateForm2Description1"].Value)</p>
    <ValidateForm Model="@Dummy4">
        <div class="row g-3">
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Name" />
            </div>
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Address" />
            </div>
        </div>
    </ValidateForm>

    <p class="mt-3">@((MarkupString)Localizer["LabelsValidateForm2Description2"].Value)</p>
    <ValidateForm Model="@Dummy4" ShowLabel="false">
        <div class="row g-3">
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Name" />
            </div>
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Address" />
            </div>
        </div>
    </ValidateForm>

    <p class="mt-3">@((MarkupString)Localizer["LabelsValidateForm2Description3"].Value)</p>
    <ValidateForm Model="@Dummy4">
        <div class="row g-3 form-inline">
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Name" />
            </div>
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Address" />
            </div>
        </div>
    </ValidateForm>

    <p class="mt-3">@((MarkupString)Localizer["ValidateFormAlignRight"].Value)</p>
    <ValidateForm Model="@Dummy4">
        <div class="row g-3 form-inline form-inline-end">
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Name" />
            </div>
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Address" />
            </div>
        </div>
    </ValidateForm>

    <p class="mt-3">@((MarkupString)Localizer["ValidateFormAlignCenter"].Value)</p>
    <ValidateForm Model="@Dummy4">
        <div class="row g-3 form-inline form-inline-center">
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Name" />
            </div>
            <div class="col-sm-12 col-md-6">
                <BootstrapInput @bind-Value="Dummy4.Address" />
            </div>
        </div>
    </ValidateForm>
</DemoBlock>

<DemoBlock Title="@Localizer["LabelsRowLabelWidthTitle"]" Introduction="@Localizer["LabelsRowLabelWidthIntro"]" Name="RowLabelWidth">
    <section ignore>
        <p>@((MarkupString)LocalizerForm["ValidateFormNormalFormLabelWidth"].Value)</p>
        <p>@((MarkupString)LocalizerForm["LongDisplayDescription"].Value)</p>
        <Pre>:root {
    --bb-row-label-width: 120px;
}</Pre>
    </section>

    <ValidateForm Model="@Dummy4">
        <div class="row g-3">
            <div class="col-12">
                <BootstrapInput @bind-Value="@Dummy4.Name" DisplayText="@LocalizerForm["LongDisplayText"]" />
            </div>
        </div>
        <div class="row g-3 form-inline mt-0">
            <div class="col-12">
                <BootstrapInput @bind-Value="@Dummy4.Address" DisplayText="@LocalizerForm["LongDisplayText"]" ShowLabelTooltip="true" />
            </div>
        </div>
    </ValidateForm>
</DemoBlock>

<DemoBlock Title="@Localizer["LabelsWidthTitle"]" Introduction="@Localizer["LabelsWidthIntro"]" Name="LabelWidth">
    <section ignore>
        <p>@((MarkupString)Localizer["LabelsWidthDescription"].Value)</p>
        <Pre>&lt;ValidateForm Model="@@Dummy4" LabelWidth="100"&gt;
    &lt;EditorForm TModel="Foo" ItemsPerRow="2" RowType="RowType.Inline" LabelAlign="Alignment.Right" LabelWidth="120"
                AutoGenerateAllItem="false"&gt;
        &lt;FieldItems&gt;
            &lt;EditorItem @@bind-Field="@@Dummy4.Name"&gt;&lt;/EditorItem&gt;
        &lt;/FieldItems&gt;
    &lt;/EditorForm&gt;

    &lt;BootstrapLabelSetting LabelWidth="220"&gt;
        &lt;div class="row form-inline g-3 mt-0"&gt;
            &lt;div class="col-12 col-sm-6"&gt;
                &lt;BootstrapLabel LabelWidth="180" Value="@@LocalizerForm["LongDisplayText"]"&gt;&lt;/BootstrapLabel&gt;
                &lt;BootstrapInput @@bind-Value="Dummy4.Name" ShowLabel="false" /&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/BootstrapLabelSetting&gt;
&lt;/ValidateForm&gt;</Pre>
        <p>
            @((MarkupString)Localizer["LabelsWidthCode1"].Value)
        </p>
        <ul class="ul-demo">
            <li><code>ValidateForm</code> <b>100</b></li>
            <li><code>EditorForm</code> <b>120</b></li>
            <li><code>BootstrapLabelSetting</code> <b>220</b></li>
            <li><code>BootstrapLabel</code> <b>180</b></li>
        </ul>
    </section>
    <ValidateForm Model="@Dummy4" LabelWidth="100">
        <EditorForm TModel="Foo" ItemsPerRow="2" RowType="RowType.Inline" LabelAlign="Alignment.Right" LabelWidth="120"
                    AutoGenerateAllItem="false">
            <FieldItems>
                <EditorItem @bind-Field="@Dummy4.Name"></EditorItem>
            </FieldItems>
        </EditorForm>

        <BootstrapLabelSetting LabelWidth="220">
            <div class="row form-inline g-3 mt-0">
                <div class="col-12 col-sm-6">
                    <BootstrapLabel LabelWidth="180" Value="@LocalizerForm["LongDisplayText"]"></BootstrapLabel>
                    <BootstrapInput @bind-Value="Dummy4.Name" ShowLabel="false" />
                </div>
            </div>
        </BootstrapLabelSetting>
    </ValidateForm>
</DemoBlock>
