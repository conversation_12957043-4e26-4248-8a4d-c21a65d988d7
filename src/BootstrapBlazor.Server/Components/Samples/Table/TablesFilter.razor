@page "/table/filter"
@inject IStringLocalizer<NavMenu> NavMenuLocalizer
@inject IStringLocalizer<Tables> BaseLocalizer
@inject IStringLocalizer<TablesFilter> Localizer
@inject IStringLocalizer<Foo> FooLocalizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@BaseLocalizer["TableBaseTitle"] - @NavMenuLocalizer["TableFilter"]</h3>

<h4>@Localizer["TablesFilterDesc"]</h4>

<ul class="ul-demo mt-3">
    <li>@((MarkupString)Localizer["TablesFilterDescLi1"].Value)</li>
    <li>@((MarkupString)Localizer["TablesFilterDescLi2"].Value)</li>
</ul>

<DemoBlock Title="@Localizer["FilterableTitle"]"
           Introduction="@Localizer["FilterableIntro"]"
           Name="Filterable">
    <section ignore>
        <p>@((MarkupString)Localizer["FilterableP"].Value)</p>
        <ul class="ul-demo mb-3">
            <li>@((MarkupString)Localizer["FilterableLi1"].Value)</li>
            <li>@((MarkupString)Localizer["FilterableLi2"].Value)</li>
            <li>@((MarkupString)Localizer["FilterableLi3"].Value)</li>
            <li>@((MarkupString)Localizer["FilterableLi4"].Value)</li>
        </ul>
        <div>@((MarkupString)Localizer["FilterableDiv"].Value)</div>
        <Alert ShowBar="true" ShowBorder="true" Color="Color.Info" class="mt-3">@((MarkupString)Localizer["FilterableAlert"].Value)</Alert>
    </section>

    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true" @bind-SelectedRows="SelectedItems" IsKeepSelectedRows="true"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" Filterable="true" DefaultSort="true" DefaultSortOrder="SortOrder.Desc" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Complete" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Education" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Count" Width="100" Sortable="true" Filterable="true" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["FilterTemplateTitle"]"
           Introduction="@Localizer["FilterTemplateIntro"]" Name="CustomFilter">
    <section ignore>@((MarkupString)Localizer["TablesFilterTemplateDescription", ComponentSourceCodeUrl].Value)</section>

    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Complete" Width="100" Sortable="true" Filterable="true">
                <Template Context="value">
                    <Checkbox Value="@value.Value" IsDisabled="true"></Checkbox>
                </Template>
            </TableColumn>
            <TableColumn @bind-Field="@context.Count" Width="100" Sortable="true" Filterable="true">
                <FilterTemplate>
                    <FilterProvider ShowMoreButton="false">
                        <CustomerFilter></CustomerFilter>
                    </FilterProvider>
                </FilterTemplate>
            </TableColumn>
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["ShowFilterHeaderTitle"]"
           Introduction="@Localizer["ShowFilterHeaderIntro"]"
           Name="ShowFilterHeader">
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true" ShowFilterHeader="true"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Complete" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Education" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Count" Width="100" Sortable="true" Filterable="true" DefaultSort="true" DefaultSortOrder="@SortOrder.Desc" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["DefaultSortTitle"]"
           Introduction="@Localizer["DefaultSortIntro"]"
           Name="DefaultSort">
    <section ignore>
        <ul class="ul-demo mb-3">
            <li>@((MarkupString)Localizer["DefaultSortLi1"].Value)</li>
            <li>@((MarkupString)Localizer["DefaultSortLi2"].Value)</li>
        </ul>
        <p>@Localizer["DefaultSortP"]</p>
    </section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" />
            <TableColumn @bind-Field="@context.Count" Width="100" Sortable="true" DefaultSort="true" DefaultSortOrder="@SortOrder.Desc" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["SortListTitle"]"
           Introduction="@Localizer["SortListIntro"]"
           Name="SortString">
    <section ignore>@((MarkupString)Localizer["SortListP"].Value)</section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true" SortString="SortList"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" />
            <TableColumn @bind-Field="@context.Count" Width="100" Sortable="true" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["OnSortTitle"]"
           Introduction="@Localizer["OnSortIntro"]"
           Name="OnSort">
    <section ignore>@((MarkupString)Localizer["OnSortP"].Value)</section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true" SortString="@SortString" OnSort="OnSort"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" />
            <TableColumn @bind-Field="@context.Count" Width="100" Sortable="true" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["OnAdvancedSortTitle"]"
           Introduction="@Localizer["OnAdvancedSortIntro"]"
           Name="ShowAdvancedSort">
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true" ShowToolbar="true" ShowAdvancedSort="true"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" />
            <TableColumn @bind-Field="@context.Count" Width="100" Sortable="true" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["SetFilterInCodeTitle"]"
           Introduction="@Localizer["SetFilterInCodeIntro"]"
           Name="SetFilterInCode">
    <section ignore>
        <Button Text="@Localizer["SetFilterInCodeButtonText1"]" OnClickWithoutRender="SetFilterInCode"></Button>
        <Button Text="@Localizer["SetFilterInCodeButtonText2"]" OnClickWithoutRender="ResetAllFilters"></Button>
    </section>

    <Table TItem="Foo" @ref="TableSetFilter"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true" ShowFilterHeader="true"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Address" Sortable="true" />
            <TableColumn @bind-Field="@context.Complete" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Education" Width="100" Sortable="true" Filterable="true" />
            <TableColumn @bind-Field="@context.Count" Width="150" Sortable="true" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["MultiFilterTitle"]"
           Introduction="@Localizer["MultiFilterIntro"]"
           Name="MultiFilter">
    <section ignore>
        <p>@((MarkupString)Localizer["MultiFilterTips"].Value)</p>
        <ul class="ul-demo">
            <li>@((MarkupString)Localizer["MultiFilterTipsLi1"].Value)</li>
            <li>@((MarkupString)Localizer["MultiFilterTipsLi2"].Value)</li>
        </ul>
    </section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowSkeleton="true"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" Sortable="true" />
            <TableColumn @bind-Field="@context.Name" Width="100" Sortable="true" Filterable="true">
                <FilterTemplate>
                    <FilterProvider ShowMoreButton="false">
                        <MultiFilter Items="Items.Select(i => new SelectedItem(i.Name!, i.Name!)).DistinctBy(i => i.Value).ToList()"></MultiFilter>
                    </FilterProvider>
                </FilterTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Address" Sortable="true" Filterable="true">
                <FilterTemplate>
                    <FilterProvider ShowMoreButton="false">
                        <MultiFilter OnGetItemsAsync="OnGetAddressItemsAsync"></MultiFilter>
                    </FilterProvider>
                </FilterTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Complete" Width="100" Sortable="true" Filterable="true">
                <FilterTemplate>
                    <FilterProvider ShowMoreButton="false">
                        <MultiFilter ShowSearch="false" Items="Items.Select(i => new SelectedItem(i.Complete.ToString()!, i.Complete.ToString()!)).DistinctBy(i => i.Value).ToList()"></MultiFilter>
                    </FilterProvider>
                </FilterTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Education" Width="100" Sortable="true" Filterable="true">
                <FilterTemplate>
                    <FilterProvider ShowMoreButton="false">
                        <MultiFilter ShowSearch="false" Items="Items.Select(i => new SelectedItem(i.Education.ToString()!, i.Education.ToString()!)).DistinctBy(i => i.Value).ToList()"></MultiFilter>
                    </FilterProvider>
                </FilterTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Count" Width="150" Sortable="true" />
        </TableColumns>
    </Table>
</DemoBlock>
