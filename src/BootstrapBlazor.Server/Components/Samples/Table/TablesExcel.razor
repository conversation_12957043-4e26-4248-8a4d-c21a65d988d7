@page "/table/excel"
@inject IStringLocalizer<NavMenu> NavMenuLocalizer
@inject IStringLocalizer<TablesExcel> Localizer
@inject IStringLocalizer<Foo> LocalizerFoo
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["TablesExcelTitle"] - @NavMenuLocalizer["TableExcel"]</h3>
<h4>@Localizer["TablesExcelDescription"]</h4>

<DemoBlock Title="@Localizer["TablesExcelOnQueryTitle"]"
           Introduction="@Localizer["TablesExcelOnQueryIntro"]"
           Name="OnQuery">
    <Table TItem="Foo" ShowToolbar="true" ShowRefresh="true" ShowColumnList="true" ShowExtendButtons="true"
           IsExcel="true" IsPagination="true" PageItemsSource="@PageItemsSource"
           OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" Readonly="true" />
            <TableColumn @bind-Field="@context.Education" />
            <TableColumn @bind-Field="@context.Count" Align="Alignment.Right" />
            <TableColumn @bind-Field="@context.Complete" Align="Alignment.Center" />
        </TableColumns>
    </Table>

    <section ignore>
        <ConsoleLogger @ref="Logger" />
    </section>
</DemoBlock>

<p class="mt-3">@((MarkupString)Localizer["TablesExcelTips"].Value)</p>

<p>
    <div class="code-label">@((MarkupString)Localizer["TablesExcelSetDataSourceTitle"].Value)</div>
    <div class="mt-2">@((MarkupString)Localizer["TablesExcelSetDataSourceDescription"].Value)</div>
</p>

<Pre>protected override void OnInitialized()
{
    base.OnInitialized();

    Items = Foo.GenerateFoo(Localizer);
}</Pre>

<p>
    <div class="code-label">@((MarkupString)Localizer["TablesExcelNewLogicTitle"].Value)</div>
    <div class="mt-2">@((MarkupString)Localizer["TablesExcelNewLogicDescription"].Value)</div>
</p>

<Pre>private Task&lt;Foo&gt; OnAddAsync()
{
    // @Localizer["TablesExcelNewLogicNote1"]
    var foo = new Foo() { DateTime = DateTime.Now, Address = $"@Localizer["TablesExcelNewLogicNote1Address"] {DateTime.Now.Second}" };
    Items.Insert(0, foo);

    // @Localizer["TablesExcelNewLogicNote2"]
    Logger.Log($"@Localizer["TablesExcelNewLogicNote2Log1"]: {Items.Count} - @Localizer["TablesExcelNewLogicNote2Log2"]: Add");
    return Task.FromResult(foo);
}</Pre>

<p>
    <div class="code-label">@((MarkupString)Localizer["TablesExcelDeleteLogicTitle"].Value)</div>
    <div class="mt-2">@((MarkupString)Localizer["TablesExcelDeleteLogicDescription"].Value)</div>
</p>

<Pre>private Task&lt;bool&gt; OnDeleteAsync(IEnumerable&lt;Foo&gt; items)
{
    // @Localizer["TablesExcelDeleteLogicNote1"]
    Items.RemoveAll(i => items.Contains(i));

    // @Localizer["TablesExcelDeleteLogicNote2"]
    Logger.Log($"@Localizer["TablesExcelDeleteLogicNote2Log1"]: {Items.Count} - @Localizer["TablesExcelDeleteLogicNote2Log2"]: Delete");
    return Task.FromResult(true);
}</Pre>

<p>
    <div class="code-label">@((MarkupString)Localizer["TablesExcelUpdateLogicTitle"].Value)</div>
    <div class="mt-2">@((MarkupString)Localizer["TablesExcelUpdateLogicDescription1"].Value)</div>
    <div class="mt-2">@((MarkupString)Localizer["TablesExcelUpdateLogicDescription2"].Value)</div>
</p>

<Pre>private Task&lt;bool&gt; OnSaveAsync(Foo item, ItemChangedType changedType)
{
    // @Localizer["TablesExcelUpdateLogicNote"]
    Logger.Log($"@Localizer["TablesExcelUpdateLogicLog1"] - @Localizer["TablesExcelUpdateLogicLog2"]");
    return Task.FromResult(true);
}</Pre>

<DemoBlock Title="@Localizer["TablesExcelCellRenderTitle"]"
           Introduction="@Localizer["TablesExcelCellRenderIntro"]"
           Name="CellRender">
    <section ignore>
        <ul class="ul-demo mb-3">
            <li>@((MarkupString)Localizer["TablesExcelCellRenderTips1"].Value)</li>
            <li>@((MarkupString)Localizer["TablesExcelCellRenderTips2"].Value)</li>
        </ul>
    </section>
    <Table TItem="Foo" Items="@Items" IsBordered="true" IsExcel="true" IsFixedHeader="true" Height="500">
        <TableColumns>
            <TableColumn @bind-Field="@context.Name" Width="200">
                <EditTemplate Context="row">
                    <div class="d-flex disabled">
                        <div>
                            <img src="@WebsiteOption.Value.GetAvatarUrl(row!.Id)" class="bb-avatar" />
                        </div>
                        <div class="ps-2">
                            <div>@row.Name</div>
                            <div class="bb-sub">@Foo.GetTitle(row!.Id)</div>
                        </div>
                    </div>
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Address" />
            <TableColumn @bind-Field="@context.Education" Width="160" />
            <TableColumn @bind-Field="@context.Count" Width="160">
                <EditTemplate Context="row">
                    <div class="disabled">
                        <div>@row!.Count %</div>
                        <BootstrapBlazor.Components.Progress Value="@row.Count" Color="@Foo.GetProgressColor(row.Count)"></BootstrapBlazor.Components.Progress>
                    </div>
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Complete" Align="Alignment.Center" Width="80" />
        </TableColumns>
    </Table>
</DemoBlock>
