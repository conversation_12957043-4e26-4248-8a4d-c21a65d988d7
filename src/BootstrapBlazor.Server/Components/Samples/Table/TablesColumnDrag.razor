@page "/table/column/drag"
@inject IStringLocalizer<NavMenu> NavMenuLocalizer
@inject IStringLocalizer<TablesColumnDrag> Localizer
@inject IStringLocalizer<Foo> FooLocalizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["TablesColumnTitle"] - @NavMenuLocalizer["TableColumnDrag"]</h3>

<h4>@Localizer["TablesColumnDescription"]</h4>

<DemoBlock Title="@Localizer["AllowDragOrderTitle"]" Introduction="@Localizer["AllowDragOrderIntro"]" Name="AllowDragColumn">
    <section ignore>@((MarkupString)Localizer["AllowDragOrderDesc"].Value)</section>
    <Table TItem="Foo" ClientTableName="table-test"
           IsPagination="true" PageItemsSource="@PageItemsSource" AllowDragColumn="true"
           IsStriped="true" IsBordered="true" OnDragColumnEndAsync="OnDragColumnEndAsync"
           ShowToolbar="false" IsMultipleSelect="true" ShowExtendButtons="false"
           OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="120" FormatString="yyyy-MM-dd" />
            <TableColumn @bind-Field="@context.Name" Width="100" />
            <TableColumn @bind-Field="@context.Address" />
            <TableColumn @bind-Field="@context.Count" />
        </TableColumns>
    </Table>
    <ConsoleLogger @ref="Logger" />
</DemoBlock>
