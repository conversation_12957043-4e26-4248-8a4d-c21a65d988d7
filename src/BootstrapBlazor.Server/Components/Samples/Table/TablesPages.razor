@page "/table/page"
@inject IStringLocalizer<NavMenu> NavMenuLocalizer
@inject IStringLocalizer<TablesPages> Localizer
@inject IStringLocalizer<Foo> LocalizerFoo

<h3>@Localizer["TablesPagesTitle"] - @NavMenuLocalizer["TablePage"]</h3>
<h4>@Localizer["TablesPagesDescription"]</h4>

<DemoBlock Title="@Localizer["TablesPagePaginationTitle"]"
           Introduction="@Localizer["TablesPagePaginationIntro"]"
           Name="Pagination">
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource" OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesPageShowTopPaginationTitle"]"
           Introduction="@Localizer["TablesPageShowTopPaginationIntro"]"
           Name="ShowTopPagination">
    <Table TItem="Foo" IsAutoScrollTopWhenClickPage="true" IsFixedHeader="true" Height="200"
           IsPagination="true" ShowTopPagination="true" PageItemsSource="@PageItemsSource" OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" />
        </TableColumns>
    </Table>
</DemoBlock>
