@page "/table/edit"
@inject IStringLocalizer<NavMenu> NavMenuLocalizer
@inject IStringLocalizer<TablesEdit> Localizer
@inject IStringLocalizer<Foo> LocalizerFoo
@inject ToastService ToastService
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["TablesEditTitle"] - @NavMenuLocalizer["TableEdit"]</h3>
<h4>@((MarkupString)Localizer["TablesEditDescription"].Value)</h4>

<DemoBlock Title="@Localizer["TablesEditItemsTitle"]" Introduction="@Localizer["TablesEditItemsIntro"]" Name="EditItems">
    <section ignore>@((MarkupString)Localizer["TablesEditItemsDescription"].Value)</section>
    <Table TItem="Foo" @bind-Items="EditItems"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true"
           OnAddAsync="@OnAddAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" />
            <TableColumn @bind-Field="@context.Education" />
            <TableColumn @bind-Field="@context.Count" />
            <TableColumn @bind-Field="@context.Complete" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesVisibleTitle"]" Introduction="@Localizer["TablesVisibleIntro"]" Name="Visible">
    <Table TItem="Foo" Items="@Items.Take(3)" IsMultipleSelect="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowColumnList="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" Visible="false" IsVisibleWhenAdd="false" IsVisibleWhenEdit="true" />
            <TableColumn @bind-Field="@context.Address" Rows="3" Visible="false" IsVisibleWhenAdd="true" IsVisibleWhenEdit="true" />
            <TableColumn @bind-Field="@context.Count" IsVisibleWhenAdd="false" />
            <TableColumn @bind-Field="@context.Complete" IsVisibleWhenEdit="false" />
            <TableColumn @bind-Field="@context.Education" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesEditTemplateTitle"]" Introduction="@Localizer["TablesEditTemplateIntro"]" Name="EditTemplate">
    <section ignore>@((MarkupString)Localizer["TablesEditTemplateDescription"].Value)</section>
    <Table TItem="Foo" EditDialogShowMaximizeButton="true"
           IsPagination="true" PageItemsSource="@PageItemsSource" DataService="@CustomerDataService"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true" AutoGenerateColumns="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Complete" Align="Alignment.Center"></TableColumn>
            <TableColumn @bind-Field="@context.Count" Align="Alignment.Right"></TableColumn>
            <TableColumn @bind-Field="@context.Hobby"></TableColumn>
        </TableColumns>
        <EditTemplate>
            <!--本例中使用了联动功能，必须封装成组件-->
            <DemoTableEditTemplate Model="context" />
        </EditTemplate>
        <EditFooterTemplate>
            <Button Text="Popup" Color="Color.Danger" Icon="fa-regular fa-comment-dots" OnClick="() => OnClick(context)"></Button>
            <DialogCloseButton></DialogCloseButton>
            <DialogSaveButton Color="Color.Primary" Icon="fa-solid fa-floppy-disk" Text="Save"></DialogSaveButton>
        </EditFooterTemplate>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesEditOnAddAsyncTitle"]" Introduction="@Localizer["TablesEditOnAddAsyncIntro"]" Name="OnAddAsync">
    <section ignore>
        <p>@((MarkupString)Localizer["TablesEditOnAddAsyncDescription"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditOnAddAsyncTips1"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditOnAddAsyncTips2"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditOnAddAsyncTips3"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditOnAddAsyncTips4"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditOnAddAsyncTips5"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditOnAddAsyncTips6"].Value)</p>
    </section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true" EditDialogIsDraggable="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true" IsExtendButtonsInRowHeader="true"
           OnQueryAsync="@OnQueryAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" Readonly="true" />
            <TableColumn @bind-Field="@context.Education" />
            <TableColumn @bind-Field="@context.Count" Ignore="true" />
            <TableColumn @bind-Field="@context.Complete" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesColumnEditTemplateTitle"]" Introduction="@Localizer["TablesColumnEditTemplateIntro"]" Name="ColumnEditTemplate">
    <section ignore>
        <div>@((MarkupString)Localizer["TablesColumnEditTemplateDescription1"].Value)</div>
        <div>@((MarkupString)Localizer["TablesColumnEditTemplateTips"].Value)</div>
    </section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true" IsExtendButtonsInRowHeader="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true"
           OnQueryAsync="@OnQueryAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name">
                <EditTemplate Context="v">
                    <div class="col-12 col-sm-6">
                        <TablesNameDrop Model="v" />
                    </div>
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Address" Rows="3" />
            <TableColumn @bind-Field="@context.Count" />
            <TableColumn @bind-Field="@context.Complete" />
            <TableColumn @bind-Field="@context.Hobby">
                <EditTemplate Context="v">
                    <DemoHobbyTemplate Model="v" />
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.Education" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesEditModeTitle"]" Introduction="@Localizer["TablesEditModeIntro"]" Name="EditMode">
    <section ignore>
        <p>@((MarkupString)Localizer["TablesEditModeDescription"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditModeTips1"].Value)</p>
        <p>@((MarkupString)Localizer["TablesEditModeTips2"].Value)</p>
    </section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true"
           AddModalTitle="@Localizer["TablesEditModeAddModalTitle"]" EditModalTitle="@Localizer["TablesEditModeEditModalTitle"]"
           OnQueryAsync="@OnQueryAsync" EditMode="EditMode.EditForm"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" Rows="3" />
            <TableColumn @bind-Field="@context.Education" />
            <TableColumn @bind-Field="@context.Count" IsVisibleWhenAdd="false" />
            <TableColumn @bind-Field="@context.Complete" IsVisibleWhenEdit="false" />
        </TableColumns>
    </Table>

    <section ignore>
        <p>@((MarkupString)Localizer["TablesEditModeInCell"].Value)</p>
        <RadioList @bind-Value="InsertMode" />
    </section>
    <Table TItem="Foo"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true"
           AddModalTitle="@Localizer["TablesEditModeAddModalTitle"]" EditModalTitle="@Localizer["TablesEditModeEditModalTitle"]"
           EditMode="EditMode.InCell" @bind-Items="@BindItems" InsertRowMode="InsertMode">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" Rows="3" />
            <TableColumn @bind-Field="@context.Education" IsPopover="true" />
            <TableColumn @bind-Field="@context.Count" IsVisibleWhenAdd="false" />
            <TableColumn @bind-Field="@context.Complete" IsVisibleWhenEdit="false" />
        </TableColumns>
    </Table>

    <section ignore>
        <p>@((MarkupString)Localizer["TablesEditModeDrawer"].Value)</p>
    </section>
    <Table TItem="Foo"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true"
           AddModalTitle="@Localizer["TablesEditModeAddModalTitle"]" EditModalTitle="@Localizer["TablesEditModeEditModalTitle"]"
           EditMode="EditMode.Drawer" @bind-Items="@BindItems" InsertRowMode="InsertMode">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" Rows="3" />
            <TableColumn @bind-Field="@context.Education" />
            <TableColumn @bind-Field="@context.Count" IsVisibleWhenAdd="false" />
            <TableColumn @bind-Field="@context.Complete" IsVisibleWhenEdit="false" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesEditInjectDataServiceTitle"]" Introduction="@Localizer["TablesEditInjectDataServiceIntro"]" Name="InjectDataService">
    <section ignore>
        @((MarkupString)Localizer["TablesEditInjectDataServiceDescription"].Value)
        <ul class="ul-demo mb-3">
            <li><code>OnAddAsync</code></li>
            <li><code>OnDeleteAsync</code></li>
            <li><code>OnSaveAsync</code></li>
            <li><code>OnQueryAsync</code></li>
        </ul>
        <div class="mb-3">@Localizer["TablesEditInjectDataServiceTips1"] <a href="@DataServiceUrl" target="_blank">@Localizer["TablesEditInjectDataServiceTips2"]</a></div>
        <b>@Localizer["TablesEditInjectDataServiceTips3"]</b>
        <div class="mt-1">@((MarkupString)Localizer["TablesEditInjectDataServiceTips4"].Value)</div>
        <Pre class="no-highlight my-3">services.AddTableDemoDataService();</Pre>
    </section>
    <Table TItem="Foo"
           IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true" AutoGenerateColumns="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Hobby" Items="@Hobbies" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesEditDataServiceTitle"]" Introduction="@Localizer["TablesEditDataServiceIntro"]" Name="DataService">
    <section ignore>
        <b>@Localizer["TablesEditDataServiceDescription"]</b>
        <div class="mt-1">@((MarkupString)Localizer["TablesEditDataServiceTips1"].Value)</div>
        <div class="mt-1">@((MarkupString)Localizer["TablesEditDataServiceTips2"].Value)</div>
        <Pre class="no-highlight my-3 mb-3">services.AddTableDemoDataService();</Pre>
    </section>
    <Table TItem="Foo" EditDialogShowMaximizeButton="true"
           IsPagination="true" PageItemsSource="@PageItemsSource" DataService="@CustomerDataService"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true" AutoGenerateColumns="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Hobby" Items="@Hobbies" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesEditFooterTemplateTitle"]" Introduction="@Localizer["TablesEditFooterTemplateIntro"]" Name="EditFooterTemplate">
    <section ignore>
        <p>@((MarkupString)Localizer["TablesEditFooterTemplateDescription"].Value)</p>
        <Pre class="mb-3">&lt;EditFooterTemplate Context="model"&gt;
    &lt;Button Text="Popup" Color="Color.Danger" Icon="fa-regular fa-comment-dots" OnClick="() =&gt; OnClick(model)"&gt;&lt;/Button&gt;
    &lt;DialogCloseButton /&gt;
    &lt;DialogSaveButton Color="Color.Primary" Icon="fa-solid fa-floppy-disk" Text="Save" /&gt;
&lt;/EditFooterTemplate&gt;</Pre>
    </section>
    <Table TItem="Foo" IsPagination="true" PageItemsSource="@PageItemsSource"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true" IsExtendButtonsInRowHeader="true"
           OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" />
            <TableColumn @bind-Field="@context.Education" />
            <TableColumn @bind-Field="@context.Count" Align="Alignment.Right" />
            <TableColumn @bind-Field="@context.Complete" Align="Alignment.Center" />
        </TableColumns>
        <EditTemplate>
            <!--本处代码未使用联动功能，所以无须封装成组件，如果增加 Hobby 字段使用 MultiSelect 组件必须封装成组件如上例-->
            <div class="row g-3 form-inline">
                <div class="col-12 col-sm-6">
                    <BootstrapInput @bind-Value="@context.Name" placeholder="@PlaceHolderString" maxlength="50" />
                </div>
                <div class="col-12 col-sm-6">
                    <BootstrapInput @bind-Value="@context.Address" placeholder="@PlaceHolderString" maxlength="50" />
                </div>
                <div class="col-12 col-sm-6">
                    <DateTimePicker @bind-Value="@context.DateTime" />
                </div>
                <div class="col-12 col-sm-6">
                    <Select @bind-Value="@context.Education" />
                </div>
                <div class="col-12 col-sm-6">
                    <BootstrapInputNumber @bind-Value="@context.Count" ShowButton="true" />
                </div>
                <div class="col-12 col-sm-6">
                    <Switch @bind-Value="@context.Complete" ShowInnerText="true" />
                </div>
            </div>
        </EditTemplate>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesReadonlyColumnTitle"]" Introduction="@Localizer["TablesReadonlyColumnIntro"]" Name="ReadonlyColumn">
    <section ignore>
        <p>@Localizer["TablesReadonlyColumnDescription"]</p>
        <p>@((MarkupString)Localizer["TablesReadonlyColumnTips1"].Value)</p>
        <Pre class="no-highlight my-3 mb-3">&lt;TableColumn Field="@@context.ReadonlyColumn" FieldName="ReadonlyColumn" /&gt;</Pre>
        <p>@((MarkupString)Localizer["TablesReadonlyColumnTips2"].Value)</p>
        <Pre class="no-highlight my-3 mb-3">&lt;TableColumn TItem="Foo1" TType="string" Field="@@context.ReadonlyColumn" FieldName="ReadonlyColumn" /&gt;</Pre>
    </section>
    <Table TItem="Foo" EditDialogShowMaximizeButton="true"
           IsPagination="true" PageItemsSource="@PageItemsSource" DataService="@CustomerDataService"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true" AutoGenerateColumns="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Hobby" Items="@Hobbies" Ignore="true" />
            <TableColumn Field="@context.ReadonlyColumn" FieldName="@nameof(context.ReadonlyColumn)" Text="@LocalizerFoo[nameof(context.ReadonlyColumn)]" />
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesTemplateColumnTitle"]" Introduction="@Localizer["TablesTemplateColumnIntro"]" Name="TableTemplateColumn">
    <section ignore>
        <p>@((MarkupString)Localizer["TablesTemplateColumnDescription"].Value)</p>
    </section>
    <Table TItem="Foo" EditDialogShowMaximizeButton="true"
           IsPagination="true" PageItemsSource="@PageItemsSource" DataService="@CustomerDataService"
           IsStriped="true" IsBordered="true" IsMultipleSelect="true" AutoGenerateColumns="true"
           ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Hobby" Items="@Hobbies" Ignore="true" />
            <TableTemplateColumn Text="@Localizer["TableTemplateColumnText"]">
                <Template Context="v">
                    Test
                </Template>
            </TableTemplateColumn>
        </TableColumns>
    </Table>
</DemoBlock>
