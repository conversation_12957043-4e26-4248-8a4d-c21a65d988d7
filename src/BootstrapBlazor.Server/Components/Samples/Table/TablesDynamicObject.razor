@page "/table/dynamic-object"
@inject IStringLocalizer<NavMenu> NavMenuLocalizer
@inject IStringLocalizer<TablesDynamicObject> Localizer

<h3>@Localizer["TablesDynamicObjectTitle"] - @NavMenuLocalizer["TableDynamicObject"]</h3>
<h4>@((MarkupString)Localizer["TablesDynamicObjectDescription"].Value)</h4>

<DemoBlock Title="@Localizer["TablesDynamicObjectMetaObjectProviderTitle"]"
           Introduction="@Localizer["TablesDynamicObjectMetaObjectProviderIntro"]"
           Name="MetaObjectProvider">
    <Table TItem="CustomDynamicData" OnQueryAsync="OnQueryAsync"
           IsStriped="true" IsBordered="true" ShowToolbar="true" ShowColumnList="true" ShowDefaultButtons="false" ShowRefresh="false">
        <TableColumns>
            <TableColumn @bind-Field="@context.Fix" Sortable="true" Filterable="true" />
            @foreach (var element in DynamicColumnList)
            {
                <TableColumn Field="@element.ToString()" FieldName="@element.ToString()" Text="@element" Sortable="true" Filterable="true">
                    <Template Context="v">
                        <div>Template - @v.Value</div>
                    </Template>
                </TableColumn>
            }
        </TableColumns>
    </Table>
</DemoBlock>

<DemoBlock Title="@Localizer["TablesDynamicObjectIDynamicObjectTitle"]"
           Introduction="@Localizer["TablesDynamicObjectIDynamicObjectIntro"]"
           Name="IDynamicObject">
    <Table TItem="CustomDynamicColumnsObjectData" Items="CustomDynamicItems"
           IsStriped="true" IsBordered="true" ShowToolbar="true" ShowColumnList="true" ShowDefaultButtons="false" ShowRefresh="false">
        <TableColumns>
            <TableColumn @bind-Field="@context.Fix" Sortable="true" Filterable="true" />
            @foreach (var element in StaticColumnList)
            {
                <TableColumn Field="@element" FieldName="@element" Text="@element" Sortable="true" Filterable="true">
                    <Template Context="v">
                        <div>Template - @v.Value</div>
                    </Template>
                </TableColumn>
            }
        </TableColumns>
    </Table>
</DemoBlock>
