@page "/select"
@inject DialogService Dialog
@inject IStringLocalizer<Selects> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["SelectsTitle"]</h3>

<h4>@Localizer["SelectsDescription"]</h4>

<DemoBlock Title="@Localizer["SelectsNormalTitle"]"
           Introduction="@Localizer["SelectsNormalIntro"]"
           Name="Normal">
    <section ignore>@((MarkupString)Localizer["SelectsNormalDescription"].Value)</section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select Items="Items" OnSelectedItemChanged="OnItemChanged" @bind-Value="Model.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select Color="Color.Primary" Items="Items" @bind-Value="Model.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select Color="Color.Success" Items="Items" @bind-Value="Model.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select Color="Color.Danger" Items="Items" @bind-Value="Model.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select Color="Color.Warning" Items="Items" @bind-Value="Model.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select Color="Color.Info" Items="Items" @bind-Value="Model.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select Color="Color.Secondary" Items="Items" @bind-Value="Model.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select Color="Color.Dark" Items="Items" @bind-Value="Model.Name"></Select>
        </div>
    </div>
    <ConsoleLogger @ref="Logger" />
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsDisableTitle"]"
           Introduction="@Localizer["SelectsDisableIntro"]"
           Name="Disable">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Primary" Items="Items" IsDisabled="true"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Success" Items="Items" IsDisabled="true"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Danger" Items="Items" IsDisabled="true"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Warning" Items="Items" IsDisabled="true"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Info" Items="Items" IsDisabled="true"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Secondary" Items="Items" IsDisabled="true"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Dark" Items="Items" IsDisabled="true"></Select>
        </div>
    </div>
    <p class="mt-3">@((MarkupString)Localizer["SelectsDisableOption"].Value)</p>
    <div class="row">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Items="Items4" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsBindingTitle"]"
           Introduction="@Localizer["SelectsBindingIntro"]"
           Name="Binding">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select Color="Color.Primary" Items="Items" @bind-Value="BindingModel.Name"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInput readonly @bind-Value="BindingModel.Name" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsBindingSelectedItemTitle"]"
           Introduction="@Localizer["SelectsBindingSelectedItemIntro"]"
           Name="BindingSelectedItem">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select Color="Color.Primary" Items="Items" @bind-Value="Item"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Display Value="@ItemString"></Display>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsCascadingTitle"]"
           Introduction="@Localizer["SelectsCascadingIntro"]"
           Name="Cascading">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Items="Items3" OnSelectedItemChanged="OnCascadeBindSelectClick" />
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Items="Items2" />
        </div>
        <div class="col-12">
            <Button Text="@Localizer["SelectsCascadingButtonText1"]" OnClickWithoutRender="OnShowDialog" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsClientValidationTitle"]"
           Introduction="@Localizer["SelectsClientValidationIntro"]"
           Name="ClientValidation">
    <ValidateForm Model="ValidateModel">
        <div class="row g-3">
            <div class="col-12 col-sm-6">
                <Select @bind-Value="ValidateModel.Name">
                    <Options>
                        <SelectOption Text="@Localizer["SelectsOption1"]" Value="" />
                        <SelectOption Text="@Localizer["SelectsOption2"]" Value="1" />
                        <SelectOption Text="@Localizer["SelectsOption3"]" Value="2" />
                        <SelectOption Text="@Localizer["SelectsOption4"]" Value="3" />
                    </Options>
                </Select>
            </div>
            <div class="col-12 col-sm-6 align-self-end">
                <Button ButtonType="ButtonType.Submit">@Localizer["SelectsClientValidationButtonText2"]</Button>
            </div>
        </div>
    </ValidateForm>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsGroupTitle"]"
           Introduction="@Localizer["SelectsGroupIntro"]"
           Name="Group">
    <div class="row">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Primary" Items="GroupItems">
            </Select>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsGuidTitle"]"
           Introduction="@Localizer["SelectsGuidIntro"]"
           Name="Guid">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select Color="Color.Primary" Items="GuidItems" @bind-Value="CurrentGuid" />
        </div>
        <div class="col-12 col-sm-6">
            <div class="form-control">@CurrentGuid</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsDisplayLabelTitle"]"
           Introduction="@Localizer["SelectsDisplayLabelIntro"]"
           Name="DisplayLabel">
    <p>@((MarkupString)Localizer["SelectsDisplayLabelDescription"].Value)</p>
    <Divider Text="@Localizer["SelectsDisplayLabelDivider1"]" Alignment="Alignment.Left" style="margin: 2rem 0;"></Divider>
    <ValidateForm Model="LabelModel">
        <div class="row">
            <div class="col-12">
                <Select Color="Color.Primary" Items="Items" @bind-Value="LabelModel.Name" />
            </div>
        </div>
    </ValidateForm>
    <Divider Text="@Localizer["SelectsDisplayLabelDivider2"]" Alignment="Alignment.Left" style="margin: 2rem 0;" />
    <div class="row">
        <div class="col-12">
            <Select Color="Color.Primary" Items="Items" @bind-Value="LabelModel.Name" />
        </div>
    </div>
    <Divider Text="@Localizer["SelectsDisplayLabelDivider3"]" Alignment="Alignment.Left" style="margin: 2rem 0;"></Divider>
    <div class="row">
        <div class="col-12">
            <Select Color="Color.Primary" Items="Items" @bind-Value="LabelModel.Name" DisplayText="@Localizer["SelectsDisplayLabelSelectText"]" ShowLabel="true" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsStaticTitle"]"
           Introduction="@Localizer["SelectsStaticIntro"]"
           Name="Static">
    <div class="row">
        <div class="col-12 col-sm-6">
            <Select TValue="string">
                <Options>
                    <SelectOption Text="@Localizer["SelectsOption1"]" Value="1" />
                    <SelectOption Text="@Localizer["SelectsOption2"]" Value="2" Active="true" />
                    <SelectOption Text="@Localizer["SelectsOption3"]" Value="3" />
                </Options>
            </Select>
        </div>
    </div>
</DemoBlock> 

<DemoBlock Title="@Localizer["SelectsEnumTitle"]"
           Introduction="@Localizer["SelectsEnumIntro"]"
           Name="Enum">
    <section ignore>
        <p>@((MarkupString)Localizer["SelectsEnumDescription1"].Value)</p>
        <div>@((MarkupString)Localizer["SelectsEnumDescription2"].Value)</div>
    </section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select @bind-Value="SelectedEnumItem1" PlaceHolder="@Localizer["SelectsPlaceHolder"]" ShowLabel="true" DisplayText="@Localizer["SelectsEnumSelectText1"]">
            </Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select @bind-Value="SelectedEnumItem1" ShowLabel="true" DisplayText="@Localizer["SelectsEnumSelectText1"]">
            </Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select @bind-Value="SelectedEnumItem" ShowLabel="true" DisplayText="@Localizer["SelectsEnumSelectText2"]">
            </Select>
        </div>
        <div class="col-12 col-sm-6 align-self-end">
            <div class="form-control">@SelectedEnumItem</div>
        </div>
        <div class="col-12 col-sm-6">
            <Select @bind-Value="_enumValueDemo" Items="_enumValueDemoItems" ShowLabel="true" DisplayText="@Localizer["SelectsEnumSelectText3"]">
            </Select>
        </div>
        <div class="col-12 col-sm-6 align-self-end">
            <div class="form-control">@_enumValueDemo</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsNullableTitle"]"
           Introduction="@Localizer["SelectsNullableIntro"]"
           Name="Nullable">
    <section ignore>@((MarkupString)Localizer["SelectsNullableDescription"].Value)</section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select Items="NullableIntItems" @bind-Value="NullableSelectedIntItem">
            </Select>
        </div>
        <div class="col-12 col-sm-6">
            <div class="form-control">@GetSelectedIntItemString()</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsNullableBooleanTitle"]"
           Introduction="@Localizer["SelectsNullableBooleanIntro"]"
           Name="NullableBoolean">
    <section ignore>
        <p>@((MarkupString)Localizer["SelectsNullableBooleanDescription1"].Value)</p>
        <div>@((MarkupString)Localizer["SelectsNullableBooleanDescription2"].Value)</div>
    </section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select Items="NullableBoolItems" @bind-Value="SelectedBoolItem">
            </Select>
        </div>
        <div class="col-12 col-sm-6">
            <div class="form-control">@GetSelectedBoolItemString()</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsCustomTemplateTitle"]"
           Introduction="@Localizer["SelectsCustomTemplateIntro"]"
           Name="CustomTemplate">
    <div class="row">
        <div class="col-12 col-sm-6 select-custom">
            <Select @bind-Value="@_fooName" Items="VirtualItems">
                <ItemTemplate>
                    @{
                        var foo = Foos.First(i => i.Name == context.Text);
                    }
                    <div class="dropdown-item-demo">
                        <div class="select-custom-header">
                            <div class="id">@foo.Id</div>
                            <div class="name">@foo.Name</div>
                            <Light Color="@(foo.Complete ? Color.Success : Color.Warning)"></Light>
                        </div>
                        <Divider />
                        <div class="select-custom-body">
                            <img src="@WebsiteOption.Value.GetAvatarUrl(foo.Id)" class="bb-avatar" />
                            <div class="select-custom-detail">
                                <div class="d-flex">
                                    <div class="flex-fill">
                                        <div>@Foo.GetTitle(foo.Id)</div>
                                        <div class="mt-3">@foo.Address</div>
                                    </div>
                                    <div>
                                        <Circle Width="80" Value="@foo.Count" Color="Color.Info" StrokeWidth="3" />
                                    </div>
                                </div>
                                <BootstrapBlazor.Components.Progress Value="@foo.Count"></BootstrapBlazor.Components.Progress>
                            </div>
                        </div>
                    </div>
                </ItemTemplate>
            </Select>
        </div>
        <div class="col-12 col-sm-6">
            <Display TValue="string" Value="@_fooName" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsShowSearchTitle"]"
           Introduction="@Localizer["SelectsShowSearchIntro"]"
           Name="ShowSearch">
    <section ignore>
        <div class="row g-3">
            <div class="col-12 col-sm-6">
                <BootstrapInputGroup>
                    <BootstrapInputGroupLabel DisplayText="IsClearable" />
                    <Checkbox @bind-Value="@_isShowSearchClearable" />
                </BootstrapInputGroup>
            </div>
        </div>
    </section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Items="Items" ShowSearch="true"
                    IsClearable="_isShowSearchClearable">
            </Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select TValue="string" Items="StringItems" ShowSearch="true"
                    IsClearable="_isShowSearchClearable">
            </Select>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsClearableTitle"]"
           Introduction="@Localizer["SelectsClearableIntro"]"
           Name="IsClearable">
    <section ignore>
        <p>@((MarkupString)Localizer["SelectsClearableDesc"].Value)</p>
    </section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select IsClearable="true" Items="ClearableItems" Value="ClearableModel.NullableName" ShowLabel="true" DisplayText="string?"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select IsClearable="true" Items="Items" Value="ClearableModel.Name" ShowLabel="true" DisplayText="string"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select IsClearable="true" Items="IntItems" Value="ClearableModel.NullableCount" ShowLabel="true" DisplayText="int?"></Select>
        </div>
        <div class="col-12 col-sm-6">
            <Select IsClearable="true" Items="IntItems" Value="ClearableModel.Count" ShowLabel="true" DisplayText="int"></Select>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsConfirmSelectTitle"]"
           Introduction="@Localizer["SelectsConfirmSelectIntro"]"
           Name="ConfirmSelect">
    <section ignore>
        <ul class="ul-demo">
            <li>@((MarkupString)Localizer["SelectConfifrmSelectDesc1"].Value)</li>
            <li>@((MarkupString)Localizer["SelectConfifrmSelectDesc2"].Value)</li>
        </ul>
    </section>
    <div class="row">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Items="Items" ShowSwal="true"
                    SwalTitle="@Localizer["SwalTitle"]" SwalContent="@Localizer["SwalContent"]" SwalFooter="@Localizer["SwalFooter"]" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsDisplayTemplateTitle"]"
           Introduction="@Localizer["SelectsDisplayTemplateIntro"]"
           Name="DisplayTemplate">
    <div class="row">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Items="Items">
                <DisplayTemplate>
                    <i class="fa-solid fa-flag"></i>
                    <span>@context?.Text</span>
                </DisplayTemplate>
            </Select>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsTimeZoneTitle"]"
           Introduction="@Localizer["SelectsTimeZoneIntro"]"
           Name="TimeZone">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Value="@TimeZoneId" Items="TimeZoneItems" OnValueChanged="OnTimeZoneValueChanged" />
        </div>
        <div class="col-12 col-sm-6">
            <Display TValue="TimeSpan" Value="@TimeZoneValue" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsPopoverTitle"]"
           Introduction="@Localizer["SelectsPopoverIntro"]"
           Name="Popover">
    <div class="row g-3 form-inline">
        <div class="col-12">
            <Switch @bind-Value="_showPopoverSearch" ShowLabel="true" DisplayText="ShowSearch"></Switch>
        </div>
        <div class="col-12">
            <Select TValue="string" Items="Items" IsPopover="true" ShowSearch="_showPopoverSearch" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsIsEditableTitle"]" Introduction="@Localizer["SelectsIsEditableIntro"]" Name="IsEditable">
    <section ignore>@((MarkupString)Localizer["SelectsIsEditableDesc"].Value)</section>
    <div class="row">
        <div class="col-12 col-sm-6">
            <Select TValue="string" Color="Color.Primary" Items="Items" IsEditable="true" OnInputChangedCallback="OnInputChangedCallback"></Select>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsVirtualizeTitle"]"
           Introduction="@Localizer["SelectsVirtualizeIntro"]"
           Name="IsVirtualize">
    <section ignore>
        <p>@((MarkupString)Localizer["SelectsVirtualizeDescription"].Value)</p>
        <div class="row g-3">
            <div class="col-12 col-sm-6">
                <BootstrapInputGroup>
                    <BootstrapInputGroupLabel DisplayText="ShowSearch" />
                    <Checkbox @bind-Value="@_showSearch" />
                </BootstrapInputGroup>
            </div>
            <div class="col-12 col-sm-6">
                <BootstrapInputGroup>
                    <BootstrapInputGroupLabel DisplayText="IsClearable" />
                    <Checkbox @bind-Value="@_isClearable" />
                </BootstrapInputGroup>
            </div>
        </div>
    </section>

    <p class="code-label">1. 使用 OnQueryAsync 作为数据源</p>
    <div class="row mb-3">
        <div class="col-6">
            <Select IsVirtualize="true" OnQueryAsync="OnQueryAsync" @bind-Value="VirtualItem1"
                    ShowSearch="_showSearch" IsClearable="_isClearable"></Select>
        </div>
        <div class="col-6">
            <Display TValue="string" Value="@VirtualItem1?.Text"></Display>
        </div>
    </div>

    <p class="code-label">2. 使用 Items 作为数据源</p>
    <div class="row">
        <div class="col-6">
            <Select IsVirtualize="true" Items="VirtualItems" @bind-Value="VirtualItem2"
                    ShowSearch="_showSearch" IsClearable="_isClearable"></Select>
        </div>
        <div class="col-6">
            <Display TValue="string" Value="@VirtualItem2?.Text"></Display>
        </div>
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />

<EventTable Items="@GetEvents()" />
