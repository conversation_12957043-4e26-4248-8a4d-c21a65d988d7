@page "/select-generic"
@inject DialogService Dialog
@inject IStringLocalizer<Selects> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["SelectsTitle"]</h3>

<h4>@Localizer["SelectsDescription"]</h4>

<DemoBlock Title="@Localizer["SelectsNormalTitle"]"
           Introduction="@Localizer["SelectsNormalIntro"]"
           Name="Normal">
    <p>@((MarkupString)Localizer["SelectsNormalDescription"].Value)</p>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric Items="Items" OnSelectedItemChanged="OnItemChanged" @bind-Value="Model.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Primary" Items="Items" @bind-Value="Model.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Success" Items="Items" @bind-Value="Model.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Danger" Items="Items" @bind-Value="Model.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Warning" Items="Items" @bind-Value="Model.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Info" Items="Items" @bind-Value="Model.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Secondary" Items="Items" @bind-Value="Model.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Dark" Items="Items" @bind-Value="Model.Name"></SelectGeneric>
        </div>
    </div>
    <ConsoleLogger @ref="Logger" />
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsDisableTitle"]"
           Introduction="@Localizer["SelectsDisableIntro"]"
           Name="Disable">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Primary" Items="Items" IsDisabled="true"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Success" Items="Items" IsDisabled="true"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Danger" Items="Items" IsDisabled="true"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Warning" Items="Items" IsDisabled="true"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Info" Items="Items" IsDisabled="true"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Secondary" Items="Items" IsDisabled="true"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Dark" Items="Items" IsDisabled="true"></SelectGeneric>
        </div>
    </div>
    <p class="mt-3">@((MarkupString)Localizer["SelectsDisableOption"].Value)</p>
    <div class="row">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Items="Items4" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsBindingTitle"]"
           Introduction="@Localizer["SelectsBindingIntro"]"
           Name="Binding">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Primary" Items="Items" @bind-Value="BindingModel.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInput readonly @bind-Value="BindingModel.Name" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsClearableTitle"]"
           Introduction="@Localizer["SelectsClearableIntro"]"
           Name="IsClearable">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Primary" IsClearable="true" Items="ClearableItems" Value="ClearableModel.Name"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Primary" IsClearable="true" Items="Items" Value="ClearableModel.Name"></SelectGeneric>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsCascadingTitle"]"
           Introduction="@Localizer["SelectsCascadingIntro"]"
           Name="Cascading">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Items="Items3" OnSelectedItemChanged="OnCascadeBindSelectClick" />
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Items="Items2" />
        </div>
        <div class="col-12">
            <Button Text="@Localizer["SelectsCascadingButtonText1"]" OnClickWithoutRender="OnShowDialog" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsClientValidationTitle"]"
           Introduction="@Localizer["SelectsClientValidationIntro"]"
           Name="ClientValidation">
    <ValidateForm Model="ValidateModel">
        <div class="row g-3">
            <div class="col-12 col-sm-6">
                <SelectGeneric @bind-Value="ValidateModel.Name">
                    <Options>
                        <SelectOptionGeneric Text="@Localizer["SelectsOption1"]" Value="4" />
                        <SelectOptionGeneric Text="@Localizer["SelectsOption2"]" Value="1" />
                        <SelectOptionGeneric Text="@Localizer["SelectsOption3"]" Value="2" />
                        <SelectOptionGeneric Text="@Localizer["SelectsOption4"]" Value="3" />
                    </Options>
                </SelectGeneric>
            </div>
            <div class="col-12 col-sm-6 align-self-end">
                <Button ButtonType="ButtonType.Submit">@Localizer["SelectsClientValidationButtonText2"]</Button>
            </div>
        </div>
    </ValidateForm>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsGroupTitle"]"
           Introduction="@Localizer["SelectsGroupIntro"]"
           Name="Group">
    <div class="row">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Primary" Items="GroupItems"></SelectGeneric>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsGuidTitle"]"
           Introduction="@Localizer["SelectsGuidIntro"]"
           Name="Guid">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric Color="Color.Primary" Items="GuidItems" @bind-Value="CurrentGuid"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <div class="form-control">@CurrentGuid</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsDisplayLabelTitle"]"
           Introduction="@Localizer["SelectsDisplayLabelIntro"]"
           Name="DisplayLabel">
    <p>@((MarkupString)Localizer["SelectsDisplayLabelDescription"].Value)</p>
    <Divider Text="@Localizer["SelectsDisplayLabelDivider1"]" Alignment="Alignment.Left" style="margin: 2rem 0;"></Divider>
    <ValidateForm Model="LabelModel">
        <div class="row">
            <div class="col-12">
                <SelectGeneric Color="Color.Primary" Items="Items" @bind-Value="LabelModel.Name" />
            </div>
        </div>
    </ValidateForm>
    <Divider Text="@Localizer["SelectsDisplayLabelDivider2"]" Alignment="Alignment.Left" style="margin: 2rem 0;" />
    <div class="row">
        <div class="col-12">
            <SelectGeneric Color="Color.Primary" Items="Items" @bind-Value="LabelModel.Name" />
        </div>
    </div>
    <Divider Text="@Localizer["SelectsDisplayLabelDivider3"]" Alignment="Alignment.Left" style="margin: 2rem 0;"></Divider>
    <div class="row">
        <div class="col-12">
            <SelectGeneric Color="Color.Primary" Items="Items" @bind-Value="LabelModel.Name" DisplayText="@Localizer["SelectsDisplayLabelSelectText"]" ShowLabel="true" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsStaticTitle"]"
           Introduction="@Localizer["SelectsStaticIntro"]"
           Name="Static">
    <div class="row">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string">
                <Options>
                    <SelectOption Text="@Localizer["SelectsOption1"]" Value="1" />
                    <SelectOption Text="@Localizer["SelectsOption2"]" Value="2" Active="true" />
                    <SelectOption Text="@Localizer["SelectsOption3"]" Value="3" />
                </Options>
            </SelectGeneric>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsEnumTitle"]"
           Introduction="@Localizer["SelectsEnumIntro"]"
           Name="Enum">
    <p>@((MarkupString)Localizer["SelectsEnumDescription1"].Value)</p>
    <p>@((MarkupString)Localizer["SelectsEnumDescription2"].Value)</p>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric @bind-Value="SelectedEnumItem1" PlaceHolder="@Localizer["SelectsPlaceHolder"]" ShowLabel="true" DisplayText="@Localizer["SelectsEnumSelectText1"]">
            </SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric @bind-Value="SelectedEnumItem1" ShowLabel="true" DisplayText="@Localizer["SelectsEnumSelectText1"]">
            </SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric @bind-Value="SelectedEnumItem" ShowLabel="true" DisplayText="@Localizer["SelectsEnumSelectText2"]">
            </SelectGeneric>
        </div>
        <div class="col-12 col-sm-6 align-self-end">
            <div class="form-control">@SelectedEnumItem</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsNullableTitle"]"
           Introduction="@Localizer["SelectsNullableIntro"]"
           Name="Nullable">
    <section ignore>@((MarkupString)Localizer["SelectsNullableDescription"].Value)</section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric Items="NullableIntItems" @bind-Value="NullableSelectedIntItem"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <div class="form-control">@GetSelectedIntItemString()</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsNullableBooleanTitle"]"
           Introduction="@Localizer["SelectsNullableBooleanIntro"]"
           Name="NullableBoolean">
    <section ignore>
        <p>@((MarkupString)Localizer["SelectsNullableBooleanDescription1"].Value)</p>
        <div>@((MarkupString)Localizer["SelectsNullableBooleanDescription2"].Value)</div>
    </section>
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric Items="NullableBoolItems" @bind-Value="SelectedBoolItem"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <div class="form-control">@GetSelectedBoolItemString()</div>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsCustomTemplateTitle"]"
           Introduction="@Localizer["SelectsCustomTemplateIntro"]"
           Name="CustomTemplate">
    <div class="row">
        <div class="col-12 col-sm-6 select-custom">
            <SelectGeneric @bind-Value="@_foo" Items="VirtualItems">
                <ItemTemplate>
                    @{
                        var foo = context.Value!;
                    }
                    <div class="dropdown-item-demo">
                        <div class="select-custom-header">
                            <div class="id">@foo.Id</div>
                            <div class="name">@foo.Name</div>
                            <Light Color="@(foo.Complete ? Color.Success : Color.Warning)"></Light>
                        </div>
                        <Divider />
                        <div class="select-custom-body">
                            <img src="@WebsiteOption.Value.GetAvatarUrl(foo.Id)" class="bb-avatar" />
                            <div class="select-custom-detail">
                                <div class="d-flex">
                                    <div class="flex-fill">
                                        <div>@Foo.GetTitle(foo.Id)</div>
                                        <div class="mt-3">@foo.Address</div>
                                    </div>
                                    <div>
                                        <Circle Width="80" Value="@foo.Count" Color="Color.Info" StrokeWidth="3" />
                                    </div>
                                </div>
                                <BootstrapBlazor.Components.Progress Value="@foo.Count"></BootstrapBlazor.Components.Progress>
                            </div>
                        </div>
                    </div>
                </ItemTemplate>
            </SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <Display TValue="string" Value="@_foo.Name" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsShowSearchTitle"]"
           Introduction="@Localizer["SelectsShowSearchIntro"]"
           Name="ShowSearch">
    <section ignore>
        <div class="row mb-3">
            <div class="col-12 col-sm-6">
                <BootstrapInputGroup>
                    <BootstrapInputGroupLabel DisplayText="IsClearable" />
                    <Checkbox @bind-Value="@_isShowSearchClearable" />
                </BootstrapInputGroup>
            </div>
        </div>
    </section>
    <div class="row g-3 mt-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Items="Items" ShowSearch="true" IsClearable="_isShowSearchClearable" />
        </div>
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Items="StringItems" ShowSearch="true" IsClearable="_isShowSearchClearable" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsConfirmSelectTitle"]"
           Introduction="@Localizer["SelectsConfirmSelectIntro"]"
           Name="ConfirmSelect">
    <div class="row">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Items="Items" OnBeforeSelectedItemChange="@OnBeforeSelectedItemChange"
                           SwalTitle="@Localizer["SwalTitle"]" SwalContent="@Localizer["SwalContent"]" SwalFooter="@Localizer["SwalFooter"]"></SelectGeneric>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsDisplayTemplateTitle"]"
           Introduction="@Localizer["SelectsDisplayTemplateIntro"]"
           Name="DisplayTemplate">
    <div class="row">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Items="Items">
                <DisplayTemplate>
                    <i class="fa-solid fa-flag"></i>
                    <span>@context?.Text</span>
                </DisplayTemplate>
            </SelectGeneric>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsTimeZoneTitle"]"
           Introduction="@Localizer["SelectsTimeZoneIntro"]"
           Name="TimeZone">
    <div class="row g-3">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Value="@TimeZoneId" Items="TimeZoneItems" OnValueChanged="OnTimeZoneValueChanged" />
        </div>
        <div class="col-12 col-sm-6">
            <Display TValue="TimeSpan" Value="@TimeZoneValue" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsPopoverTitle"]"
           Introduction="@Localizer["SelectsPopoverIntro"]"
           Name="Popover">
    <div class="row">
        <div class="col-12 col-sm-6 overflow-hidden">
            <SelectGeneric TValue="string" Items="Items" IsPopover="true" ShowSearch="true" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsIsEditableTitle"]" Introduction="@Localizer["SelectsIsEditableIntro"]" Name="IsEditable">
    <section ignore>@((MarkupString)Localizer["SelectsIsEditableDesc"].Value)</section>
    <div class="row">
        <div class="col-12 col-sm-6">
            <SelectGeneric TValue="string" Color="Color.Primary" Items="Items" IsEditable="true" OnInputChangedCallback="OnInputChangedCallback"></SelectGeneric>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsVirtualizeTitle"]"
           Introduction="@Localizer["SelectsVirtualizeIntro"]"
           Name="IsVirtualize">
    <section ignore>@((MarkupString)Localizer["SelectsVirtualizeDescription"].Value)</section>

    <div class="row mb-3">
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="ShowSearch" />
                <Checkbox @bind-Value="@_showSearch" />
            </BootstrapInputGroup>
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInputGroup>
                <BootstrapInputGroupLabel DisplayText="IsClearable" />
                <Checkbox @bind-Value="@_isClearable" />
            </BootstrapInputGroup>
        </div>
    </div>

    <p class="code-label">1. 使用 OnQueryAsync 作为数据源</p>
    <div class="row mb-3">
        <div class="col-6">
            <SelectGeneric IsVirtualize="true" OnQueryAsync="OnQueryAsync" @bind-Value="VirtualItem1" ShowSearch="_showSearch" IsClearable="_isClearable"></SelectGeneric>
        </div>
        <div class="col-6">
            <Display TValue="string" Value="@VirtualItem1.Name"></Display>
        </div>
    </div>

    <p class="code-label">2. 使用 Items 作为数据源</p>
    <div class="row">
        <div class="col-6">
            <SelectGeneric IsVirtualize="true" Items="VirtualItems" @bind-Value="VirtualItem2" ShowSearch="_showSearch" IsClearable="_isClearable"></SelectGeneric>
        </div>
        <div class="col-6">
            <Display TValue="string" Value="@VirtualItem2.Name"></Display>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SelectsGenericTitle"]"
           Introduction="@Localizer["SelectsGenericIntro"]"
           Name="Generic">
    <section ignore>@((MarkupString)Localizer["SelectsGenericDesc"].Value)</section>
    <div class="row">
        <div class="col-12 col-sm-6">
            <SelectGeneric Items="_genericItems" @bind-Value="_selectedFoo"
                           IsEditable="true" TextConvertToValueCallback="TextConvertToValueCallback"></SelectGeneric>
        </div>
        <div class="col-12 col-sm-6">
            <Display Value="_selectedFoo.Address"></Display>
        </div>
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />

<EventTable Items="@GetEvents()" />
