@page "/tutorials/template2"
@layout TutorialsLoginLayout

<div class="login-item">
    <div class="text-center">
        <h4 class="text-dark">欢迎使用 BootstrapBlazor 组件库</h4>
        <h4 class="text-dark">用户登录!</h4>
    </div>
    <ValidateForm Model="@Model" OnValidSubmit="OnSubmit">
        <BootstrapInput @bind-Value="Model.UserName" ShowLabel="false" PlaceHolder="请输入账号" />
        <BootstrapPassword @bind-Value="Model.Password" ShowLabel="false" PlaceHolder="请输入密码" />
        <Checkbox @bind-Value="@Model.RememberMe" ShowLabel="false" ShowAfterLabel="true" DisplayText="记住登录状态" />
        <Button Text="登录" Color="Color.Primary" ButtonType="ButtonType.Submit"></Button>
    </ValidateForm>
    <hr />
    <Button Text="忘记密码" Color="Color.Danger"></Button>
    <Button Text="用户注册" Color="Color.Info"></Button>
</div>

@code {
    [CascadingParameter]
    [NotNull]
    private LoginModel? Model { get; set; }

    private Task OnSubmit(EditContext context)
    {
        StateHasChanged();
        return Task.CompletedTask;
    }
}
