@page "/tutorials/template3"
@layout TutorialsLoginLayout
@inject IOptions<WebsiteOptions> WebsiteOption

<div class="login-item login-item-avatar">
    <div class="text-center">
        <Avatar Url="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" IsCircle="true" Size="Size.ExtraExtraLarge" />
        <h4 class="text-dark">欢迎使用 BootstrapBlazor 组件库</h4>
    </div>
    <ValidateForm Model="@Model" OnValidSubmit="OnSubmit">
        <BootstrapInput TValue="string" ShowLabel="false" PlaceHolder="请输入账号" />
        <BootstrapPassword ShowLabel="false" PlaceHolder="请输入密码" />
        <Checkbox TValue="bool" ShowLabel="false" ShowAfterLabel="true" DisplayText="记住登录状态" />
        <Button Text="登录" Color="Color.Primary" ButtonType="ButtonType.Submit"></Button>
    </ValidateForm>
    <hr />
    <div class="d-flex">
        <Button Text="忘记密码" Color="Color.Danger"></Button>
        <Button Text="用户注册" Color="Color.Info"></Button>
    </div>
</div>

@code {
    [CascadingParameter]
    [NotNull]
    private LoginModel? Model { get; set; }

    private Task OnSubmit(EditContext context)
    {
        StateHasChanged();
        return Task.CompletedTask;
    }
}
