@page "/tutorials/template1"
@layout TutorialsLoginLayout

<div class="login-item login-item-floating">
    <div class="text-center">
        <h4 class="text-dark">欢迎使用 BootstrapBlazor 组件库</h4>
        <h4 class="text-dark">用户登录!</h4>
    </div>
    <ValidateForm Model="@Model" OnValidSubmit="OnSubmit">
        <FloatingLabel DisplayText="邮箱" TValue="string" PlaceHolder="<EMAIL>" />
        <FloatingLabel DisplayText="密码" TValue="string" type="password" PlaceHolder="password" />
        <Checkbox TValue="bool" ShowLabel="false" ShowAfterLabel="true" DisplayText="记住登录状态" />
        <Button Text="登录" Color="Color.Primary" ButtonType="ButtonType.Submit"></Button>
    </ValidateForm>
    <hr />
    <Button Text="忘记密码" Color="Color.Danger"></Button>
    <Button Text="用户注册" Color="Color.Info"></Button>
</div>

@code {
    [CascadingParameter]
    [NotNull]
    private LoginModel? Model { get; set; }

    private Task OnSubmit(EditContext context)
    {
        StateHasChanged();
        return Task.CompletedTask;
    }
}
