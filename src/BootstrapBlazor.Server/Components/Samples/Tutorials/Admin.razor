@page "/tutorials/admin"
@inject IOptions<WebsiteOptions> WebsiteOption

<p>后台权限管理演示系统</p>

<div class="row g-2">
    <div class="col-12 col-sm-6">
        <a href="https://pro.blazor.zone" target="_blank">
            <div class="card">
                <div class="card-header">BootstrapBlazor 组件库实现</div>
                <div class="card-body">
                    <img src="@WebsiteOption.Value.GetAssetUrl("images/tutorials/pro.jpg")" class="w-100" />
                </div>
            </div>
        </a>
    </div>
    <div class="col-12 col-sm-6">
        <a href="https://admin.blazor.zone" target="_blank">
            <div class="card">
                <div class="card-header">MVC & Blazor 混合实现</div>
                <div class="card-body">
                    <img src="@WebsiteOption.Value.GetAssetUrl("images/tutorials/admin.jpg")" class="w-100" />
                </div>
            </div>
        </a>
    </div>
</div>
