@page "/message"
@inject IStringLocalizer<Messages> Localizer
@inject MessageService MessageService

<h3>@Localizer["MessagesTitle"]</h3>
<h4>@Localizer["MessagesDescription"]</h4>

<p>@Localizer["MessagesIntro"]</p>

<p class="code-label">@((MarkupString)Localizer["MessagesTips1"].Value)</p>
<Pre>@@inject MessageService MessageService</Pre>
<Pre>[Inject]
[NotNull]
private MessageService? MessageService { get; set; }
</Pre>
<p class="code-label">@((MarkupString)Localizer["MessagesTips2"].Value)</p>
<Pre>await MessageService.Show(new MessageOption()
{
    Content = "@Localizer["MessagesTips3"]"
});</Pre>

<DemoBlock Title="@Localizer["MessagesNormalTitle"]" Introduction="@Localizer["MessagesNormalIntro"]" Name="Normal">
    <button class="btn btn-primary" @onclick="@ShowMessage">@Localizer["MessagesMessagePrompt"]</button>
    <Message @ref="Message" Placement="Placement.Bottom" />
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesAsyncTitle"]" Introduction="@Localizer["MessagesAsyncIntro"]" Name="Async">
    <Button IsAsync="true" OnClick="@ShowAsyncMessage" Text="@Localizer["MessagesAsyncText"]"></Button>
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesIconTitle"]" Introduction="@Localizer["MessagesIconIntro"]" Name="Icon">
    <button class="btn btn-primary" @onclick="@ShowIconMessage">@Localizer["MessagesMessagePrompt"]</button>
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesCloseButtonTitle"]" Introduction="@Localizer["MessagesCloseButtonIntro"]" Name="CloseButton">
    <button class="btn btn-primary" @onclick="@ShowCloseMessage">@Localizer["MessagesMessagePrompt"]</button>
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesLeftBoardTitle"]" Introduction="@Localizer["MessagesLeftBoardIntro"]" Name="LeftBoard">
    <button class="btn btn-primary" @onclick="@ShowBarMessage">@Localizer["MessagesMessagePrompt"]</button>
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesDifferentColorTitle"]" Introduction="@Localizer["MessagesDifferentColorIntro"]" Name="DifferentColor">
    <div class="row g-3">
        <div class="col-6 col-sm-auto">
            <button class="btn btn-primary" @onclick="@(e => ShowColorMessage(Color.Primary))">@Localizer["MessagesDifferentColorPrimary"]</button>
        </div>
        <div class="col-6 col-sm-auto">
            <button class="btn btn-success" @onclick="@(e => ShowColorMessage(Color.Success))">@Localizer["MessagesDifferentColorSuccess"]</button>
        </div>
        <div class="col-6 col-sm-auto">
            <button class="btn btn-info" @onclick="@(e => ShowColorMessage(Color.Info))">@Localizer["MessagesDifferentColorInfo"]</button>
        </div>
        <div class="col-6 col-sm-auto">
            <button class="btn btn-danger" @onclick="@(e => ShowColorMessage(Color.Danger))">@Localizer["MessagesDifferentColorDanger"]</button>
        </div>
        <div class="col-6 col-sm-auto">
            <button class="btn btn-warning" @onclick="@(e => ShowColorMessage(Color.Warning))">@Localizer["MessagesDifferentColorWarning"]</button>
        </div>
        <div class="col-6 col-sm-auto">
            <button class="btn btn-secondary" @onclick="@(e => ShowColorMessage(Color.Secondary))">@Localizer["MessagesDifferentColorSecondary"]</button>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesPositionTitle"]" Introduction="@Localizer["MessagesPositionIntro"]" Name="Position">
    <button class="btn btn-primary" @onclick="@ShowBottomMessage">@Localizer["MessagesMessagePrompt"]</button>
    <Message @ref="Message1" Placement="Placement.Bottom" />
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesTemplateTitle"]" Introduction="@Localizer["MessagesTemplateIntro"]" Name="Template">
    <button class="btn btn-primary" @onclick="@ShowTemplateMessage">@Localizer["MessagesTemplatePrompt"]</button>
    <Message @ref="Message1" Placement="Placement.Bottom" />
</DemoBlock>

<DemoBlock Title="@Localizer["MessagesShowModeTitle"]" Introduction="@Localizer["MessagesShowModeIntro"]" Name="ShowMode">
    <button class="btn btn-primary" @onclick="@ShowLastOnlyMessage">@Localizer["MessagesShowModePrompt"]</button>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />

<AttributeTable Title="@Localizer["MessagesItem"]" Items="@GetMessageItemAttributes()" />

@code {
    private RenderFragment RenderContent =>
    @<div class="message-custom">
        <p class="text-primary">Message Header</p>
        <p>Message Body</p>
        <div class="text-warning">Message Footer</div>
    </div>;
}
