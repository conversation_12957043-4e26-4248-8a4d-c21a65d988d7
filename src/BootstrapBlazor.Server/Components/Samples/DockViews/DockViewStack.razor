@page "/dock-view/stack"
@inherits BaseDockView

<h3>@Localizer["DockViewStackTitle"]</h3>

<h4>@((MarkupString)Localizer["DockViewStackIntro"].Value)</h4>

<DockView Name="DockViewRow" class="dock-demo">
    <DockContent Type="DockContentType.Stack">
        <DockComponent Title="标签一">
            <Table TItem="DynamicObject" DynamicContext="DataTableDynamicContext"
                   IsStriped="true" IsBordered="true" IsExcel="true" ShowRefresh="false"
                   ShowDefaultButtons="false" IsFixedHeader="true">
                <DetailRowTemplate>
                    @{
                        var detailContext = GetDetailDataTableDynamicContext(context);
                    }
                    <div class="p-2 w-100">
                        <Table TItem="DynamicObject" DynamicContext="detailContext" IsStriped="true" IsBordered="true" IsExcel="true">
                        </Table>
                    </div>
                </DetailRowTemplate>
            </Table>
        </DockComponent>
        <DockComponent Title="标签二">
            <Table TItem="Foo" @bind-Items="Items"
                   IsStriped="true" IsBordered="true" IsMultipleSelect="true" IsFixedHeader="true"
                   ShowToolbar="true" ShowExtendButtons="true" ShowSkeleton="true"
                   OnAddAsync="@OnAddAsync">
                <TableColumns>
                    <TableColumn @bind-Field="@context.DateTime" Width="180" />
                    <TableColumn @bind-Field="@context.Name" />
                    <TableColumn @bind-Field="@context.Address" Width="180" TextEllipsis="true" ShowTips="true" />
                    <TableColumn @bind-Field="@context.Education" />
                    <TableColumn @bind-Field="@context.Count" />
                    <TableColumn @bind-Field="@context.Complete" />
                </TableColumns>
            </Table>
        </DockComponent>
        <DockComponent Title="标签三">
            <Table TItem="TreeFoo" IsBordered="true" IsStriped="true" HeaderStyle="@TableHeaderStyle.Light"
                   Items="@TreeItems" IsTree="true" TreeNodeConverter="@TreeNodeConverter" OnTreeExpand="@OnTreeExpand" IsFixedHeader="true">
                <TableColumns>
                    <TableColumn @bind-Field="@context.Name" Width="140" />
                    <TableColumn @bind-Field="@context.DateTime" Width="180" />
                    <TableColumn @bind-Field="@context.Address" Width="260" TextEllipsis="true" ShowTips="true" />
                    <TableColumn @bind-Field="@context.Count" Width="80" />
                </TableColumns>
            </Table>
        </DockComponent>
    </DockContent>
</DockView>
