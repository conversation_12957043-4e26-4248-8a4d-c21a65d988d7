@page "/sortable-list"
@inject IStringLocalizer<SortableLists> Localizer
@inject IStringLocalizer<Foo> FooLocalizer

<h3>@Localizer["SortableListTitle"]</h3>

<h4>@Localizer["SortableListDescription"]</h4>

<PackageTips Name="BootstrapBlazor.Sortable" />

<p>@((MarkupString)Localizer["SortableListClassTitle"].Value)</p>

<ul class="ul-demo">
    <li>@((MarkupString)Localizer["SortableListClassLi1"].Value)</li>
    <li>@((MarkupString)Localizer["SortableListClassLi2"].Value)</li>
</ul>

<Pre>.sortable-chosen {
    background-color: var(--bs-primary);
    color: var(--bs-body-bg);
}

.sortable-ghost {
    background-color: var(--bs-info);
    color: var(--bs-body-bg);
}

.sortable-swap-highlight {
    background-color: var(--bs-success);
    color: var(--bs-body-bg);
}</Pre>

<DemoBlock Title="@Localizer["SortableListNormalTitle"]"
           Introduction="@Localizer["SortableListNormalIntro"]"
           Name="Normal">
    <SortableList Option="_option1" OnUpdate="OnUpdate">
        <div class="sl-list row g-2">
            @foreach (var item in Items)
            {
                <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
            }
        </div>
    </SortableList>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListGroupTitle"]"
           Introduction="@Localizer["SortableListGroupIntro"]"
           Name="Group">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_option2" OnAdd="OnAdd1" OnUpdate="OnUpdate1" OnRemove="OnRemove1">
                <div class="sl-list row g-2">
                    @foreach (var item in Items1)
                    {
                        <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
                    }
                </div>
            </SortableList>
        </div>
        <div class="col-12 col-sm-6">
            <SortableList Option="_option2" OnAdd="OnAdd2" OnUpdate="OnUpdate2" OnRemove="OnRemove2">
                <div class="sl-list row g-2">
                    @foreach (var item in Items2)
                    {
                        <FooSortableListRightItem @key="item" Value="item"></FooSortableListRightItem>
                    }
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListCloneTitle"]"
           Introduction="@Localizer["SortableListCloneIntro"]"
           Name="Clone">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_option31">
                <div class="sl-list row g-2">
                    @foreach (var item in ItemsCloneLeft)
                    {
                        <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
                    }
                </div>
            </SortableList>
        </div>
        <div class="col-12 col-sm-6">
            <SortableList Option="_option32">
                <div class="sl-list row g-2">
                    @foreach (var item in ItemsCloneRight)
                    {
                        <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
                    }
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListDisableSortTitle"]"
           Introduction="@Localizer["SortableListDisableSortIntro"]"
           Name="Sort">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_option4">
                <div class="sl-list">
                    <div class="sl-item">1</div>
                    <div class="sl-item">2</div>
                    <div class="sl-item">3</div>
                    <div class="sl-item">4</div>
                </div>
            </SortableList>
        </div>
        <div class="col-12 col-sm-6">
            <SortableList Option="_option5">
                <div class="sl-list">
                    <div class="sl-item">5</div>
                    <div class="sl-item">6</div>
                    <div class="sl-item">7</div>
                    <div class="sl-item">8</div>
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListHandlerTitle"]"
           Introduction="@Localizer["SortableListHandlerIntro"]"
           Name="Handler">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_option6">
                <div class="sl-list">
                    <div class="sl-item"><i class="fa-solid fa-bars"></i><span>1</span></div>
                    <div class="sl-item"><i class="fa-solid fa-bars"></i><span>2</span></div>
                    <div class="sl-item"><i class="fa-solid fa-bars"></i><span>3</span></div>
                    <div class="sl-item"><i class="fa-solid fa-bars"></i><span>4</span></div>
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListFilterTitle"]"
           Introduction="@Localizer["SortableListFilterIntro"]"
           Name="Filter">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_option7">
                <div class="sl-list">
                    <div class="sl-item"><i class="fa-solid fa-bars"></i><span>1</span></div>
                    <div class="sl-item"><i class="fa-solid fa-bars"></i><span>2</span></div>
                    <div class="sl-item filter"><i class="fa-solid fa-bars"></i><span>3</span></div>
                    <div class="sl-item"><i class="fa-solid fa-bars"></i><span>4</span></div>
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListNestTitle"]"
           Introduction="@Localizer["SortableListNestIntro"]"
           Name="Nest">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_option1">
                <div class="sl-list">
                    <div class="sl-item">
                        <SortableList Option="_option1">
                            <div class="sl-list">
                                <div class="sl-item"><span>11</span></div>
                                <div class="sl-item"><span>12</span></div>
                                <div class="sl-item"><span>13</span></div>
                            </div>
                        </SortableList>
                    </div>
                    <div class="sl-item"><span>2</span></div>
                    <div class="sl-item"><span>3</span></div>
                    <div class="sl-item"><span>4</span></div>
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListTableTitle"]"
           Introduction="@Localizer["SortableListTableIntro"]"
           Name="Table">
    <SortableList Option="_optionTable" OnUpdate="OnUpdateTable">
        <Table TItem="Foo" Items="@Items" IsStriped="true" ShowLineNo="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.DateTime" Width="180" />
                <TableColumn @bind-Field="@context.Name" />
                <TableColumn @bind-Field="@context.Address" />
            </TableColumns>
        </Table>
    </SortableList>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListMultiTitle"]"
           Introduction="@Localizer["SortableListMultiIntro"]"
           Name="MultiDrag">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_optionMulti" OnUpdate="OnUpdateMultiDrag">
                <div class="sl-list row g-2">
                    @foreach (var item in ItemsMultiDrags)
                    {
                        <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
                    }
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListSwapTitle"]"
           Introduction="@Localizer["SortableListSwapIntro"]"
           Name="Swap">
    <div class="row g-2">
        <div class="col-12 col-sm-6">
            <SortableList Option="_optionSwap" OnUpdate="OnUpdateSwap">
                <div class="sl-list row g-2">
                    @foreach (var item in ItemsSwaps)
                    {
                        <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
                    }
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableListOnAddTitle"]"
           Introduction="@Localizer["SortableListOnAddIntro"]"
           Name="OnAdd">
    <div class="row g-2">
        <div class="col-12 col-sm-4">
            <SortableList Id="sl01" Option="_optionAdd" OnAdd="OnAddItems1" OnUpdate="OnUpdateItems1" OnRemove="OnRemoveItems1">
                <div class="sl-list row g-2">
                    @foreach (var item in AddItems1)
                    {
                        <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
                    }
                </div>
            </SortableList>
        </div>
        <div class="col-12 col-sm-4">
            <SortableList Id="sl02" Option="_optionAdd" OnAdd="OnAddItems2" OnUpdate="OnUpdateItems2" OnRemove="OnRemoveItems2">
                <div class="sl-list row g-2">
                    @foreach (var item in AddItems2)
                    {
                        <FooSortableListItem @key="item" Value="item"></FooSortableListItem>
                    }
                </div>
            </SortableList>
        </div>
        <div class="col-12 col-sm-4">
            <SortableList Id="sl03" Option="_optionAdd" OnAdd="OnAddItems3" OnUpdate="OnUpdateItems3" OnRemove="OnRemoveItems3">
                <div class="sl-list row g-2">
                    @foreach (var item in AddItems3)
                    {
                        <FooSortableListRightItem @key="item" Value="item"></FooSortableListRightItem>
                    }
                </div>
            </SortableList>
        </div>
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
