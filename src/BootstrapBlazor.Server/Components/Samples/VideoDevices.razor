@page "/video-device"
@inject IStringLocalizer<VideoDevices> Localizer

<h3>@Localizer["VideoDeviceTitle"]</h3>

<h4>@Localizer["VideoDeviceIntro"]</h4>

<Pre>[Inject, NotNull]
private IVideoDevice? VideoDeviceService { get; set; }</Pre>

<DemoBlock Title="@Localizer["BaseUsageTitle"]"
           Introduction="@Localizer["BaseUsageIntro"]"
           Name="Normal">
    <div class="row form-inline g-3">
        <div class="col-12">
            <div class="bb-actions">
                <Button Text="@Localizer["VideoDeviceRequestText"]" Icon="fa-solid fa-photo-film" OnClick="OnRequestDevice"></Button>
                <Button Text="@Localizer["VideoDeviceOpenText"]" Icon="fa-solid fa-play" OnClick="OnOpenVideo" IsDisabled="_isOpen || string.IsNullOrEmpty(_deviceId)"></Button>
                <Button Text="@Localizer["VideoDeviceCloseText"]" Icon="fa-solid fa-stop" OnClick="OnCloseVideo" IsDisabled="!_isOpen"></Button>
                <Button Text="@Localizer["VideoDeviceCaptureText"]" Icon="fa-solid fa-camera" OnClick="OnCapture" IsDisabled="!_isOpen"></Button>
                <Button Text="@Localizer["VideoDeviceDownloadText"]" Icon="fa-solid fa-download" OnClick="OnDownload" IsDisabled="!_isOpen"></Button>
                <Button Text="QVGA" IsDisabled="!_isOpen" OnClickWithoutRender="() => OnApply(320, 240)"></Button>
                <Button Text="VGA" IsDisabled="!_isOpen" OnClickWithoutRender="() => OnApply(640, 480)"></Button>
                <Button Text="HD" IsDisabled="!_isOpen" OnClickWithoutRender="() => OnApply(1280, 960)"></Button>
            </div>
        </div>
        <div class="col-12">
            <Select Items="@_items" @bind-Value="_deviceId" DisplayText="Devices" ShowLabel="true"></Select>
        </div>
    </div>

    <video class="bb-video" muted playsinline autoplay></video>

    @if (!string.IsNullOrEmpty(_previewUrl))
    {
        <img class="bb-image" src="@_previewUrl" />
    }
</DemoBlock>

