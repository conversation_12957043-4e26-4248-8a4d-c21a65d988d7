@page "/file-viewer"
@inject IStringLocalizer<FileViewers> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["Title"]</h3>

<PackageTips Name="BootstrapBlazor.FileViewer" />

<Tips class="mt-3">
    <p>@((MarkupString)Localizer["Tips"].Value)</p>
</Tips>

<Pre>&lt;ItemGroup&gt;
    &lt;RuntimeHostConfigurationOption Include="System.Drawing.EnableUnixSupport" Value="true" /&gt;
&lt;/ItemGroup&gt;</Pre>

<DemoBlock Title="@Localizer["BaseUsageText"]" Introduction="@Localizer["BaseUsageIntro"]" Name="Normal">
    <p>Word</p>
    <FileViewer Filename="@WordSampleFile" Height="300px" StyleString="padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);border: var(--bs-card-border-width) solid var(--bs-card-border-color);" />
    <p>Excel</p>
    <FileViewer Filename="@ExcelSampleFile" Height="300px" StyleString="padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);border: var(--bs-card-border-width) solid var(--bs-card-border-color);" />
</DemoBlock>

<DemoBlock Title="@Localizer["BaseUsageText2"]" Introduction="@Localizer["BaseUsageIntro2"]" Name="Reload">
    <div class="row g-3">
        <div class="col-6 col-sm-4 col-md-3 col-xl-2">
            <Dropdown TValue="string" Items="Items" OnSelectedItemChanged="@ChangeURL" />
        </div>
        <div class="col-6 col-sm-4 col-md-3 col-xl-5">
            <BootstrapInput @bind-Value="@Url" />
        </div>
        <div class="col-6 col-sm-4 col-md-3 col-xl-auto">
            <Button Color="Color.Primary" OnClick="Apply">加载</Button>
        </div>
    </div>

    <FileViewer @ref="fileViewer" Filename="@CombineFilename(Url)" Height="300px" StyleString="padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);border: var(--bs-card-border-width) solid var(--bs-card-border-color);" />
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
