@page "/spinner"
@inject IStringLocalizer<Spinners> Localizer

<h3>@Localizer["SpinnersTitle"]</h3>
<h4>@Localizer["SpinnersDescription"]</h4>

<p>
    <b>@Localizer["SpinnersTipsTitle"]</b>
    <br />
    @Localizer["SpinnersTips"]
</p>

<DemoBlock Title="@Localizer["SpinnersNormalTitle"]"
           Introduction="@Localizer["SpinnersNormalIntro"]"
           Name="Normal">
    <Spinner></Spinner>
</DemoBlock>

<DemoBlock Title="@Localizer["SpinnersColorTitle"]"
           Introduction="@Localizer["SpinnersColorIntro"]"
           Name="Color">
    <div class="row g-3">
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Primary"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Secondary"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Success"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Danger"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Warning"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Info"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Light"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Color="Color.Dark"></Spinner></div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SpinnersGrowingTitle"]"
           Introduction="@Localizer["SpinnersGrowingIntro"]"
           Name="Growing">
    <Spinner SpinnerType="SpinnerType.Grow"></Spinner>
</DemoBlock>

<DemoBlock Title="@Localizer["SpinnersGrowingColorTitle"]"
           Introduction="@Localizer["SpinnersGrowingColorIntro"]"
           Name="GrowingColor">
    <div class="row g-3">
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Primary"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Secondary"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Success"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Danger"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Warning"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Info"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Light"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner SpinnerType="SpinnerType.Grow" Color="Color.Dark"></Spinner></div>
    </div>

</DemoBlock>

<DemoBlock Title="@Localizer["SpinnersSizeTitle"]"
           Introduction="@Localizer["SpinnersSizeIntro"]"
           Name="Size">
    <div class="row g-3">
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Size="Size.ExtraSmall"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Size="Size.Small"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Size="Size.Medium"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Size="Size.Large"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Size="Size.ExtraLarge"></Spinner></div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto"><Spinner Size="Size.ExtraExtraLarge"></Spinner></div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SpinnersFlexTitle"]"
           Introduction="@Localizer["SpinnersFlexIntro"]"
           Name="Flex">
    <div class="d-flex justify-content-center">
        <Spinner></Spinner>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SpinnersCustomTitle"]"
           Introduction="@Localizer["SpinnersCustomIntro"]"
           Name="Custom">
    <div class="d-flex align-items-center">
        <strong>@Localizer["SpinnersCustomLoading"]</strong>
        <Spinner Class="ms-auto"></Spinner>
    </div>

</DemoBlock>

<DemoBlock Title="@Localizer["SpinnersFloatTitle"]"
           Introduction="@Localizer["SpinnersFloatIntro"]"
           Name="Float">
    <Spinner Class="float-end"></Spinner>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
