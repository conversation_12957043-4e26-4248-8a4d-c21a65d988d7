@page "/flip-clock"
@inject IStringLocalizer<FlipClocks> Localizer

<h3>@Localizer["FlipClocksTitle"]</h3>

<h4>@((MarkupString)Localizer["FlipClocksDescription"].Value)</h4>

<DemoBlock Title="@Localizer["CountText"]" Introduction="@Localizer["CountIntro"]" Name="Count">
    <FlipClock ViewMode="FlipClockViewMode.Count"></FlipClock>
</DemoBlock>

<DemoBlock Title="@Localizer["IsCountDownText"]" Introduction="@Localizer["IsCountDownIntro"]" Name="CountDown">
    @if (_isCompleted)
    {
        <section ignore>
            <div>Trigger OnCompletedAsync</div>
        </section>
    }
    <FlipClock ViewMode="FlipClockViewMode.CountDown" StartValue="@TimeSpan.FromSeconds(10)" OnCompletedAsync="OnCompletedAsync"></FlipClock>
</DemoBlock>

<DemoBlock Title="@Localizer["CustomText"]" Introduction="@Localizer["CustomIntro"]" Name="Custom">
    <section ignore>
        <GroupBox Title="@Localizer["Custom"]">
            <div class="row g-2">
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["Height"]" Width="150"></BootstrapInputGroupLabel>
                        <Slider @bind-Value="HeightValue" Max="200" Min="80"></Slider>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["FontSize"]" Width="150"></BootstrapInputGroupLabel>
                        <Slider @bind-Value="FontSizeValue" Max="50" Min="40"></Slider>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["CardHeight"]" Width="150"></BootstrapInputGroupLabel>
                        <Slider @bind-Value="CardHeightValue" Max="90" Min="60"></Slider>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["CardWidth"]" Width="150"></BootstrapInputGroupLabel>
                        <Slider @bind-Value="CardWidthValue" Max="60" Min="40"></Slider>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["CardMargin"]" Width="150"></BootstrapInputGroupLabel>
                        <Slider @bind-Value="CardMarginValue" Max="10" Min="5"></Slider>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["CardGroupMargin"]" Width="150"></BootstrapInputGroupLabel>
                        <Slider @bind-Value="CardGroupMarginValue" Max="28" Min="18"></Slider>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["ShowYear"]" Width="150"></BootstrapInputGroupLabel>
                        <Switch @bind-Value="_showYear"></Switch>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["ShowMonth"]" Width="150"></BootstrapInputGroupLabel>
                        <Switch @bind-Value="_showMonth"></Switch>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["ShowDay"]" Width="150"></BootstrapInputGroupLabel>
                        <Switch @bind-Value="_showDay"></Switch>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["ShowHour"]" Width="150"></BootstrapInputGroupLabel>
                        <Switch @bind-Value="_showHour"></Switch>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["ShowMinute"]" Width="150"></BootstrapInputGroupLabel>
                        <Switch @bind-Value="_showMinute"></Switch>
                    </BootstrapInputGroup>
                </div>
                <div class="col-12 col-sm-6 col-lg-4">
                    <BootstrapInputGroup>
                        <BootstrapInputGroupLabel DisplayText="@Localizer["ShowSecond"]" Width="150"></BootstrapInputGroupLabel>
                        <Switch @bind-Value="_showSecond"></Switch>
                    </BootstrapInputGroup>
                </div>
            </div>
        </GroupBox>
    </section>
    <FlipClock BackgroundColor="radial-gradient(ellipse at center, #ac85f1 0%, #833bf8 100%)" Height="@HeightValueString"
               FontSize="@FontSizeValueString" CardHeight="@CardHeightValueString" CardWidth="@CardWidthValueString"
               CardMargin="@CardMarginValueString" CardGroupMargin="@CardGroupMarginValueString"
               ShowYear="_showYear" ShowMonth="_showMonth" ShowDay="_showDay"
               ShowHour="_showHour" ShowMinute="_showMinute" ShowSecond="_showSecond"></FlipClock>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
