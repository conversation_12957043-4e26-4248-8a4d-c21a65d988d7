@page "/drawer"
@inject IStringLocalizer<Drawers> Localizer

<h3>@Localizer["Title"]</h3>
<h4>@Localizer["Description"]</h4>

<Tips>
    <p>@((MarkupString)Localizer["DrawerTips"].Value)</p>
</Tips>

<DemoBlock Title="@Localizer["NormalTitle"]" Introduction="@Localizer["NormalIntro"]" Name="Normal">
    <section ignore>
        <p class="d-flex flex-wrap drawer-demo">
            <RadioList TValue="SelectedItem" Items="@DrawerDirection" OnSelectedChanged="@OnStateChanged" />
        </p>
        <button type="button" class="btn btn-primary" @onclick="@(e => IsOpen = true)">@Localizer["Open"]</button>
    </section>

    <Drawer Placement="@DrawerAlign" @bind-IsOpen="@IsOpen" AllowResize="true">
        <div class="d-flex justify-content-center align-items-center flex-column" style="height: 290px;">
            <p>@Localizer["Content"]</p>
            <button type="button" class="btn btn-primary" @onclick="@(e => IsOpen = false)">@Localizer["Close"]</button>
        </div>
    </Drawer>
</DemoBlock>

<DemoBlock Title="@Localizer["PlacementTitle"]" Introduction="@Localizer["PlacementIntro"]" Name="Placement">
    <section ignore>
        <button type="button" class="btn btn-primary" @onclick="@OpenDrawer">@Localizer["Open"]</button>
    </section>
    <Drawer Placement="Placement.Left" @bind-IsOpen="@IsBackdropOpen" IsBackdrop="true">
        <p class="mt-3 text-center">
            @Localizer["PlacementContent"]
        </p>
    </Drawer>
</DemoBlock>

<DemoBlock Title="@Localizer["NoBackdropTitle"]" Introduction="@Localizer["NoBackdropIntro"]" Name="ShowBackdrop">
    <section ignore>
        <button type="button" class="btn btn-primary" @onclick="@OpenNoBackdropDrawer">@Localizer["Open"]</button>
    </section>
    <Drawer Placement="Placement.Left" @bind-IsOpen="@IsShowBackdropOpen" ShowBackdrop="false">
        <div class="d-flex justify-content-center align-items-center flex-column" style="height: 290px;">
            <p>@Localizer["Content"]</p>
            <button type="button" class="btn btn-primary" @onclick="@(e => IsShowBackdropOpen = false)">@Localizer["Close"]</button>
        </div>
    </Drawer>
</DemoBlock>

<DemoBlock Title="@Localizer["IsKeyboardTitle"]" Introduction="@Localizer["IsKeyboardIntro"]" Name="IsKeyboard">
    <section ignore>
        <button type="button" class="btn btn-primary" @onclick="@OpenKeyboardDrawer">@Localizer["Open"]</button>
    </section>
    <Drawer Placement="Placement.Left" @bind-IsOpen="@IsKeyboardOpen" IsKeyboard="true">
        <div class="d-flex justify-content-center align-items-center flex-column" style="height: 290px;">
            <p>@Localizer["Content"]</p>
            <button type="button" class="btn btn-primary" @onclick="@(e => IsKeyboardOpen = false)">@Localizer["Close"]</button>
        </div>
    </Drawer>
</DemoBlock>

<DemoBlock Title="@Localizer["BodyScrollTitle"]" Introduction="@Localizer["BodyScrollIntro"]" Name="BodyScroll">
    <section ignore>
        <button type="button" class="btn btn-primary" @onclick="@OpenBodyScrollDrawer">@Localizer["Open"]</button>
    </section>
    <Drawer Placement="Placement.Left" @bind-IsOpen="@IsBodyScrollOpen" BodyScroll="true">
        <div class="d-flex justify-content-center align-items-center flex-column" style="height: 290px;">
            <p>@Localizer["Content"]</p>
            <button type="button" class="btn btn-primary" @onclick="@(e => IsBodyScrollOpen = false)">@Localizer["Close"]</button>
        </div>
    </Drawer>
</DemoBlock>

<DemoBlock Title="@Localizer["DrawerServiceTitle"]" Introduction="@Localizer["DrawerServiceIntro"]" Name="DrawerService">
    <Button OnClickWithoutRender="@DrawerServiceShow" Text="@Localizer["Open"]"></Button>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
