@page "/auto-fill"
@inject IStringLocalizer<AutoFills> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["Title"]</h3>
<h4>@Localizer["Description"]</h4>

<DemoBlock Title="@Localizer["NormalTitle"]" Introduction="@Localizer["NormalIntro"]" Name="Normal">
    <section ignore>
        @((MarkupString)@Localizer["NormalDesc"].Value)
    </section>
    <AutoFill @bind-Value="Model1" Items="Items1"
              IsLikeMatch="true" OnGetDisplayText="OnGetDisplayText" IsSelectAllTextOnFocus="true">
        <ItemTemplate>
            <div class="d-flex">
                <div>
                    <img src="@WebsiteOption.Value.GetAvatarUrl(context.Id)" class="bb-avatar" />
                </div>
                <div class="ps-2">
                    <div>@context.Name</div>
                    <div class="bb-sub">@Foo.GetTitle(context.Id)</div>
                </div>
            </div>
        </ItemTemplate>
    </AutoFill>
    <section ignore>
        <img src="@WebsiteOption.Value.GetAvatarUrl(Model1.Id)" class="shadow" style="width: 140px; margin-bottom: 1rem; border-radius: 6px;" />
        <EditorForm Model="@Model1" RowType="RowType.Inline" ItemsPerRow="2" />
    </section>
</DemoBlock>

<DemoBlock Title="@Localizer["CustomFilterTitle"]" Introduction="@Localizer["CustomFilterIntro"]" Name="CustomFilter">
    <section ignore>@((MarkupString)Localizer["CustomFilterDesc"].Value)</section>
    <AutoFill @bind-Value="Model2" Items="Items2" OnCustomFilter="OnCustomFilter"
              OnGetDisplayText="OnGetDisplayText">
        <ItemTemplate>
            <div class="d-flex">
                <div>
                    <img src="@WebsiteOption.Value.GetAvatarUrl(context.Id)" class="bb-avatar" />
                </div>
                <div class="ps-2">
                    <div>@context.Name</div>
                    <div class="bb-sub">@Foo.GetTitle(context.Id)</div>
                </div>
            </div>
        </ItemTemplate>
    </AutoFill>
    <section ignore>
        <EditorForm Model="@Model2" RowType="RowType.Inline" ItemsPerRow="2" />
    </section>
</DemoBlock>

<DemoBlock Title="@Localizer["ShowDropdownListOnFocusTitle"]" Introduction="@Localizer["ShowDropdownListOnFocusIntro"]" Name="ShowDropdownListOnFocus">
    <section ignore>
        @((MarkupString)@Localizer["ShowDropdownListOnFocusDesc"].Value)
    </section>
    <AutoFill @bind-Value="Model3" Items="Items3" ShowDropdownListOnFocus="false" OnGetDisplayText="OnGetDisplayText">
        <ItemTemplate>
            <div class="d-flex">
                <div>
                    <img src="@WebsiteOption.Value.GetAvatarUrl(context.Id)" class="bb-avatar" />
                </div>
                <div class="ps-2">
                    <div>@context.Name</div>
                    <div class="bb-sub">@Foo.GetTitle(context.Id)</div>
                </div>
            </div>
        </ItemTemplate>
    </AutoFill>
    <section ignore>
        <EditorForm Model="@Model3" RowType="RowType.Inline" ItemsPerRow="2" />
    </section>
</DemoBlock>

<DemoBlock Title="@Localizer["IsVirtualizeTitle"]" Introduction="@Localizer["IsVirtualizeIntro"]" Name="IsVirtualize">
    <section ignore>
        <p>@((MarkupString)Localizer["IsVirtualizeDescription"].Value)</p>
        <div class="row g-3">
            <div class="col-12 col-sm-6">
                <BootstrapInputGroup>
                    <BootstrapInputGroupLabel DisplayText="IsClearable" />
                    <Checkbox @bind-Value="@_isClearable" />
                </BootstrapInputGroup>
            </div>
        </div>
    </section>

    <p class="code-label">1. 使用 OnQueryAsync 作为数据源</p>
    <div class="row mb-3">
        <div class="col-6">
            <AutoFill @bind-Value="Model4" OnQueryAsync="OnQueryAsync" OnGetDisplayText="OnGetDisplayText"
                      IsSelectAllTextOnFocus="true" IsVirtualize="true" RowHeight="58f" IsClearable="_isClearable">
                <ItemTemplate>
                    <div class="d-flex">
                        <div>
                            <img src="@WebsiteOption.Value.GetAvatarUrl(context.Id)" class="bb-avatar" />
                        </div>
                        <div class="ps-2">
                            <div>@context.Name</div>
                            <div class="bb-sub">@Foo.GetTitle(context.Id)</div>
                        </div>
                    </div>
                </ItemTemplate>
            </AutoFill>
        </div>
    </div>
    <section ignore>
        @if (Model4 != null)
        {
            <EditorForm Model="@Model4" RowType="RowType.Inline" ItemsPerRow="2"></EditorForm>
        }
    </section>

    <p class="code-label">2. 使用 Items 作为数据源</p>
    <div class="row mb-3">
        <div class="col-6">
            <AutoFill @bind-Value="Model5" Items="Items5" OnGetDisplayText="OnGetDisplayText"
                      IsSelectAllTextOnFocus="true" OnCustomFilter="OnCustomVirtulizeFilter"
                      IsVirtualize="true" RowHeight="58f" IsClearable="_isClearable">
                <ItemTemplate>
                    <div class="d-flex">
                        <div>
                            <img src="@WebsiteOption.Value.GetAvatarUrl(context.Id)" class="bb-avatar" />
                        </div>
                        <div class="ps-2">
                            <div>@context.Name</div>
                            <div class="bb-sub">@Foo.GetTitle(context.Id)</div>
                        </div>
                    </div>
                </ItemTemplate>
            </AutoFill>
        </div>
    </div>
    <section ignore>
        @if (Model5 != null)
        {
            <EditorForm Model="@Model5" RowType="RowType.Inline" ItemsPerRow="2"></EditorForm>
        }
    </section>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
