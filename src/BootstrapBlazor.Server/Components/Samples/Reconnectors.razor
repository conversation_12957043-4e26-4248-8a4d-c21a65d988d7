@page "/reconnector"
@inject IStringLocalizer<Reconnectors> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["Title"]</h3>

<h4>@Localizer["SubTitle"]</h4>

<p><b>@Localizer["Usage"]</b></p>

<p>@((MarkupString)Localizer["Step1"].Value)</p>

<Pre>@@RenderBody()

&lt;div id="blazor-error-ui"&gt;
&lt/div&gt;

// add ReconnectorOutlet component
<b><i>&lt;component type="typeof(ReconnectorOutlet)" param-AutoReconnect="true" render-mode="ServerPrerendered" /&gt;</i></b></Pre>

<p>组件参数 <code>AutoReconnect</code> 用于控制是否开启网络重连，默认 <code>true</code> 已开启，可以通过 <code>param-AutoReconnect="false"</code> 关闭</p>

<p>@((MarkupString)Localizer["Step2"].Value)</p>

<Pre>&lt;Reconnector&gt;
    &lt;ReconnectingTemplate&gt;
        &lt;p&gt;连接出现了一点小问题，需要重新连接。&lt;/p&gt;
        &lt;p&gt;重新连接中，请稍后... 也可以立即 &lt;a href="javascript:location.reload()"&gt;重新加载&lt;/a&gt;&lt;/p&gt;
    &lt;/ReconnectingTemplate&gt;
    &lt;ReconnectFailedTemplate&gt;
        &lt;p&gt;连接失败了，请确认网络是否正常。&lt;/p&gt;
        &lt;p&gt;
            如果网络正常，你可以&lt;a href="javascript:window.Blazor.reconnect()"&gt;重新连接&lt;/a&gt;
            或者立即 &lt;a href="javascript:location.reload()"&gt;重新加载&lt;/a&gt;
        &lt;/p&gt;
    &lt;/ReconnectFailedTemplate&gt;
    &lt;ReconnectRejectedTemplate&gt;
        &lt;p&gt;所有的连接尝试都被拒绝了，这很有可能是由于网络问题或者服务器问题引起的。&lt;/p&gt;
        &lt;p&gt;您可以尝试&lt;a href="javascript:location.reload()"&gt;重新加载&lt;/a&gt;服务器。如果是由于服务器问题引起的失败，重新连接可能不成功&lt;/p&gt;
    &lt;/ReconnectRejectedTemplate&gt;
&lt;/Reconnector&gt;</Pre>

<p>@Localizer["IntroTitle"]</p>

<ul class="ul-demo">
    <li><code>ReconnectingTemplate</code> @Localizer["ReconnectingTemplateText"]</li>
    <li><code>ReconnectFailedTemplate</code> @Localizer["ReconnectFailedTemplateText"]</li>
    <li><code>ReconnectRejectedTemplate</code> @Localizer["ReconnectRejectedTemplateText"]</li>
</ul>

<p>@((MarkupString)Localizer["Intro"].Value)</p>

<table class="table">
    <thead>
        <tr>
            <td>@Localizer["TableHeader1"]</td>
            <td>@Localizer["TableHeader2"]</td>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td><code>components-reconnect-show</code></td>
            <td>@Localizer["TableRow1"]</td>
        </tr>
        <tr>
            <td><code>components-reconnect-hide</code></td>
            <td>@Localizer["TableRow2"]</td>
        </tr>
        <tr>
            <td><code>components-reconnect-failed</code></td>
            <td>@((MarkupString)Localizer["TableRow3"].Value)</td>
        </tr>
        <tr>
            <td><code>components-reconnect-rejected</code></td>
            <td>
                <div>@((MarkupString)Localizer["TableRow4"].Value)</div>
                <ul class="ul-demo mt-2">
                    @((MarkupString)Localizer["TableRow5"].Value)
                </ul>
            </td>
        </tr>
    </tbody>
</table>

<p><b>@Localizer["Application"]</b></p>

<p>本网站使用 <code>Reconnector</code> 已实现三种状态的模板，代码如下：</p>

<p>1. <b><code>ReconnectingTemplate</code> 模板</b></p>

<div class="connection-box">
    <div class="connection-mask"></div>
    <div class="connection-body">
        <div class="row g-3">
            <div class="col-12 col-sm-5 d-none d-sm-block">
                <h5>Bootstrap Blazor UI 组件库</h5>
                <div class="d-flex">
                    <div class="flex-fill">
                        <p>基于 <b>Bootstrap</b> 样式的 <b>Blazor UI</b> 组件库</p>
                        <p>适配移动端支持各种主流浏览器，适配 <b>ABP</b>，同时支持 <b>NET6/NET7/NET8/NET9</b>，交流群 <b>795206915</b></p>
                        <p>已提供项目模板方便快速上手 <a class="connection-link" href="@TemplateUrl">项目模板</a></p>
                    </div>
                    <div class="connection-body-tail d-none d-sm-block"></div>
                </div>
            </div>
            <div class="col-12 col-sm-5">
                <h5>Reconnector 组件</h5>
                <p><b>正在尝试重新连接服务器</b></p>
                <div>服务器正在更新新版本，稍等一会儿即可提供服务，或者 <kbd>F12</kbd> 打开 <b>Developer tools</b> 查看 <b>控制台</b> 是否有错误输出，请加群与管理员联系</div>
            </div>
            <div class="col-12 col-sm-2">
                <div class="d-flex align-items-center justify-content-center h-100">
                    <a href="javascript:void()" class="btn btn-primary">重新连接</a>
                </div>
            </div>
        </div>
    </div>
</div>

<Pre>&lt;ReconnectingTemplate&gt;
    &lt;div class="connection-mask"&gt;&lt;/div&gt;
    &lt;div class="connection-body"&gt;
        &lt;div class="row g-3"&gt;
            &lt;div class="col-12 col-sm-5"&gt;
                &lt;h5&gt;Bootstrap Blazor UI 组件库&lt;/h5&gt;
                &lt;div class="d-flex"&gt;
                    &lt;div class="flex-fill"&gt;
                        &lt;div class="mb-2"&gt;基于 &lt;b&gt;Bootstrap&lt;/b&gt; 样式的 &lt;b&gt;Blazor UI&lt;/b&gt; 组件库&lt;/div&gt;
                        &lt;div class="mb-2"&gt;适配移动端支持各种主流浏览器，适配 &lt;b&gt;ABP&lt;/b&gt;，同时支持 &lt;b&gt;NET6/NET7/NET8/NET9&lt;/b&gt;，交流群 &lt;b&gt;795206915&lt;/b&gt;&lt;/div&gt;
                        &lt;div&gt;已提供项目模板方便快速上手 &lt;a class="connection-link" href="@TemplateUrl"&gt;项目模板&lt;/a&gt;&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="connection-body-tail d-none d-sm-block"&gt;&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="col-12 col-sm-5"&gt;
                &lt;h5&gt;Reconnector 组件&lt;/h5&gt;
                &lt;div class="mb-2"&gt;&lt;b&gt;正在尝试重新连接服务器&lt;/b&gt;&lt;/div&gt;
                &lt;div class="mb-2"&gt;服务器正在更新新版本，稍等一会儿即可提供服务，或者 &lt;kbd&gt;F12&lt;/kbd&gt; 打开 &lt;b&gt;Developer tools&lt;/b&gt; 查看 &lt;b&gt;控制台&lt;/b&gt; 是否有错误输出，请加群与管理员联系&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="col-12 col-sm-2"&gt;
                &lt;div class="d-flex align-items-center justify-content-center h-100"&gt;
                    &lt;a href="javascript:window.Blazor.reconnect()" class="btn btn-primary"&gt;重新连接&lt;/a&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/ReconnectingTemplate&gt;</Pre>

<div class="my-2"><b>2. <code>ReconnectFailedTemplate</code> 模板</b></div>

<div class="connection-box">
    <div class="connection-mask"></div>
    <div class="connection-body">
        <div class="row g-3">
            <div class="col-12 col-sm-5 d-none d-sm-block">
                <h5>Bootstrap Blazor UI 组件库</h5>
                <div class="d-flex">
                    <div class="flex-fill">
                        <p>基于 <b>Bootstrap</b> 样式的 <b>Blazor UI</b> 组件库</p>
                        <p>适配移动端支持各种主流浏览器，适配 <b>ABP</b>，同时支持 <b>NET6/NET7/NET8</b>，交流群 <b>795206915</b></p>
                        <div>已提供项目模板方便快速上手 <a class="connection-link" href="@TemplateUrl">项目模板</a></div>
                    </div>
                    <div class="connection-body-tail d-none d-sm-block"></div>
                </div>
            </div>
            <div class="col-12 col-sm-5">
                <h5>Reconnector 组件</h5>
                <p><b>与服务器连接失败</b></p>
                <div>请确认网络是否正常，或者 <kbd>F12</kbd> 打开 <b>Developer tools</b> 查看 <b>控制台</b> 是否有错误输出，请加群与管理员联系</div>
            </div>
            <div class="col-12 col-sm-2">
                <div class="d-flex flex-column align-items-center justify-content-center h-100">
                    <a href="javascript:void()" class="btn btn-primary mb-3">重新连接</a>
                    <a href="javascript:void()" class="btn btn-info">重新加载</a>
                </div>
            </div>
        </div>
    </div>
</div>

<Pre>&lt;ReconnectFailedTemplate&gt;
    &lt;div class="connection-mask"&gt;&lt;/div&gt;
    &lt;div class="connection-body"&gt;
        &lt;div class="row g-3"&gt;
            &lt;div class="col-12 col-sm-5"&gt;
                &lt;h5&gt;Bootstrap Blazor UI 组件库&lt;/h5&gt;
                &lt;div class="d-flex"&gt;
                    &lt;div class="flex-fill"&gt;
                        &lt;div class="mb-2"&gt;基于 &lt;b&gt;Bootstrap&lt;/b&gt; 样式的 &lt;b&gt;Blazor UI&lt;/b&gt; 组件库&lt;/div&gt;
                        &lt;div class="mb-2"&gt;适配移动端支持各种主流浏览器，适配 &lt;b&gt;ABP&lt;/b&gt;，同时支持 &lt;b&gt;NET6/NET7/NET8/NET9&lt;/b&gt;，交流群 &lt;b&gt;795206915&lt;/b&gt;&lt;/div&gt;
                        &lt;div&gt;已提供项目模板方便快速上手 &lt;a class="connection-link" href="@TemplateUrl"&gt;项目模板&lt;/a&gt;&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="connection-body-tail d-none d-sm-block"&gt;&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="col-12 col-sm-5"&gt;
                &lt;h5&gt;Reconnector 组件&lt;/h5&gt;
                &lt;div class="mb-2"&gt;&lt;b&gt;与服务器连接失败&lt;/b&gt;&lt;/div&gt;
                &lt;div class="mb-2"&gt;请确认网络是否正常，或者 &lt;kbd&gt;F12&lt;/kbd&gt; 打开 &lt;b&gt;Developer tools&lt;/b&gt; 查看 &lt;b&gt;控制台&lt;/b&gt; 是否有错误输出，请加群与管理员联系&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="col-12 col-sm-2"&gt;
                &lt;div class="d-flex flex-column align-items-center justify-content-center h-100"&gt;
                    &lt;a href="javascript:window.Blazor.reconnect()" class="btn btn-primary mb-2"&gt;重新连接&lt;/a&gt;
                    &lt;a href="javascript:location.reload()" class="btn btn-info"&gt;重新加载&lt;/a&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/ReconnectFailedTemplate&gt;</Pre>

<div class="my-2"><b>3. <code>ReconnectRejectedTemplate</code> 模板</b></div>

<div class="connection-box">
    <div class="connection-mask"></div>
    <div class="connection-body">
        <div class="row g-3">
            <div class="col-12 col-sm-5 d-none d-sm-block">
                <h5>Bootstrap Blazor UI 组件库</h5>
                <div class="d-flex">
                    <div class="flex-fill">
                        <p>基于 <b>Bootstrap</b> 样式的 <b>Blazor UI</b> 组件库</p>
                        <p>适配移动端支持各种主流浏览器，适配 <b>ABP</b>，同时支持 <b>NET6/NET7/NET8</b>，交流群 <b>795206915</b></p>
                        <p>已提供项目模板方便快速上手 <a class="connection-link" href="@TemplateUrl">项目模板</a></p>
                    </div>
                    <div class="connection-body-tail d-none d-sm-block"></div>
                </div>
            </div>
            <div class="col-12 col-sm-5">
                <h5>Reconnector 组件</h5>
                <p><b>服务器拒绝连接</b></p>
                <div>所有的连接尝试都被拒绝了，这很有可能是由于网络问题或者服务器问题引起的，请加群与管理员联系</div>
            </div>
            <div class="col-12 col-sm-2">
                <div class="d-flex flex-column align-items-center justify-content-center h-100">
                    <a href="javascript:void()" class="btn btn-info">重新加载</a>
                </div>
            </div>
        </div>
    </div>
</div>

<Pre>&lt;ReconnectRejectedTemplate&gt;
    &lt;div class="connection-mask"&gt;&lt;/div&gt;
    &lt;div class="connection-body"&gt;
        &lt;div class="row g-3"&gt;
            &lt;div class="col-12 col-sm-5"&gt;
                &lt;h5&gt;Bootstrap Blazor UI 组件库&lt;/h5&gt;
                &lt;div class="d-flex"&gt;
                    &lt;div class="flex-fill"&gt;
                        &lt;div class="mb-2"&gt;基于 &lt;b&gt;Bootstrap&lt;/b&gt; 样式的 &lt;b&gt;Blazor UI&lt;/b&gt; 组件库&lt;/div&gt;
                        &lt;div class="mb-2"&gt;适配移动端支持各种主流浏览器，适配 &lt;b&gt;ABP&lt;/b&gt;，同时支持 &lt;b&gt;NET6/NET7/NET8/NET9&lt;/b&gt;，交流群 &lt;b&gt;795206915&lt;/b&gt;&lt;/div&gt;
                        &lt;div&gt;已提供项目模板方便快速上手 &lt;a class="connection-link" href="@TemplateUrl"&gt;项目模板&lt;/a&gt;&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="connection-body-tail d-none d-sm-block"&gt;&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="col-12 col-sm-5"&gt;
                &lt;h5&gt;Reconnector 组件&lt;/h5&gt;
                &lt;div class="mb-2"&gt;&lt;b&gt;服务器拒绝连接&lt;/b&gt;&lt;/div&gt;
                &lt;div class="mb-2"&gt;所有的连接尝试都被拒绝了，这很有可能是由于网络问题或者服务器问题引起的，请加群与管理员联系&lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="col-12 col-sm-2"&gt;
                &lt;div class="d-flex flex-column align-items-center justify-content-center h-100"&gt;
                    &lt;a href="javascript:location.reload()" class="btn btn-info"&gt;重新加载&lt;/a&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/ReconnectRejectedTemplate&gt;</Pre>

<AttributeTable Items="@GetAttributes()" />
