@page "/file-icon"

<h3>@Localizer["Title"]</h3>
<h4>@Localizer["Intro"]</h4>

<DemoBlock Title="@Localizer["BaseUsageTitle"]" Introduction="@Localizer["BaseUsageIntro"]" Name="Normal">
    <div class="row row-cols-4 g-2">
        <div class="col">
            <FileIcon Extension=".xlsx" />
        </div>
        <div class="col">
            <FileIcon Extension=".docx" />
        </div>
        <div class="col">
            <FileIcon Extension=".pptx" />
        </div>
        <div class="col">
            <FileIcon Extension=".pdf" />
        </div>
        <div class="col">
            <FileIcon Extension=".mp4" />
        </div>
        <div class="col">
            <FileIcon Extension=".png" />
        </div>
        <div class="col">
            <FileIcon Extension=".jpeg" />
        </div>
        <div class="col">
            <FileIcon Extension=".cs" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ColorTitle"]" Introduction="@Localizer["ColorIntro"]" Name="IconColor">
    <div class="row row-cols-4 g-2">
        <div class="col">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" />
        </div>
        <div class="col">
            <FileIcon Extension=".docx" IconColor="Color.Primary" />
        </div>
        <div class="col">
            <FileIcon Extension=".pptx" IconColor="Color.Danger" />
        </div>
        <div class="col">
            <FileIcon Extension=".pdf" IconColor="Color.Warning" />
        </div>
        <div class="col">
            <FileIcon Extension=".mp4" IconColor="Color.Info" />
        </div>
        <div class="col">
            <FileIcon Extension=".png" IconColor="Color.Secondary" />
        </div>
        <div class="col">
            <FileIcon Extension=".jpeg" IconColor="Color.Dark" />
        </div>
        <div class="col">
            <FileIcon Extension=".cs" IconColor="Color.Light" />
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["BackgroundTemplateTitle"]" Introduction="@Localizer["BackgroundTemplateIntro"]" Name="BackgroundTemplate">
    <div class="row row-cols-4 g-2">
        <div class="col">
            <FileIcon Extension=".xlsx" IconColor="Color.Success">
                <BackgroundTemplate>
                    <i class="fa-regular fa-clipboard fa-4x" />
                </BackgroundTemplate>
            </FileIcon>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["CustomCssTitle"]" Introduction="@Localizer["CustomCssIntro"]" Name="CustomClass">
    <div class="row row-cols-4 g-2">
        <div class="col">
            <FileIcon class="custom-icon" Extension=".xlsx" IconColor="Color.Success" />
        </div>
        <div class="col">
            <FileIcon class="custom-icon" Extension=".docx" IconColor="Color.Primary" />
        </div>
        <div class="col">
            <FileIcon class="custom-icon" Extension=".pptx" IconColor="Color.Danger" />
        </div>
        <div class="col">
            <FileIcon class="custom-icon" Extension=".pdf" IconColor="Color.Warning" />
        </div>
        <div class="col">
            <FileIcon class="custom-icon" Extension=".mp4" IconColor="Color.Info">
                <BackgroundTemplate>
                    <i class="fa-regular fa-clipboard fa-4x" />
                </BackgroundTemplate>
            </FileIcon>
        </div>
        <div class="col">
            <FileIcon class="custom-icon" Extension=".png" IconColor="Color.Secondary">
                <BackgroundTemplate>
                    <i class="fa-regular fa-clipboard fa-4x" />
                </BackgroundTemplate>
            </FileIcon>
        </div>
        <div class="col">
            <FileIcon class="custom-icon" Extension=".jpeg" IconColor="Color.Dark">
                <BackgroundTemplate>
                    <i class="fa-regular fa-clipboard fa-4x" />
                </BackgroundTemplate>
            </FileIcon>
        </div>
        <div class="col">
            <FileIcon class="custom-icon" Extension=".cs" IconColor="Color.Light">
                <BackgroundTemplate>
                    <i class="fa-regular fa-clipboard fa-4x" />
                </BackgroundTemplate>
            </FileIcon>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["SizeTitle"]" Introduction="@Localizer["SizeIntro"]" Name="Size">
    <div class="row row-cols-auto g-2">
        <div class="col">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" Size="Size.ExtraSmall">
            </FileIcon>
        </div>
        <div class="col ms-3">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" Size="Size.Small">
            </FileIcon>
        </div>
        <div class="col ms-3">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" Size="Size.None">
            </FileIcon>
        </div>
        <div class="col ms-3">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" Size="Size.Medium">
            </FileIcon>
        </div>
        <div class="col ms-3">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" Size="Size.Large">
            </FileIcon>
        </div>
        <div class="col ms-3">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" Size="Size.ExtraLarge">
            </FileIcon>
        </div>
        <div class="col ms-3">
            <FileIcon Extension=".xlsx" IconColor="Color.Success" Size="Size.ExtraExtraLarge">
            </FileIcon>
        </div>
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
