@page "/client"
@inject IStringLocalizer<Client> Localizer

<h3>@Localizer["Title"]</h3>

<h4>@Localizer["SubTitle"]</h4>

<DemoBlock Title="@Localizer["BasicUsageTitle"]" Introduction="@Localizer["BasicUsageIntro"]" Name="Normal">
    <section ignore>
        <p>@Localizer["BasicUsageP1"]</p>
        <p class="code-label">@((MarkupString)Localizer["BasicUsageP2"].Value)</p>
        <Pre>public void Configure(IApplicationBuilder app)
{
    // ...
    // 增加下面这一行
    // add this line
    app.UseBootstrapBlazor();
    app.UseEndpoints(endpoints =>
    {
        endpoints.MapDefaultControllerRoute();
        endpoints.MapBlazorHub();
        endpoints.MapFallbackToPage("/_Host");
    });
}</Pre>

        <Tips>
            <p>@((MarkupString)Localizer["BasicUsageTips"].Value)</p>
        </Tips>

        <p class="code-label">@((MarkupString)Localizer["BasicUsageP3"].Value)</p>
        <Pre>[Inject]
[NotNull]
private WebClientService? ClientService { get; set; }

private ClientInfo? ClientInfo { get; set; }

protected override async Task OnAfterRenderAsync(bool firstRender)
{
    await base.OnAfterRenderAsync(firstRender);

    if (firstRender)
    {
        ClientInfo = await ClientService.GetClientInfo();
        StateHasChanged();
    }
}</Pre>

        <p class="code-label">@((MarkupString)Localizer["BasicUsageP4"].Value)</p>
        <p>@((MarkupString)Localizer["LocatorsProviderOptions"].Value)</p>
        <p>@((MarkupString)Localizer["LocatorsProviderDesc1"].Value)</p>
        <Pre>{
    "BootstrapBlazorOptions": {
        "WebClientOptions": {
            "EnableIpLocator": true
    }
}</Pre>
        <p>@((MarkupString)Localizer["LocatorsProviderDesc2"].Value)</p>
        <Pre>services.AddBootstrapBlazor(op =>
{
    op.WebClientOptions.EnableIpLocator = true;
});</Pre>
        <p>@((MarkupString)Localizer["LocatorsProviderDesc3"].Value)</p>
        <Pre>services.Configure&lt;BootstrapBlazorOptions&gt;(op =>
{
    op.WebClientOptions.EnableIpLocator = true;
});</Pre>

    </section>
    <GroupBox Title="@Localizer["GroupBoxTitle"]">
        <p class="code-label">@Localizer["IpLocatorFactoryDesc"] <a href="locator" target="_blank">IpLocatorFactory</a></p>

        <div class="row g-3 form-inline">
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.Id" DisplayText="@Localizer["Id"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.RequestUrl" DisplayText="@Localizer["RequestUrl"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.Ip" DisplayText="@Localizer["Ip"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.City" DisplayText="@Localizer["City"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.OS" DisplayText="@Localizer["OS"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.Browser" DisplayText="@Localizer["Browser"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.Engine" DisplayText="@Localizer["Engine"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.Device" DisplayText="@Localizer["Device"]" ShowLabel="true" />
            </div>
            <div class="col-12 col-sm-6">
                <Display Value="_clientInfo.Language" DisplayText="@Localizer["Language"]" ShowLabel="true" />
            </div>
            <div class="col-12">
                <Display Value="_clientInfo.UserAgent" DisplayText="UserAgent" ShowLabel="true" ShowTooltip="true" />
            </div>
        </div>
    </GroupBox>
</DemoBlock>
