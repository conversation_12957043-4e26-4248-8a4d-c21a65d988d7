@page "/link-button"
@inject IOptions<WebsiteOptions> WebsiteOption
@inject IStringLocalizer<LinkButtons> Localizer

<h3>@Localizer["LinkButtonsComTitle"]</h3>

<h4>@Localizer["LinkButtonsSubTitle"]</h4>

<DemoBlock Title="@Localizer["LinkButtonTextTitle"]"
           Introduction="@Localizer["LinkButtonTextIntro"]"
           Name="Text">
    <LinkButton Text="@Localizer["LinkButtonText"]" />
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonUrlTitle"]"
           Introduction="@Localizer["LinkButtonUrlIntro"]"
           Name="Url">
    <LinkButton Text="@Localizer["LinkButtonText"]" Url="@WebsiteOption.Value.ServerUrl" />
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonTitleTitle"]"
           Introduction="@Localizer["LinkButtonTitleIntro"]"
           Name="Title">
    <p>@((MarkupString)Localizer["LinkButtonTitleDetail"].Value)</p>
    <LinkButton Text="@Localizer["LinkButtonText"]" TooltipText="@Localizer["LinkButtonTitleTooltip"]" />
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonImageTitle"]"
           Introduction="@Localizer["LinkButtonImageIntro"]"
           Name="Image">
    <LinkButton ImageUrl="@WebsiteOption.Value.GetAssetUrl("images/Argo-C.png")" />
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonIconTitle"]"
           Introduction="@Localizer["LinkButtonIconIntro"]"
           Name="Icon">
    <LinkButton Icon="fa-solid fa-font-awesome" />
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonChildTitle"]"
           Introduction="@Localizer["LinkButtonChildIntro"]"
           Name="ChildContent">
    <LinkButton>
        <i class="fa-solid fa-font-awesome"></i>
        <span>@Localizer["LinkButtonText"]</span>
    </LinkButton>
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonOnClickTitle"]"
           Introduction="@Localizer["LinkButtonOnClickIntro"]"
           Name="OnClick">
    <LinkButton Text="@Localizer["LinkButtonText"]" OnClick="@OnClick" />
    <ConsoleLogger @ref="Logger" />
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonIsDisabledTitle"]"
           Introduction="@Localizer["LinkButtonIsDisabledIntro"]"
           Name="IsDisabled">
    <div class="row g-3">
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto">
            <LinkButton IsDisabled="true" Color="Color.Primary">@Localizer["LinkButtonIsDisabledText"]</LinkButton>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonColorTitle"]"
           Introduction="@Localizer["LinkButtonColorIntro"]"
           Name="Color">
    <div class="row g-3">
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto">
            <LinkButton Color="Color.Danger">@Localizer["LinkButtonColorText"]</LinkButton>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["LinkButtonVerticalTitle"]"
           Introduction="@Localizer["LinkButtonVerticalIntro"]"
           Name="IsVertical">
    <div class="row g-3">
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto">
            <LinkButton IsVertical="true" Icon="fa-solid fa-pen" Text="@Localizer["LinkButtonVerticalText"]" />
        </div>
        <div class="col-6 col-sm-4 col-md-3 col-lg-auto">
            <LinkButton IsVertical="true" Icon="fa-solid fa-pen" Text="@Localizer["LinkButtonVerticalText"]" IsDisabled="true" />
        </div>
    </div>
</DemoBlock>

<AttributeTable Items="GetAttributes()"></AttributeTable>
