@page "/select-table"
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["Title"]</h3>

<h4>@Localizer["Intro"]</h4>

<DemoBlock Title="@Localizer["NormalTitle"]" Introduction="@Localizer["NormalIntro"]" Name="Normal">
    <section ignore>
        @((MarkupString)Localizer["NormalDesc"].Value)
    </section>
    <SelectTable TItem="Foo" @bind-Value="@_foo" OnQueryAsync="OnQueryAsync" GetTextCallback="@GetTextCallback" TableMinWidth="300" IsClearable>
        <TableColumns>
            <TableColumn @bind-Field="@context.Name"></TableColumn>
            <TableColumn @bind-Field="@context.Address"></TableColumn>
        </TableColumns>
    </SelectTable>
</DemoBlock>

<DemoBlock Title="@Localizer["ColorTitle"]" Introduction="@Localizer["ColorIntro"]" Name="Color">
    <SelectTable TItem="Foo" @bind-Value="@_colorFoo" OnQueryAsync="OnQueryAsync" GetTextCallback="@GetTextCallback" Color="Color.Info">
        <TableColumns>
            <TableColumn @bind-Field="@context.Name"></TableColumn>
            <TableColumn @bind-Field="@context.Address"></TableColumn>
        </TableColumns>
    </SelectTable>
</DemoBlock>

<DemoBlock Title="@Localizer["IsDisabledTitle"]" Introduction="@Localizer["IsDisabledIntro"]" Name="IsDisabled">
    <SelectTable TItem="Foo" @bind-Value="@_disabledFoo" OnQueryAsync="OnQueryAsync" GetTextCallback="@GetTextCallback" IsDisabled="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Name"></TableColumn>
            <TableColumn @bind-Field="@context.Address"></TableColumn>
        </TableColumns>
    </SelectTable>
</DemoBlock>

<DemoBlock Title="@Localizer["TemplateTitle"]" Introduction="@Localizer["TemplateIntro"]" Name="ValueTemplate">
    <SelectTable TItem="Foo" @bind-Value="@_templateFoo" OnQueryAsync="OnQueryAsync" GetTextCallback="@GetTextCallback" ShowAppendArrow="false">
        <TableColumns>
            <TableColumn @bind-Field="@context.Name"></TableColumn>
            <TableColumn @bind-Field="@context.Address"></TableColumn>
        </TableColumns>
        <Template>
            <div class="dropdown-item-demo">
                <div class="select-custom-header">
                    <div class="id">@context.Id</div>
                    <div class="name">@context.Name</div>
                    <Light Color="@(context.Complete ? Color.Success : Color.Warning)"></Light>
                </div>
                <Divider />
                <div class="select-custom-body">
                    <img src="@WebsiteOption.Value.GetAvatarUrl(context.Id)" class="bb-avatar" />
                    <div class="select-custom-detail">
                        <div class="d-flex">
                            <div class="flex-fill">
                                <div>@Foo.GetTitle(context.Id)</div>
                                <div class="mt-3">@context.Address</div>
                            </div>
                            <div>
                                <Circle Width="80" Value="@context.Count" Color="Color.Info" StrokeWidth="3" />
                            </div>
                        </div>
                        <BootstrapBlazor.Components.Progress Value="@context.Count"></BootstrapBlazor.Components.Progress>
                    </div>
                </div>
            </div>
        </Template>
    </SelectTable>
</DemoBlock>

<DemoBlock Title="@Localizer["ValidateFormTitle"]" Introduction="@Localizer["ValidateFormIntro"]" Name="ValidateForm">
    <ValidateForm Model="Model">
        <div class="row g-3">
            <div class="col-12">
                <SelectTable TItem="Foo" @bind-Value="@Model.Foo" OnQueryAsync="OnQueryAsync" GetTextCallback="@GetTextCallback" DisplayText="Test">
                    <TableColumns>
                        <TableColumn @bind-Field="@context.Name"></TableColumn>
                        <TableColumn @bind-Field="@context.Address"></TableColumn>
                    </TableColumns>
                </SelectTable>
            </div>
            <div class="col-12">
                <Button ButtonType="ButtonType.Submit">Submit</Button>
            </div>
        </div>
    </ValidateForm>
</DemoBlock>

<DemoBlock Title="@Localizer["SortableTitle"]" Introduction="@Localizer["SortableIntro"]" Name="Sortable">
    <SelectTable TItem="Foo" @bind-Value="@_sortableFoo" OnQueryAsync="OnFilterQueryAsync" GetTextCallback="@GetTextCallback">
        <TableColumns>
            <TableColumn @bind-Field="@context.Name" Sortable="true"></TableColumn>
            <TableColumn @bind-Field="@context.Address" Sortable="true" Filterable="true"></TableColumn>
        </TableColumns>
    </SelectTable>
</DemoBlock>

<DemoBlock Title="@Localizer["SearchTitle"]" Introduction="@Localizer["SearchIntro"]" Name="Search">
    <div class="row g-3">
        <div class="col-4">
            <SelectTable TItem="Foo" @bind-Value="_searchFoo"
                         OnQueryAsync="OnFilterQueryAsync" GetTextCallback="@GetTextCallback"
                         ShowSearch="true" IsPagination="true" PageItemsSource="PageItemsSource">
                <TableColumns>
                    <TableColumn @bind-Field="@context.Name" Sortable="true" Searchable="true" Width="100"></TableColumn>
                    <TableColumn @bind-Field="@context.Address" Sortable="true" Filterable="true" Searchable="true"></TableColumn>
                    <TableColumn @bind-Field="@context.Education" Sortable="true" Filterable="true" Searchable="true" Width="100"></TableColumn>
                </TableColumns>
            </SelectTable>
        </div>
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
