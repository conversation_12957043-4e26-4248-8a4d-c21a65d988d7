@page "/progress"
@inject IStringLocalizer<Progress> Localizer

<h3>@Localizer["ProgressTitle"]</h3>
<h4>@Localizer["ProgressDescription"]</h4>

<DemoBlock Title="@Localizer["ProgressNormalTitle"]"
           Introduction="@Localizer["ProgressNormalIntro"]"
           Name="Normal">
    <div class="row g-3">
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="0"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="25"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="50"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="75"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="100"></BootstrapBlazor.Components.Progress>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ProgressDisplayValueTitle"]"
           Introduction="@Localizer["ProgressDisplayValueIntro"]"
           Name="DisplayValue">
    <BootstrapBlazor.Components.Progress Value="25.5" IsShowValue="true"></BootstrapBlazor.Components.Progress>
</DemoBlock>

<DemoBlock Title="@Localizer["ProgressHeightTitle"]"
           Introduction="@Localizer["ProgressHeightIntro"]"
           Name="Height">
    ﻿<BootstrapBlazor.Components.Progress Value="25" IsShowValue="true" Height="32"></BootstrapBlazor.Components.Progress>
</DemoBlock>

<DemoBlock Title="@Localizer["ProgressColorTitle"]"
           Introduction="@Localizer["ProgressColorIntro"]"
           Name="Color">
    <div class="row g-3">
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="25" Color="Color.Success" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="50" Color="Color.Info" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="75" Color="Color.Warning" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress Value="100" Color="Color.Danger" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ProgressStripeTitle"]"
           Introduction="@Localizer["ProgressStripeIntro"]"
           Name="Stripe">
    <div class="row g-3">
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsStriped="true" Value="25" Color="Color.Success" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsStriped="true" Value="50" Color="Color.Info" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsStriped="true" Value="75" Color="Color.Warning" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsStriped="true" Value="100" Color="Color.Danger" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ProgressDynamicTitle"]"
           Introduction="@Localizer["ProgressDynamicIntro"]"
           Name="Dynamic">
    <div class="row g-3">
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsAnimated="true" IsStriped="true" Value="25" Color="Color.Success" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsAnimated="true" IsStriped="true" Value="50" Color="Color.Info" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsAnimated="true" IsStriped="true" Value="75" Color="Color.Warning" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsAnimated="true" IsStriped="true" Value="100" Color="Color.Danger" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ProgressBindingTitle"]"
           Introduction="@Localizer["ProgressBindingIntro"]"
           Name="Binding">
    <div class="row g-3">
        <div class="col-12">
            <BootstrapInput @bind-Value="@Value" />
        </div>
        <div class="col-12">
            <BootstrapBlazor.Components.Progress IsAnimated="true" IsStriped="true" @bind-Value="@Value" Color="Color.Danger" IsShowValue="true"></BootstrapBlazor.Components.Progress>
        </div>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["ProgressDecimalsTitle"]"
           Introduction="@Localizer["ProgressDecimalsIntro"]"
           Name="Binding">
    <BootstrapBlazor.Components.Progress @bind-Value="@Value2" IsShowValue="true" Round="2"></BootstrapBlazor.Components.Progress>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />

@code {

    private double Value { get; set; } = 75;

    private double Value2 { get; set; } = 75.34;
}
