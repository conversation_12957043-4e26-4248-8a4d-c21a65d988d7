@page "/menu"
@inject IOptions<WebsiteOptions> WebsiteOption
@inject IStringLocalizer<Menus> Localizer

<h3>@Localizer["MenusTitle"]</h3>

<h4>@Localizer["MenusDescription"]</h4>

<Tips class="mt-3">
    <p>@((MarkupString)Localizer["MenusTips1"].Value)</p>
</Tips>

<DemoBlock Title="@Localizer["MenusTopBarTitle"]"
           Introduction="@Localizer["MenusTopBarIntro"]"
           Name="TopBar">
    <Menu Items="@Items" DisableNavigation="true" OnClick="@OnClickMenu"></Menu>
    <ConsoleLogger @ref="Logger"></ConsoleLogger>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusBottomBarTitle"]"
           Introduction="@Localizer["MenusBottomBarIntro"]"
           Name="BottomBar">
    <Tips class="mb-3">
        <div>@((MarkupString)Localizer["MenusBottomBarTips"].Value)</div>
    </Tips>
    <div style="height: 120px; position: relative;">
        <div>@ClickedMenuItemText</div>
        <Menu Items="@BottomItems" DisableNavigation="true" OnClick="@OnClickBottomMenu" IsBottom="true"></Menu>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusIconTopTitle"]"
           Introduction="@Localizer["MenusIconTopIntro"]"
           Name="IconTop">
    <Menu Items="@IconItems" DisableNavigation="true"></Menu>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusLeftRightLayoutTitle"]"
           Introduction="@Localizer["MenusLeftRightLayoutIntro"]"
           Name="LeftRightLayout">
    <Menu Items="@SideMenuItems" DisableNavigation="true" IsVertical="true" OnClick="@OnClickSideMenu" style="width:220px; border-right: 1px solid #e6e6e6; padding-right: 4px;"></Menu>
    <ConsoleLogger @ref="SideLogger"></ConsoleLogger>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusIconLeftTitle"]"
           Introduction="@Localizer["MenusIconLeftIntro"]"
           Name="IconLeft">
    <Menu Items="@IconSideMenuItems" DisableNavigation="true" IsVertical="true" style="border-right: 1px solid #e6e6e6; width:220px;" />
</DemoBlock>

<DemoBlock Title="@Localizer["MenusAccordionTitle"]"
           Introduction="@Localizer["MenusAccordionIntro"]"
           Name="Accordion">
    <Menu Items="@IconSideMenuItems" DisableNavigation="true" IsVertical="true" IsAccordion="true" style="border-right: 1px solid #e6e6e6; width:220px;"></Menu>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusClickShrinkTitle"]"
           Introduction="@Localizer["MenusClickShrinkIntro"]"
           Name="ClickShrink">
    <Tips>
        <p>@(new MarkupString(Localizer["MenusClickShrinkAlertText"].Value))</p>
    </Tips>
    <p>@((MarkupString)Localizer["MenusClickShrinkDescription"].Value)</p>
    <div class="layout-menu-demo">
        <Layout IsFullSide="true" ShowFooter="true" ShowCollapseBar="true" Menus="IconSideMenuItems">
            <Header>
                <div class="ms-3">
                    @Localizer["MenusClickShrinkSpanSpan"]
                </div>
            </Header>
            <Side>
                <div class="layout-banner">
                    <img class="layout-logo" src="@WebsiteOption.Value.GetAssetUrl("images/logo.png")" />
                    <div class="layout-title">
                        <span>@Localizer["MenusClickShrinkMenuTitle"]</span>
                    </div>
                </div>
            </Side>
            <Main>
                <div style="padding: 10rem 1rem;">Main</div>
            </Main>
            <Footer>
                <div class="text-center flex-fill">
                    <a href="@WebsiteOption.Value.BootstrapAdminLink" target="_blank">Bootstrap Admin</a>
                </div>
            </Footer>
        </Layout>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusWidgetTitle"]"
           Introduction="@Localizer["MenusWidgetIntro"]"
           Name="Widget">
    <Menu Items="@WidgetIconSideMenuItems" DisableNavigation="true" IsVertical="true" style="border-right: 1px solid #e6e6e6; width:220px;"></Menu>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusCustomNodeTitle"]"
           Introduction="@Localizer["MenusCustomNodeIntro"]"
           Name="CustomNode">
    <section ignore>@((MarkupString)Localizer["MenusCustomNodeDescription"].Value)</section>
    <Menu Items="@CollapsedIconSideMenuItems" DisableNavigation="true" IsVertical="true" style="border-right: 1px solid #e6e6e6; width:220px;"></Menu>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusDynamicTitle"]"
           Introduction="@Localizer["MenusDynamicIntro"]"
           Name="Dynamic">
    <Menu Items="@DynamicSideMenuItems" DisableNavigation="true" IsVertical="true" style="border-right: 1px solid #e6e6e6; width:220px;" class="mb-3"></Menu>
    <Button Text="@Localizer["MenusDynamicButton1Text"]" OnClick="UpdateMenu"></Button>
    <Button Text="@Localizer["MenusDynamicButton2Text"]" OnClick="ResetMenu"></Button>
</DemoBlock>

<DemoBlock Title="@Localizer["MenusPartDisableTitle"]"
           Introduction="@Localizer["MenusPartDisableIntro"]"
           Name="PartDisable">
    <section ignore>@((MarkupString)Localizer["MenusPartDisableDescription1"].Value)</section>

    <Menu Items="@DisabledMenuItems" DisableNavigation="true"></Menu>
    <section ignore class="mt-3"><b>@Localizer["MenusPartDisableDescription2"]</b></section>
    <div style="width:220px; border-right: 1px solid #e6e6e6;">
        <Menu Items="@DisabledMenuItems" DisableNavigation="true" IsVertical="true"></Menu>
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
