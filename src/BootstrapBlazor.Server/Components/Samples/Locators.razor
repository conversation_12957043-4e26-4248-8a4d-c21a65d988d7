@page "/locator"

<h3>@Localizer["LocatorsTitle"]</h3>

<h4>@Localizer["LocatorsSubTitle"]</h4>

<p><b>@Localizer["LocatorsNormalDescription"]</b></p>
<p>@((MarkupString)Localizer["LocatorsNormalInjectIPLocator"].Value)</p>
<Pre>[Inject]
[NotNull]
private IIpLocatorFactory? IpLocatorFactory { get; set; }
</Pre>
<Tips>
    <div>@((MarkupString)Localizer["LocatorsNormalTipsTitle"].Value)</div>
    <div><b>@Localizer["LocatorsNormalTips1"]</b></div>
    <div>@((MarkupString)Localizer["LocatorsNormalTips2"].Value)</div>
</Tips>
<Pre>Encoding.RegisterProvider(CodePagesEncodingProvider.Instance)</Pre>
<p><b>@Localizer["LocatorsNormalExtendDescription"]</b></p>

<p><b>@Localizer["LocatorsNormalExtend1"]</b></p>
<Pre>private class CustomerLocatorProvider : DefaultIpLocatorProvider
{
    protected override async Task&lt;string?&gt; LocateByIp(string ip)
    {
        throw new NotImplementedException();
    }
}</Pre>

<p><b>@Localizer["LocatorsNormalExtend2"]</b></p>
<Pre>services.AddSingleton&lt;IIpLocatorProvider, CustomerLocatorProvider&gt;();</Pre>
<p>@((MarkupString)Localizer["LocatorsNormalCustomerLocator"].Value)</p>

<p><b>@Localizer["LocatorsNormalExtend3"]</b></p>
<Pre>var provider = IpLocatorFactory.Create(ProviderName);
Location = await provider.Locate(Ip);</Pre>

<p>@Localizer["LocatorsNormalIpTitle"]</p>
<p><code>**************</code> @Localizer["LocatorsNormalTips3"]</p>
<p><code>**************</code> @Localizer["LocatorsNormalTips4"]</p>

<DemoBlock Title="@Localizer["LocatorsNormalTitle"]" Introduction="@Localizer["LocatorsNormalIntro"]" Name="Normal">
    <section ignore>
        @((MarkupString)Localizer["LocatorsProviderDesc"].Value)
    </section>
    <div class="row g-3 form-inline">
        <div class="col-12 col-sm-6">
            <Select Items="_providers" Value="@ProviderName" OnValueChanged="OnProviderNameChanged" ShowLabel="true" DisplayText="LocatorProvider">
            </Select>
        </div>
        <div class="col-12 col-sm-6">
        </div>
        <div class="col-12 col-sm-6">
            <BootstrapInput @bind-Value="Ip" DisplayText="@Localizer["LocatorsNormalInputText"]" ShowLabel="true" />
        </div>
        <div class="col-12 col-sm-6">
            <Display Value="Location" DisplayText="@Localizer["LocatorsNormalDisplayText"]" ShowLabel="true" />
        </div>
        <div class="col-12">
            <Button Icon="fa-solid fa-location-arrow" Text="@Localizer["LocatorsNormalButtonText"]" OnClick="OnClick" />
        </div>
    </div>
</DemoBlock>
