@page "/topology"
@inherits WebSiteModuleComponentBase
@inject IStringLocalizer<Topologies> Localizer
@inject FanControllerDataService DataService
@inject SwalService SwalService
@inject IOptions<WebsiteOptions> WebsiteOption
@attribute [JSModuleAutoLoader("Samples/Topologies.razor.js", JSObjectReference = true, AutoInvokeDispose = false)]

<h3>@Localizer["TopologiesTitle"]</h3>
<h4>@((MarkupString)Localizer["TopologiesDescription"].Value)</h4>

<DemoBlock Title="@Localizer["TopologiesNormalTitle"]"
           Introduction="@Localizer["TopologiesNormalIntro"]"
           Name="Normal">
    <Button OnClickWithoutRender="OnReset" Text="Reset" />

    <div class="topology mt-3">
        <Topology @ref="TopologyElement" Content="@Content" OnBeforePushData="OnBeforePushData" IsDisableHover="true"></Topology>
    </div>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
