@page "/mouse-follower"
@inject IOptions<WebsiteOptions> WebsiteOption

<HeadContent>
    <style>
        .bb-cursor {
            color: orangered;
            height: auto;
            width: auto;
        }

        .bb-cursor-media {
            position: absolute;
            width: auto;
            height: auto;
            margin: -50px -220px 0 0;
        }

        .mouseFollower-demo:before {
            height: 400px;
            display: grid;
            align-content: center;
            text-align: center;
            font-size: 6.6vw;
            line-height: 1.1;
            will-change: transform;
            color: transparent;
            text-shadow: none;
            -webkit-text-stroke: 1px #7532F9;
            white-space: pre;
            content: "Welcome to \A BootstrapBlazor";
        }
    </style>
</HeadContent>

<h3>@Localizer["MouseFollowersTitle"]</h3>

<h4>@Localizer["MouseFollowersDescription"]</h4>

<PackageTips Name="@NugetPackageName"></PackageTips>

<DemoBlock Title="@Localizer["MouseFollowerNormalTitle"]"
           Introduction="@Localizer["MouseFollowerNormalIntro"]"
           Name="MouseFollowerNormal">
    <MouseFollower>
        <div class="mouseFollower-demo" />
    </MouseFollower>
</DemoBlock>

<DemoBlock Title="@Localizer["MouseFollowerTextTitle"]"
           Introduction="@Localizer["MouseFollowerTextIntro"]"
           Name="MouseFollowerText">
    <MouseFollower Content="Hi"
                   Options="@FollowerOptions"
                   FollowerMode="MouseFollowerMode.Text">
        <div class="mouseFollower-demo" />
    </MouseFollower>
</DemoBlock>

<DemoBlock Title="@Localizer["MouseFollowerIconTitle"]"
           Introduction="@Localizer["MouseFollowerIconIntro"]"
           Name="MouseFollowerIcon">
    <MouseFollower FollowerMode="MouseFollowerMode.Image"
                   Content="@WebsiteOption.Value.GetAssetUrl("images/performance.svg")">
        <div class="mouseFollower-demo" />
    </MouseFollower>
</DemoBlock>

<DemoBlock Title="@Localizer["MouseFollowerImageTitle"]"
           Introduction="@Localizer["MouseFollowerImageIntro"]"
           Name="MouseFollowerImage">
    <MouseFollower Options="@FollowerImageOptions"
                   FollowerMode="MouseFollowerMode.Image"
                   Content="@WebsiteOption.Value.GetAssetUrl("images/mousefollower/cat.gif")">
        <div class="mouseFollower-demo" />
    </MouseFollower>
</DemoBlock>

<DemoBlock Title="@Localizer["MouseFollowerVideoTitle"]"
           Introduction="@Localizer["MouseFollowerVideoIntro"]"
           Name="MouseFollowerVideo">
    <MouseFollower FollowerMode="MouseFollowerMode.Video"
                   Content="//vjs.zencdn.net/v/oceans.mp4">
        <div class="mouseFollower-demo" />
    </MouseFollower>
</DemoBlock>

<AttributeTable Items="@GetAttributes()" />
