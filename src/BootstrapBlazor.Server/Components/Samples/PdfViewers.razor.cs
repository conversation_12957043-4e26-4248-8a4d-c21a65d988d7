// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the Apache 2.0 License
// See the LICENSE file in the project root for more information.
// Maintainer: <PERSON><PERSON>(<EMAIL>) Website: https://www.blazor.zone

using System.ComponentModel;

namespace BootstrapBlazor.Server.Components.Samples;

/// <summary>
/// PdfViewers
/// </summary>
public partial class PdfViewers
{
    [Inject, NotNull]
    private ToastService? ToastService { get; set; }

    private bool _useGoogleDocs = false;

    private int _pageIndex = 1;

    private Task OnLoaded() => ToastService.Success("Pdf Viewer", Localizer["PdfViewerToastSuccessfulContent"]);

    private Task NotSupportCallback() => ToastService.Error("PdfViewer", Localizer["PdfViewerToastNotSupportContent"]);
}
