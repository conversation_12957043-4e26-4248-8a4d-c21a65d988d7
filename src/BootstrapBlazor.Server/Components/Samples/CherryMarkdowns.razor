@page "/cherry-markdown"
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["Header"]</h3>
<h4>@Localizer["Tip"]</h4>

<PackageTips Name="BootstrapBlazor.CherryMarkdown" />

<p>@((MarkupString)Localizer["MarkdownsNote"].Value)</p>

<Pre class="no-highlight">builder.Services.Configure&lt;HubOptions&gt;(option => option.MaximumReceiveMessageSize = null);</Pre>

<DemoBlock Title="@Localizer["NormalTitle"]" Introduction="@Localizer["NormalIntro"]" Name="Normal">
    <CherryMarkdown @bind-Value="MarkdownString" @bind-Html="HtmlString" IsSupportMath="true" style="height: 400px" />
    <div class="mt-3">
        <textarea class="form-control" rows="6" disabled="disabled">@MarkdownString</textarea>
    </div>
    <div class="mt-3">
        <textarea class="form-control" rows="6" disabled="disabled"> @HtmlString</textarea>
    </div>
</DemoBlock>

<DemoBlock Title="@Localizer["FileUploadTitle"]" Introduction="@Localizer["FileUploadIntro"]" Name="FileUpload">
    <CherryMarkdown OnFileUpload="OnFileUpload" />
</DemoBlock>

<DemoBlock Title="@Localizer["CustomTitle"]" Introduction="@Localizer["CustomIntro"]" Name="Custom">
    <CherryMarkdown ToolbarSettings="@ToolbarSettings" EditorSettings="@EditorSettings" />
</DemoBlock>

<DemoBlock Title="@Localizer["ViewTitle"]" Introduction="@Localizer["ViewIntro"]" Name="View">
    <CherryMarkdown @bind-Value="MarkdownString" @bind-Html="HtmlString" style="height: 400px" IsViewer="true" />
</DemoBlock>

<DemoBlock Title="@Localizer["ApiTitle"]" Introduction="@Localizer["ApiIntro"]" Name="Api">
    <CherryMarkdown @ref="@MarkdownElement" />
    <Button OnClick="@InsertCheckList">@Localizer["InsertCheckListButtonText"]</Button>
    <Button OnClick="@InsertPicture">@Localizer["InsertPictureButtonText"]</Button>
</DemoBlock>

<AttributeTable Items="GetAttributes()" />
