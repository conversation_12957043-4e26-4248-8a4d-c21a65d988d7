.pre-code {
    position: relative;
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    overflow: hidden;
    direction: ltr;
    width: 100%;
}

    .pre-code:not(:last-child) {
        margin-bottom: var(--bb-pre-margin-bottom);
    }

    .pre-code .loading {
        padding: .5rem 1rem;
    }

    .pre-code.loaded > pre > code {
        display: none;
    }

    .pre-code > pre {
        color: #e83e8c;
        margin-bottom: 0;
        max-height: 260px;
    }

::deep .btn-group {
    position: absolute;
    top: 0;
    right: 3rem;
}

    ::deep .btn-group .btn-primary {
        position: relative;
    }

code {
    line-height: var(--bb-code-line-height);
    font-size: 0.75rem;
    padding: 10px 65px 10px 16px;
    display: block;
    white-space: pre-wrap;
    -webkit-font-smoothing: auto;
}

.no-highlight code {
    color: var(--bs-code-color);
}
