@inject IOptions<WebsiteOptions> WebsiteOption

<Reconnector>
    <ReconnectingTemplate>
        <div class="connection-mask"></div>
        <div class="connection-body">
            <div class="row g-3">
                @RenderBootstrapBlazor
                <div class="col-12 col-sm-5">
                    <h5>Reconnector 组件</h5>
                    <p><b>正在尝试重新连接服务器</b></p>
                    <p>服务器正在更新新版本，稍等一会儿即可提供服务，或者 <kbd>F12</kbd> 打开 <b>Developer tools</b> 查看 <b>控制台</b> 是否有错误输出，请扫描左侧二维码加群与管理员联系</p>
                </div>
                <div class="col-12 col-sm-2">
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <a href="javascript:window.Blazor.reconnect()" class="btn btn-primary">重新连接</a>
                    </div>
                </div>
            </div>
        </div>
    </ReconnectingTemplate>
    <ReconnectFailedTemplate>
        <div class="connection-mask"></div>
        <div class="connection-body">
            <div class="row g-3">
                @RenderBootstrapBlazor
                <div class="col-12 col-sm-5">
                    <h5>Reconnector 组件</h5>
                    <p><b>与服务器连接失败</b></p>
                    <div>请确认网络是否正常，或者 <kbd>F12</kbd> 打开 <b>Developer tools</b> 查看 <b>控制台</b> 是否有错误输出，请扫描左侧二维码加群与管理员联系</div>
                </div>
                <div class="col-12 col-sm-2">
                    <div class="d-flex flex-column align-items-center justify-content-center h-100">
                        <a href="javascript:window.Blazor.reconnect()" class="btn btn-primary mb-3">重新连接</a>
                        <a href="javascript:location.reload()" class="btn btn-info">重新加载</a>
                    </div>
                </div>
            </div>
        </div>
    </ReconnectFailedTemplate>
    <ReconnectRejectedTemplate>
        <div class="connection-mask"></div>
        <div class="connection-body">
            <div class="row g-3">
                @RenderBootstrapBlazor
                <div class="col-12 col-sm-5">
                    <h5>Reconnector 组件</h5>
                    <p><b>服务器拒绝连接</b></p>
                    <div>所有的连接尝试都被拒绝了，这很有可能是由于网络问题或者服务器问题引起的，请扫描左侧二维码加群与管理员联系</div>
                </div>
                <div class="col-12 col-sm-2">
                    <div class="d-flex flex-column align-items-center justify-content-center h-100">
                        <a href="javascript:location.reload()" class="btn btn-info">重新加载</a>
                    </div>
                </div>
            </div>
        </div>
    </ReconnectRejectedTemplate>
</Reconnector>

@code {
    private string TemplateUrl => $"{WebsiteOption.Value.GiteeRepositoryUrl}/wikis/%E9%A1%B9%E7%9B%AE%E6%A8%A1%E6%9D%BF%E4%BD%BF%E7%94%A8%E6%95%99%E7%A8%8B";

    RenderFragment RenderBootstrapBlazor =>
    @<div class="col-12 col-sm-5">
        <h5>Bootstrap Blazor UI 组件库</h5>
        <div class="d-flex">
            <div class="flex-fill">
                <p>一套基于 <b>Bootstrap</b> 样式的企业级 <b>Blazor UI</b> 组件库，支持 Server 与 WebAssembly</p>
                <p>适配移动端支持各种主流浏览器以及移动端，适配 <b>ABP</b>，同时支持 <b>NET6/NET7/NET8/NET9</b></p>
                <p></p>
                <div>已提供项目模板方便快速上手 <a class="connection-link" href="@TemplateUrl" target="_blank">项目模板</a></div>
            </div>
            <img altt="QQ" src="@WebsiteOption.Value.GetAssetUrl("images/<EMAIL>")" alt="QQGroup" />
            <div class="connection-body-tail d-none d-sm-block"></div>
        </div>
    </div>;
}
