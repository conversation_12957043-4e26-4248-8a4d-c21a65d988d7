@using Microsoft.Extensions.DependencyInjection
@inject PackageVersionService VersionManager
@inject IStringLocalizer<InstallContent> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Title</h3>

<h5>@Localizer["P5"]</h5>
<p>@((MarkupString)Localizer["InstallByTemplate"].Value)</p>

<h5 class="code-label">@Localizer["P9"]</h5>

<p class="code-label">@Localizer["P10"]</p>
<p class="code-label">1. @Localizer["P11"]</p>
<p class="code-label">2. @Localizer["P12"]</p>
<p class="code-label">3. @Localizer["P13"] <b>Blazor App</b> @Localizer["P14"] <b>@Localizer["P15"]</b>, @Localizer["P16"] <b>Create</b></p>
<img alt="install" src="@WebsiteOption.Value.GetAssetUrl("images/create-new-application.png")" style="border-radius: 6px;" class="d-none d-sm-block" />
@ChooseTemplate

<h5>@Localizer["P17"]</h5>
<p class="code-label">1. @Localizer["P18"] <b>nuget.org</b> @Localizer["P19"] <code>BootstrapBlazor</code></p>
<p>@Localizer["P20"] <b>Manage Nuget Packages</b></p>
<Pre @key="@Version" class="no-highlight">dotnet add package BootstrapBlazor --version @Version</Pre>
<img alt="install" src="@WebsiteOption.Value.GetAssetUrl("images/manage-nuget-packages-for-server-app.png")" style="border-radius: 6px;" class="d-none d-sm-block" />

<p class="code-label">2. @Localizer["P21"]</p>
<img alt="install" src="@WebsiteOption.Value.GetAssetUrl("images/nuget_install.png")" style="width: 1000px; border-radius: 6px;" class="d-none d-sm-block" />

<p class="code-label">3. @Localizer["P22"]</p>
<p>@Localizer["P23"]</p>
@SheetTemplate
<Tips>
    <div>@((MarkupString)Localizer["Tips2"].Value)</div>
</Tips>
<Pre>&lt;head&gt;
    ...
<b>
    &lt;!-- @Localizer["P24"] !--&gt;
    &lt;link href="_content/BootstrapBlazor.FontAwesome/css/font-awesome.min.css" rel="stylesheet"&gt;
    &lt;link href="_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css" rel="stylesheet"&gt;
</b>
    ...
    &lt;link href="css/site.css" rel="stylesheet"&gt;
    &lt;link href="BlazorApp1.styles.css" rel="stylesheet"&gt;
&lt;/head&gt;</Pre>

<p class="code-label">4. @((MarkupString)Localizer["P25"].Value)</p>
@ScriptsTemplate
<Pre>&lt;body&gt;
    ...
    &lt;!-- @Localizer["P26"] !--&gt;
    <b>&lt;script src="_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"&gt;&lt;/script&gt;</b>
    ...
    &lt;script src="_framework/blazor.server.js"&gt;&lt;/script&gt;
&lt;/body&gt;</Pre>

<p class="code-label">5. @Localizer["P27"]</p>
@ServicesTemplate

<p class="code-label">6. @Localizer["P28"]</p>
<p>@Localizer["P29"] <code>_Imports.razor</code> @Localizer["P30"] <code>Razor</code> @Localizer["P31"]</p>
<Pre><b>@@using BootstrapBlazor.Components</b></Pre>

<p class="code-label">7. @((MarkupString)Localizer["AddRootText"].Value)</p>
@RootTemplate

<h5>@Localizer["P36"]</h5>
<p>@Localizer["P37"] <code>BootstrapBlazor</code> @Localizer["P38"]：</p>
<p class="code-label">1. @Localizer["P39"] <code>Button</code> @Localizer["P40"]</p>
<Pre>&lt;Button Color="Color.Primary" Icon="fa-solid fa-font-awesome" Text="@Localizer["P41"]" /&gt;</Pre>
<p class="code-label">2. @Localizer["P42"] <b>Visual studio</b> @Localizer["P43"] <kbd>F5</kbd> @Localizer["P44"]</p>
<img alt="install" src="@WebsiteOption.Value.GetAssetUrl("images/preview.png")" style="border-radius: 6px;" class="d-none d-sm-block" />
