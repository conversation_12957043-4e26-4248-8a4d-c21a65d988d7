@inherits WebSiteModuleComponentBase
@attribute [JSModuleAutoLoader("Components/Pre.razor.js")]
@inject IOptions<WebsiteOptions> WebsiteOption

<div @attributes="@AdditionalAttributes" class="@ClassString" id="@Id" data-bb-title="@CopiedText">
    <p class="loading">@LoadingText</p>
    @if (Loaded)
    {
        <pre class="scroll"><code>@ChildContent</code></pre>
        @if (CanCopy)
        {
            @if (ShowToolbar)
            {
                <div class="btn-group">
                    <Button TooltipPlacement="Placement.Top" TooltipText="@PlusTooltipTitle" TooltipTrigger="hover" Icon="fa-solid fa-plus" class="btn-code btn-plus"></Button>
                    <Button TooltipPlacement="Placement.Top" TooltipText="@MinusTooltipTitle" TooltipTrigger="hover" Icon="fa-solid fa-minus" class="btn-code btn-minus"></Button>
                </div>
            }
            <Button TooltipPlacement="Placement.Top" TooltipText="@TooltipTitle" TooltipTrigger="hover" class="btn-code btn-copy">Copy</Button>
        }
    }
</div>
