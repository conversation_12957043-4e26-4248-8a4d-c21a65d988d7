@inherits WebSiteModuleComponentBase
@inject IStringLocalizer<UpdateIntro> Localizer
@attribute [JSModuleAutoLoader("Components/UpdateIntro.razor.js")]

<div class="blazor-intro" id="@Id" data-bb-button=".blazor-intro-button" data-bb-version="@PackageVersionService.Version">
    <h5><b>Bootstrap Blazor</b> @Localizer["H1"] <span class="version">@PackageVersionService.Version</span></h5>
    <div class="d-flex">
        <div class="blazor-intro-body">
            <p>@Localizer["B1"] <b>Bootstrap</b> <b>Blazor</b> @Localizer["B2"] <a href="@WebsiteOption.Value.AdminUrl" target="_blank"><b>@Localizer["B3"]</b></a> @Localizer["B4"]。<b>Bootstrap Blazor</b> @((MarkupString)Localizer["B5"].Value)</p>
            <p>
                @Localizer["P1"] <span class="version">@PackageVersionService.Version</span> @Localizer["P2"] <a target="_blank" href="@UpdateLogUrl"><b>[@Localizer["P3"]]</b></a> @Localizer["P4"] <b>Star</b>
                <a class="px-2" href="@WebsiteOption.Value.GithubRepositoryUrl" target="_blank">
                    <img src="@WebsiteOption.Value.GetAssetUrl("images/git.svg")" alt="github" />
                </a>
                <a href="@WebsiteOption.Value.GiteeRepositoryUrl" target="_blank">
                    <img src="@WebsiteOption.Value.GetAssetUrl("images/gitee.svg")" alt="gitee" />
                </a>
            </p>
        </div>
        <div class="blazor-intro-barcode">
            <img src="@WebsiteOption.Value.GetAssetUrl("images/<EMAIL>")" alt="QQGroup" />
            <div>QQ 795206915</div>
        </div>
    </div>
    <div class="blazor-intro-button">
        <svg viewBox="0 0 24 24"><path d="M19.707 5.707l-1.414-1.414L12 10.586 5.707 4.293 4.293 5.707 10.586 12l-6.293 6.293 1.414 1.414L12 13.414l6.293 6.293 1.414-1.414L13.414 12l6.293-6.293z"></path></svg>
    </div>
</div>
