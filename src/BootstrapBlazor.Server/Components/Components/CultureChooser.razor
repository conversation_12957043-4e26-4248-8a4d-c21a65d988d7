@inherits BootstrapComponentBase

<div @attributes="@AdditionalAttributes" class="@ClassString">
    <span>@Label</span>
    <Select Value="@SelectedCulture" OnSelectedItemChanged="@SetCulture">
        <Options>
            @foreach (var kv in BootstrapOptions.Value.GetSupportedCultures())
            {
                <SelectOption Text="@GetDisplayName(kv)" Value="@kv.Name" />
            }
        </Options>
    </Select>
</div>
