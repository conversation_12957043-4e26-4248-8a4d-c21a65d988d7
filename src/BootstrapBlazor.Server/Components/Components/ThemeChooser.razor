@inherits WebSiteModuleComponentBase
@attribute [JSModuleAutoLoader("Components/ThemeChooser.razor.js")]
@inject IOptions<WebsiteOptions> WebsiteOption

<HeadContent>
    @foreach (var css in _currentTheme)
    {
        <link rel="stylesheet" href="@css" />
    }
</HeadContent>

<div id="@Id" class="theme" @onclick:stopPropagation>
    <PulseButton TooltipPlacement="Placement.Left" TooltipText="@Title" Color="Color.None" ImageUrl="@WebsiteOption.Value.GetAssetUrl("images/m.svg")" class="btn-fade btn-theme" />
</div>

<BootstrapBlazorRootContent>
    <div class="theme-list" @onclick:stopPropagation>
        <div class="theme-header">
            <div class="flex-fill">@HeaderText</div>
            <button class="btn-close btn-close-white" type="button" aria-label="Close"></button>
        </div>
        @foreach (var item in Themes)
        {
            <div class="@GetThemeItemClass(item)" @onclick="@(e => OnClickTheme(item))">
                @item.Text
            </div>
        }
    </div>
</BootstrapBlazorRootContent>
