@page "/install-server"
@inject IStringLocalizer<Install_Server> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<InstallContent Title="@Localizer["Title"]">
    <ChooseTemplate>
        <p class="code-label">4. @Localizer["D1"] <b>Blazor Server App</b> @Localizer["D2"] <b>Create</b></p>
        <img src="@WebsiteOption.Value.GetAssetUrl("images/choose-project-template-server-blazor.png")" style="border-radius: 6px;" class="d-none d-sm-block" />
    </ChooseTemplate>
    <SheetTemplate>
        <ul class="ul-demo">
            <li><code>~/Pages/_Layout.cshtml</code> <b>NET6/NET7</b></li>
            <li><code>App.razor</code> <b>NET8/NET9</b></li>
        </ul>
    </SheetTemplate>
    <ScriptsTemplate>
        <ul class="ul-demo">
            <li><code>~/Pages/_Layout.cshtml</code> <b>NET6/NET7</b></li>
            <li><code>App.razor</code> <b>NET8/NET9</b></li>
        </ul>
    </ScriptsTemplate>
    <ServicesTemplate>
        <ul class="ul-demo">
            <li><code>Program.cs</code> <b>NET6/NET7/NET8/NET9</b></li>
        </ul>
        <p><b>Startup.cs</b></p>
        <Pre>namespace MyBlazorAppName
{
    public class Startup
    {
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddServerSideBlazor();

            // @Localizer["CodeComment"]
            <b><i>services.AddBootstrapBlazor();</i></b>
        }
    }
}</Pre>

    <p><b>Program.cs</b></p>
    <Pre>var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddServerSideBlazor();

// @Localizer["CodeComment"]
<b><i>builder.Services.AddBootstrapBlazor();</i></b>

var app = builder.Build();
//more code may be present here</Pre>
    </ServicesTemplate>
    <RootTemplate>
        <ul class="ul-demo">
            <li><code>App.razor</code> <b>NET6/NET7</b></li>
            <li><code>MainLayout.razor</code> <b>NET8/NET9</b></li>
        </ul>

        <Pre>// NET6/NET7
&lt;BootstrapBlazorRoot&gt;
    &lt;Router&gt;&lt;Router&gt;
&lt;/BootstrapBlazorRoot&gt;
</Pre>

        <Pre>// NET8/NET9
&lt;BootstrapBlazorRoot&gt;
    &lt;@@Body
&lt;/BootstrapBlazorRoot&gt;</Pre>
    </RootTemplate>
</InstallContent>
