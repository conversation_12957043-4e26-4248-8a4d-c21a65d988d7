@page "/docs"
@page "/introduction"
@page "/components"

<h3>@Localizer["Title"]</h3>

<p>@Localizer["SubTitle"]</p>

<h3>@Localizer["UpdateTitle"]</h3>

<p>
    @((MarkupString)Localizer["UpdateLog"].Value)
    <a href="@WebsiteOption.Value.WikiUrl" target="_blank">@Localizer["UpdateLogLink"]</a>
</p>

<h3>@Localizer["LearnTitle"]</h3>

<ul class="ul-demo">
    <li><a href="https://docs.microsoft.com/zh-cn/aspnet/core/blazor/?WT.mc_id=DT-MVP-5004174" target="_blank">@Localizer["LearnLi1"]</a></li>
    <li><a href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/?WT.mc_id=DT-MVP-5004174" target="_blank">@Localizer["LearnLi2"]</a></li>
    <li class="learn-item">
        <a target="_blank" href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/2-understand-blazor-webassembly?WT.mc_id=DT-MVP-5004174">@Localizer["LearnLi3"]</a>
    </li>
    <li class="learn-item">
        <a target="_blank" href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/3-exercise-configure-enviromnent?WT.mc_id=DT-MVP-5004174">@Localizer["LearnLi4"]</a>
    </li>
    <li class="learn-item">
        <a target="_blank" href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/4-blazor-components?WT.mc_id=DT-MVP-5004174">@Localizer["LearnLi5"]</a>
    </li>
    <li class="learn-item">
        <a target="_blank" href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/5-exercise-add-component?WT.mc_id=DT-MVP-5004174">@Localizer["LearnLi6"]</a>
    </li>
    <li class="learn-item">
        <a target="_blank" href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/6-csharp-razor-binding?WT.mc_id=DT-MVP-5004174">@Localizer["LearnLi7"]</a>
    </li>
    <li class="learn-item">
        <a target="_blank" href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/7-exercise-razor-binding?WT.mc_id=DT-MVP-5004174">@Localizer["LearnLi8"]</a>
    </li>
    <li class="learn-item">
        <a target="_blank" href="https://docs.microsoft.com/zh-cn/learn/modules/build-blazor-webassembly-visual-studio-code/8-summary?WT.mc_id=DT-MVP-5004174">@Localizer["Summarize"]</a>
    </li>
</ul>

<h3>@Localizer["ProjectsShow"]</h3>

<p>@((MarkupString)Localizer["P5", LocalizerRules].Value)</p>

<p>@Localizer["ShowWebSiteTitle1"]：<a href="@($"{WebsiteOption.Value.AdminUrl}/Pages")" target="_blank">@WebsiteOption.Value.AdminUrl</a></p>

<p>@Localizer["ShowWebSiteTitle2"]：<a href="@($"{WebsiteOption.Value.AdminProUrl}")" target="_blank" class="text-success">@WebsiteOption.Value.AdminProUrl</a></p>

<h4>@Localizer["GetStarted"]</h4>

<p><a href="https://v5.bootcss.com/docs/getting-started/introduction/" target="_blank">Bootstrap @Localizer["QuickStart"]</a></p>

<h4>@Localizer["Features"]</h4>

<p>@((MarkupString)Localizer["P6"].Value)</p>

<p class="code-label">@Localizer["Advantage"]</p>

<ul class="ul-demo">
    <li>@((MarkupString)Localizer["AdvantageLi1"].Value)</li>
    <li>@((MarkupString)Localizer["AdvantageLi2"].Value)</li>
    <li>@Localizer["AdvantageLi3"]</li>
    <li>@Localizer["AdvantageLi4"]</li>
    <li>@Localizer["AdvantageLi5"]</li>
</ul>

<p class="code-label">@Localizer["Community"]</p>

<QQGroup />
