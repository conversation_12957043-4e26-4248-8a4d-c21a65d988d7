@page "/install-maui"
@inject IStringLocalizer<Install_Maui> Localizer
@inject PackageVersionService VersionManager
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Title</h3>

<h4>@Localizer["H1"]</h4>

<p>@Localizer["P1"]</p>
<ul class="ul-demo">
    <li><code>visual studio latest</code> @Localizer["P2"] </li>
    <li><code>net latest</code></li>
</ul>

<h4>@Localizer["P3"]</h4>

<h5>@Localizer["P4"]</h5>
<p class="code-label">1.Visual Studio</p>
<p class="code-label">2. @Localizer["P5"]</p>
<p class="code-label">3. @Localizer["P6"] <b>@Localizer["P7"]</b> @Localizer["P8"] <b>@Localizer["P9"]</b>, @Localizer["P10"] <b>@Localizer["P11"]</b>, <b>@Localizer["P12"]</b></p>
<img src="@WebsiteOption.Value.GetAssetUrl("images/choose-project-template-maui-blazor.png")" style="border-radius: 6px;" class="d-none d-sm-block" />

<h5>@Localizer["P13"]</h5>
<p class="code-label">@((MarkupString)Localizer["P14"].Value)</p>
<p>@Localizer["P15"] <b>Manage Nuget Packages</b></p>
<Pre @key="@Version" class="no-highlight">dotnet add package BootstrapBlazor --version @Version</Pre>
<img src="@WebsiteOption.Value.GetAssetUrl("images/manage-nuget-packages-for-maui-app.png")" style="border-radius: 6px;" class="d-none d-sm-block" />
<p class="code-label">@Localizer["P16"]</p>
<img src="@WebsiteOption.Value.GetAssetUrl("images/nuget_install.png")" style="width: 1000px; border-radius: 6px;" class="d-none d-sm-block" />
<p class="code-label">@Localizer["P17"]</p>
<p>@Localizer["P18"]</p>
<ul class="ul-demo">
    <li><code>~/wwwroot/index.html</code> </li>
</ul>
<Tips>
    <div>@((MarkupString)Localizer["P33"].Value)</div>
</Tips>
<Pre>&lt;head&gt;
    ...
<b>
    &lt;!-- @Localizer["P19"] !--&gt;
    &lt;link href="_content/BootstrapBlazor.FontAwesome/css/font-awesome.min.css" rel="stylesheet"&gt;
    &lt;link href="_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css" rel="stylesheet"&gt;
    &lt;link href="_content/BootstrapBlazor/css/motronic.min.css" rel="stylesheet"&gt;
</b>
    ...
    &lt;link href="css/app.css" rel="stylesheet"&gt;
    &lt;link href="MauiApp1.styles.css" rel="stylesheet"&gt;
&lt;/head&gt;</Pre>
<p class="code-label">@Localizer["P20"]</p>
<ul class="ul-demo">
    <li><code>~/wwwroot/index.html</code> </li>
</ul>
<Pre>&lt;body&gt;
    ...
    &lt;!-- @Localizer["P21"] !--&gt;
    <b>&lt;script src="_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"&gt;&lt;/script&gt;</b>
    ...
    &lt;script src="_framework/blazor.webview.js" autostart="false" &gt;&lt;/script&gt;
&lt;/body&gt;</Pre>
<p class="code-label">@Localizer["P22"]</p>
<ul class="ul-demo">
    <li><code>MauiProgram.cs</code></li>
</ul>
<p class="code-label">MauiProgram.cs</p>
<Pre>var builder = MauiApp.CreateBuilder();

builder.Services.AddMauiBlazorWebView();

// @Localizer["CodeComment"]
<b><i>builder.Services.AddBootstrapBlazor();</i></b>

return builder.Build();
</Pre>
<p class="code-label">@Localizer["P23"]</p>
<p>@Localizer["P24"] <code>~/_Imports.razor</code> @Localizer["P25"] <code>Razor</code> @Localizer["P26"]</p>
<Pre><b>@@using BootstrapBlazor.Components</b></Pre>

<p class="code-label">@((MarkupString)Localizer["AddRootText"].Value)</p>
<Pre>&lt;BootstrapBlazorRoot&gt;
    &lt;Router AppAssembly="@@typeof(App).Assembly"&gt;
        &lt;Found Context="routeData"&gt;
            &lt;PageTitle&gt;Title&lt;/PageTitle&gt;
            &lt;RouteView RouteData="@@routeData" DefaultLayout="@@typeof(MainLayout)" /&gt;
            &lt;FocusOnNavigate RouteData="@@routeData" Selector="h1" /&gt;
        &lt;/Found&gt;
    &lt;/Router&gt;
&lt;/BootstrapBlazorRoot&gt;</Pre>

<h5 class="mb-3">@Localizer["P31"]</h5>
<p>@((MarkupString)Localizer["P32"].Value)</p>
<p class="code-label">@Localizer["P34"] <code>Button</code> @Localizer["P35"]</p>
<Pre>&lt;Button Color="Color.Primary" Icon="fa-solid fa-font-awesome" Text="@Localizer["P36"]" /&gt;</Pre>
<p class="code-label">@Localizer["P37"] <b>Visual studio</b> @Localizer["P38"] <kbd>F5</kbd> @Localizer["P39"]</p>
<img src="@WebsiteOption.Value.GetAssetUrl("images/preview-maui.png")" style="border-radius: 6px;" class="d-none d-sm-block" />

<h4 class="code-label">@Localizer["P40"]</h4>
<p class="code-label">@Localizer["P41"] <code>NativeContent.xaml</code> @Localizer["P42"]</p>
<ul>
    <li><code>NativeContent.xaml</code></li>
</ul>
<Pre>&lt;ContentView
    x:Class="MauiApp1.Pages.NativeContent"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"&gt;
    &lt;VerticalStackLayout
        Padding="30,0"
        Spacing="25"
        VerticalOptions="Center"&gt;

        &lt;Image
            HeightRequest="200"
            HorizontalOptions="Center"
            SemanticProperties.Description="Cute dot net bot waving hi to you!"
            Source="dotnet_bot.png" /&gt;

        &lt;Label
            FontSize="32"
            HorizontalOptions="Center"
            SemanticProperties.HeadingLevel="Level1"
            Text="Hello, World!" /&gt;

        &lt;Label
            FontSize="18"
            HorizontalOptions="Center"
            SemanticProperties.Description="Welcome to dot net Multi platform App U I"
            SemanticProperties.HeadingLevel="Level2"
            Text="Welcome to .NET Multi-platform App UI" /&gt;

        &lt;Button
            x:Name="CounterBtn"
            Clicked="OnCounterClicked"
            HorizontalOptions="Center"
            SemanticProperties.Hint="Counts the number of times you click"
            Text="Click me" /&gt;

    &lt;/VerticalStackLayout&gt;
&lt;/ContentView&gt;</Pre>
<ul class="ul-demo">
    <li><code>NativeContent.xaml.cs</code></li>
</ul>
<Pre>public partial class NativeContent : ContentView
{
    public NativeContent()
    {
        InitializeComponent();
    }

    int count = 0;

    private void OnCounterClicked(object sender, EventArgs e)
    {
        count++;

        if (count == 1)
            CounterBtn.Text = $"Clicked {count} time";
        else
            CounterBtn.Text = $"Clicked {count} times";
    }
}</Pre>
<p class="code-label">@Localizer["P43"] <code>MainPage.xaml</code> @Localizer["P44"] <code>ContentPage</code> @Localizer["P45"]  <code>TabbedPage</code>@Localizer["P46"] <code>NavigationPage</code> @Localizer["P47"]  </p>
<ul class="ul-demo">
    <li><code>MainPage.xaml</code></li>
</ul>
<Pre>&lt;TabbedPage
    x:Class="MauiApp1.MainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:MauiApp1"
    xmlns:pages="clr-namespace:MauiApp1.Pages"
    BackgroundColor="{DynamicResource PageBackgroundColor}"&gt;
    &lt;ContentPage Title="Home"&gt;
        &lt;BlazorWebView HostPage="wwwroot/index.html"&gt;
            &lt;BlazorWebView.RootComponents&gt;
                &lt;RootComponent ComponentType="{x:Type pages:Index}" Selector="#app" /&gt;
            &lt;/BlazorWebView.RootComponents&gt;
        &lt;/BlazorWebView&gt;
    &lt;/ContentPage&gt;
    &lt;ContentPage Title="NativeContent"&gt;
        &lt;pages:NativeContent /&gt;
    &lt;/ContentPage&gt;
    &lt;ContentPage Title="Counter"&gt;
        &lt;BlazorWebView HostPage="wwwroot/index.html"&gt;
            &lt;BlazorWebView.RootComponents&gt;
                &lt;RootComponent ComponentType="{x:Type pages:Counter}" Selector="#app" /&gt;
            &lt;/BlazorWebView.RootComponents&gt;
        &lt;/BlazorWebView&gt;
    &lt;/ContentPage&gt;
    &lt;ContentPage Title="Weather"&gt;
        &lt;BlazorWebView HostPage="wwwroot/index.html"&gt;
            &lt;BlazorWebView.RootComponents&gt;
                &lt;RootComponent ComponentType="{x:Type pages:FetchData}" Selector="#app" /&gt;
            &lt;/BlazorWebView.RootComponents&gt;
        &lt;/BlazorWebView&gt;
    &lt;/ContentPage&gt;
&lt;/TabbedPage&gt;</Pre>
<ul class="ul-demo">
    <li><code>MainPage.xaml.cs</code></li>
</ul>
<Pre>public partial class MainPage : TabbedPage
{
    public MainPage()
    {
        InitializeComponent();
    }
}</Pre>
<p class="code-label">@Localizer["P48"] <b>Visual studio</b> @Localizer["P49"] <kbd>F5</kbd> @Localizer["P50"]</p>
<img src="@WebsiteOption.Value.GetAssetUrl("images/maui-blazor.gif")" style="border-radius: 6px;width: 320px;" class="d-none d-sm-block" />

@code
{
    private string Version { get; set; } = "latest";
    [Parameter]
    public string Title { get; set; } = "";

    protected override async Task OnInitializedAsync()
    {
        Version = await VersionManager.GetVersionAsync();
        Title = @Localizer["P51"];
    }
}
