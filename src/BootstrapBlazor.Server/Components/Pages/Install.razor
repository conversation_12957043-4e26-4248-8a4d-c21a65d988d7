@page "/install"
@inject PackageVersionService VersionManager
@inject IStringLocalizer<Install> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<h3>@Localizer["InstallTitle"]</h3>

<h4>Git</h4>

<p>@Localizer["GitP1"]</p>

<div class="row mb-sm-3">
    <div class="git col-12 col-sm-6 mb-3 mb-sm-0">
        <a class="git-fork" href="https://fork.dev/" target="_blank">
            <img alt="fork" src="@WebsiteOption.Value.GetAssetUrl("images/fork.png")" />
            <span>Fork</span>
        </a>
    </div>
    <div class="git col-12 col-sm-6 mb-3 mb-sm-0">
        <a href="https://tortoisegit.org/download/" target="_blank">
            <img alt="tortoisegit" src="@WebsiteOption.Value.GetAssetUrl("images/tortoisegit.svg")" />
        </a>
    </div>
</div>

<p>@((MarkupString)Localizer["GitP2"].Value)</p>

<h4>@Localizer["NugetInstall"]</h4>

<PackageTips Name="BootstrapBlazor" />

<h4>@Localizer["EnvBuildTitle"]</h4>

<ol class="ul-demo">
    <li>@((MarkupString)Localizer["EnvLi1"].Value)</li>
    <li>@((MarkupString)Localizer["EnvLi2"].Value)</li>
    <li>@((MarkupString)string.Format(Localizer["EnvLi3"].Value, WebsiteOption.Value.GiteeRepositoryUrl))</li>
</ol>

<Pre class="no-highlight">git clone @WebsiteOption.Value.GiteeRepositoryUrl</Pre>

@code {
    /// <summary>
    /// 获得/设置 版本号字符串
    /// </summary>
    private string Version { get; set; } = "fetching";

    /// <summary>
    /// OnInitializedAsync 方法
    /// </summary>
    /// <returns></returns>
    protected override async Task OnInitializedAsync()
    {
        Version = await VersionManager.GetVersionAsync();
    }
}
