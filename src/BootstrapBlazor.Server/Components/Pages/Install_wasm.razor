@page "/install-wasm"
@inject IStringLocalizer<Install_wasm> Localizer
@inject IOptions<WebsiteOptions> WebsiteOption

<InstallContent Title="@Localizer["Install_wasmHeading"]">
    <ChooseTemplate>
        <p class="code-label">4. @((MarkupString)Localizer["Install_wasmDescription"].Value)</p>
        <img src="@WebsiteOption.Value.GetAssetUrl("images/choose-project-template.png")" style="width: 800px; border-radius: 6px;" class="d-none d-sm-block" />
    </ChooseTemplate>
    <SheetTemplate>
        <p><code>wwwroot/index.html</code></p>
    </SheetTemplate>
    <ScriptsTemplate>
        <p><code>wwwroot/index.html</code></p>
    </ScriptsTemplate>
    <ServicesTemplate>
        <Pre>var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add&lt;App&gt;("app");
builder.RootComponents.Add&lt;HeadOutlet&gt;("head::after");

// @Localizer["Install_wasmCodeComment"]
<i>builder.Services.AddBootstrapBlazor();</i>

var host = builder.Build();

await SetCultureAsync(host);

await builder.Build().RunAsync();

static async Task SetCultureAsync(WebAssemblyHost host)
{
    // 如果 localStorage 未设置语言使用浏览器请求语言
    var jsRuntime = host.Services.GetRequiredService&lt;IJSRuntime>();
    var cultureName = await jsRuntime.GetCulture();

    if (!string.IsNullOrEmpty(cultureName))
    {
        var culture = new CultureInfo(cultureName);

        // 注意 wasm 模式此处必须使用 DefaultThreadCurrentCulture 不可以使用 CurrentCulture
        CultureInfo.DefaultThreadCurrentCulture = culture;
        CultureInfo.DefaultThreadCurrentUICulture = culture;
    }
}
</Pre>
    </ServicesTemplate>
    <RootTemplate>
        <ul class="ul-demo">
            <li><code>App.razor</code> <b>NET6/NET7/NET8/NET9</b></li>
        </ul>

        <Pre>// NET6/NET7
&lt;BootstrapBlazorRoot&gt;
    &lt;Router&gt;&lt;Router&gt;
&lt;/BootstrapBlazorRoot&gt;
</Pre>
    </RootTemplate>
</InstallContent>
