@layout HomeLayout
@page "/"
@page "/index"
@page "/home"
@inject IOptions<WebsiteOptions> WebsiteOption

<section class="bd-masthead">
    <div class="container-xxl bd-gutter">
        <div class="col-md-8 mx-auto text-center">
            <a class="d-flex flex-column flex-lg-row justify-content-center align-items-center mb-4 text-dark lh-sm text-decoration-none" href="https://github.com/dotnetcore/BootstrapBlazor?wt.mc_id=DT-MVP-5004174" rel="noopener" target="_blank">
                <span class="d-sm-inline-flex align-items-center gap-1 py-2 px-3 mb-lg-0 rounded-5 masthead-notice">
                    @Localizer["Support"]
                </span>
            </a>
            <img src="./favicon.png" alt="bootstrap-blazor" class="bb-logo d-none d-sm-block mx-auto my-5">
            <h1 class="mb-3 fw-semibold">@Localizer["Title"]</h1>
            <p class="lead mb-4">
                @Localizer["SubTitle"]
            </p>
            <div class="d-flex flex-column flex-lg-row align-items-md-stretch justify-content-md-center gap-3 mb-4">
                <a href="introduction/" class="btn btn-lg bd-btn-lg btn-bd-primary d-flex align-items-center justify-content-center fw-semibold">
                    <i class="fa-solid fa-book-open"></i>
                    <span class="ms-2">@Localizer["Docs"]</span>
                </a>
            </div>
            <p class="text-body-secondary mb-0">
                Currently <strong>@_versionString</strong>
                <span class="px-1">·</span>
                <a href="https://github.com/dotnetcore/BootstrapBlazor?wt.mc_id=DT-MVP-5004174" class="link-secondary">Download</a>
                <span class="px-1">·</span>
                <a href="https://github.com/dotnetcore/BootstrapBlazor/releases?wt.mc_id=DT-MVP-5004174" class="link-secondary text-nowrap">All releases</a>
            </p>
        </div>
    </div>
    <div class="donate">
        <h3>@Localizer["DonateH1"]</h3>
        <h5 class="d-none d-sm-block mb-3">@Localizer["DonateH2"]</h5>
        <img alt="barcode" class="barcode" src="@WebsiteOption.Value.GetAssetUrl("images/<EMAIL>")" />
    </div>
</section>
