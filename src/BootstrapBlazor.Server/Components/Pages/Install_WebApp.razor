@page "/install-webapp"
@inject PackageVersionService VersionManager
@inject IStringLocalizer<InstallContent> Localizer

<h3>Web App 模式安装教程</h3>

<h4>先决条件</h4>

<ul class="ul-demo">
    <li>一个可以编译和运行 .NET Framework 的系统</li>
    <li>.NET SDK - 我们推荐 <code>BootstrapBlazor</code> 使用的主要版本，通常是最新的 LTS 版本</li>
    <li>代码编辑器，我们推荐 <code>Jet Brains Rider</code>、<code>Visual Studio</code> 或 <code>VS Code</code></li>
</ul>

<h4>手动安装</h4>
<p>如果您已经有一个项目并希望从默认模板或工作应用程序向其中添加 <code>BootstrapBlazor</code>。</p>

<h5>1. 安装包</h5>
<p>通过 <code>NuGet</code> 包管理器找到该包或使用以下命令安装它。</p>
<Pre class="mb-3">dotnet add package BootstrapBlazor</Pre>

<h5>2. 更新 <code>_Imports.razor</code> 文件</h5>
<p><code>_Imports.razor</code> 文件并添加一下内容</p>
<Pre class="mb-3">@@using BootstrapBlazor.Components</Pre>

<h5>3. 添加字体与样式</h5>
<p><code>App.razor</code> 文件在 <code>head</code> 位置添加如下内容</p>
<Pre class="mb-3">// FontAwesome 字体图标样式 注意需要引用 BootstrapBlazor.FontAwesome 包
&lt;link rel="stylesheet" href="_content/BootstrapBlazor.FontAwesome/css/font-awesome.min.css" /&gt;
// 组件样式已集成 Bootstrap 最新版
&lt;link rel="stylesheet" href="_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css" /&gt;
// Motronic 主题可选建议添加
&lt;link rel="stylesheet" href="_content/BootstrapBlazor/css/motronic.min.css" /&gt;</Pre>

<h5>4. 添加脚本</h5>
<p><code>App.razor</code> 文件在 <code>body</code> 结尾位置添加如下内容它应与默认 blazor 脚本位于同一位置。</p>
<Pre class="mb-3">// 添加 BootstrapBlazor 脚本
&lt;script src="_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"&gt;&lt;/script&gt;</Pre>

<h5>5. 删除引用</h5>
<p><code>App.razor</code> 文件，从项目中删除 bootstrap 样式，请同时删除 bootstrap 和 open-iconic 文件夹</p>

<h5>6. 注册服务</h5>
<p><code>Program.cs</code> 文件中注入 <code>BootstrapBlazor</code> 服务</p>
<Pre class="mb-3">// 增加 BootstrapBlazor 服务
builder.Services.AddBootstrapBlazor();</Pre>

<h5>7. 添加 <code>BootstrapBlazorRoot</code> 组件</h5>
<p>根据项目情况增加 <code>BootstrapBlazorRoot</code> 组件，如 <code>MainLayout</code> 文件中，使用 <code>BootstrapBlazorRoot</code> 将原有内容包裹即可</p>
<Pre class="mb-3">&lt;BootstrapBlazorRoot&gt;
    &lt;Header&gt;&lt;/Header&gt;
    &lt;main&gt;
        @@Body
    &lt;/main&gt;
&lt;/BootstrapBlazorRoot&gt;</Pre>
