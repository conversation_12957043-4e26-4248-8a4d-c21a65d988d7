{"BootstrapBlazor.Server.Components.Pages.Online": {"SubTitle": "在线人数统计", "ConnectionTime": "连接时间", "LastBeatTime": "最后心跳时间", "Dur": "持续时间", "Ip": "IP地址", "City": "城市", "LocalHost": "本地连接", "OS": "操作系统", "Device": "设备", "Browser": "浏览器", "Language": "语言", "Engine": "引擎", "RequestUrl": "请求URL"}, "BootstrapBlazor.Server.Components.Pages.Tutorials": {"CategoryTitle": "BootstrapBlazor 实战案例", "DashboardSummary": "仪表盘 Dashboard", "LoginSummary": "登陆注册 Login&Register", "WaterfallSummary": "瀑布流图片 Waterfall", "TranslateSummary": "翻译工具 Translate", "DrawingSummary": "画图 Drawing", "AdminSummary": "中台 Admin", "OnlineSheet": "在线表格 UniverSheet", "MemorialMode": "追悼模式", "MFA": "多因子认证 MFA"}, "BootstrapBlazor.Server.Components.Components.Pre": {"LoadingText": "正在加载 ...", "TooltipTitle": "点击复制代码", "PlusTooltipTitle": "点击增加显示行数", "MinusTooltipTitle": "点击减少显示行数", "CopiedText": "复制代码成功"}, "BootstrapBlazor.Server.Components.Pages.Index": {"Support": "支持 NET6.0 & NET7.0 & NET8.0 & NET9.0", "Title": "Bootstrap Blazor UI", "SubTitle": "BootstrapBlazor 是一套基于 Bootstrap 和 Blazor 的企业级组件库，无缝整合了 Bootstrap 框架与 Blazor 技术。它提供了一整套强大的工具，使开发者能够轻松创建响应式和交互式的 Web 应用程序。", "Docs": "阅读文档", "DonateH1": "捐助", "DonateH2": "扫码捐助请作者喝一杯咖啡"}, "BootstrapBlazor.Server.Components.Layout.HomeLayout": {"FooterH1": "相关作品", "FooterLi1": "滑块验证码", "FriendLink": "友情链接", "Community": "社区", "CommunityLi1": "贡献指南", "CommunityLi2": "加入我们", "CommunityLi3": "联系方式", "Footer": "码云托管平台"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesDialog": {"TablesDialogTitle": "Table 表格在弹窗内使用", "TablesDialogDescription": "用于带层级关系的数据选择中", "TableDialogNormalTitle": "弹窗中数据联动", "TableDialogNormalIntro": "点击工具栏中的选择按钮弹出对话框选择候选数据", "TableDialogNormalDescription": "本例中展示如果通过 <code>Modal</code> 组件与 <code>Table</code> 进行联动，通过弹窗中选择数据，然后再进行编辑", "TableDialogNormalTips1": "点击 <code>选择</code> 按钮弹出对话框选择产品 <code>Product</code>", "TableDialogNormalTips2": "弹窗中选择产品后点击 <code>确定</code> 按钮关闭弹窗", "TableDialogNormalTips3": "点击 <code>编辑</code> 按钮，由于设置部分数据为只读，只能更改 <code>Count</code> 字段", "TableDialogNormalChoose": "选择", "TableDialogNormalSelectItem": "选择项目", "TableDialogNormalSure": "确定"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesDynamicObject": {"TablesDynamicObjectTitle": "Table 表格", "TablesDynamicObjectDescription": "使用 <code>IDynamicMetaObjectProvider</code> 类作为数据源", "TablesDynamicObjectMetaObjectProviderTitle": "IDynamicMetaObjectProvider 集合", "TablesDynamicObjectMetaObjectProviderIntro": "通过设置 <code>Items</code> 数据源，使用 <code>IDynamicMetaObjectProvider</code> 数据集合作为数据源", "TablesDynamicObjectIDynamicObjectTitle": "IDynamicObject 集合", "TablesDynamicObjectIDynamicObjectIntro": "通过设置 <code>Items</code> 数据源，使用 <code>IDynamicObject</code> 数据集合作为数据源"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesPages": {"TablesPagesTitle": "Table 表格", "TablesPagesDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "TablesPagePaginationTitle": "分页表格", "TablesPagePaginationIntro": "设置 <code>IsPagination</code> 显示分页组件", "TablesPageShowTopPaginationTitle": "显示在顶端", "TablesPageShowTopPaginationIntro": "设置 <code>ShowTopPagination</code> 为 <code>true</code> 是顶端显示分页组件，可通过 <code>IsAutoScrollTopWhenClickPage</code> 控制是否翻页后自动滚动到顶端，默认值为 <code>false</code> 保持滚动条位置"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesFixedColumn": {"TablesFixedColumnTitle": "固定列功能", "TablesFixedColumnDescription": "对于列数很多的数据，可以固定前后的列，横向滚动查看其它数据", "TablesFixedColumnNote": "固定列用法请尽可能的设置固定列宽度，本页面如果宽度过长，请 <kbd>F12</kbd> 人为减小可视页面宽度", "TablesFixedColHeaderTitle": "固定列头与列尾", "TablesFixedColHeaderIntro": "设置 <code>Fixed</code> 属性固定表头", "TablesFixedColHeaderDescription": "本例中设置 <code>Name</code> <code>Education</code> <code>Count</code> <code>Complete</code> 前两列和最后一列为固定列，中间各列进行水平滚动，本例中设置 <code>AllowResize=\"true\"</code> 固定列也可以调整宽度", "TablesFixedColTableHeaderTitle": "固定表头与列", "TablesFixedColTableHeaderIntro": "设置 <code>IsFixedHeader=\"true\"</code> 固定表头，设置 <code>Fixed</code> 对列进行固定", "TablesFixedExtendButtonsColumnTitle": "固定按钮操作列", "TablesFixedExtendButtonsColumnIntro": "设置 <code>FixedExtendButtonsColumn</code> 对操作列进行固定", "TablesFixedHeaderAndExtendButtonsColumnTitle": "固定表头与按钮操作列", "TablesFixedHeaderAndExtendButtonsColumnIntro": "设置 <code>IsFixedHeader=\"true\"</code> 固定表头，设置 <code>FixedMultipleColumn</code> 对选择列进行固定，设置 <code>FixedExtendButtonsColumn</code> 对操作列进行固定"}, "BootstrapBlazor.Server.Components.Samples.Tooltips": {"TooltipsTitle": "Tooltip 提示工具条", "TooltipsDescription": "提供鼠标悬停或者获得焦点后显示提示框", "TooltipsNormalTitle": "基础用法", "TooltipsNormalIntro": "常用于展示鼠标 hover 时的提示信息", "TooltipsButtonTitle": "Button 组件提示框", "TooltipsButtonIntro": "提供鼠标悬停或者获得焦点后显示提示框", "BootstrapTooltipTitle": "测试组件", "TooltipsSelectorTitle": "提示信息宿主设置", "TooltipsSelectorIntro": "通过设置 <code>Selector</code> 参数精确定位触发提示框元素", "TooltipsCustomClassTitle": "自定义样式", "TooltipsCustomClassIntro": "通过设置 <code>CustomClass</code> 参数进行自定义样式设置", "BootstrapTooltipIntro": "尝试新做一个用起来比较舒服的组件，可尝鲜使用，后期组件名字可能会更改", "BootstrapTooltipTips1": "通过 <code>BootstrapTooltip</code> 对其他组件或者 <code>HTML</code> 元素进行包裹，使其被包裹对象具有 <code>Tooltip</code> 功能", "BootstrapTooltipTips2": "本例中通过 <code>BootstrapTooltip</code> 包裹一个图标，鼠标移动到图标上时，显示预设 <code>Tooltip</code> 使用更简单快捷"}, "BootstrapBlazor.Server.Components.Samples.Toasts": {"ToastsTitle": "Toast 轻量弹窗", "ToastsSubTitle": "提供轻量级 Toast 弹窗", "ToastsDescriptionTitle": "组件使用介绍", "ToastsTips1": "1. 注入服务 <code>ToastService</code>", "ToastsTips2": "2. 调用其实例 <code>api</code>", "ToastsNormalTitle": "基础用法", "ToastsNormalIntro": "用户操作时，右下角给予适当的提示信息", "ToastsSuccess": "成功通知", "ToastsDanger": "失败通知", "ToastsInfo": "信息通知", "ToastsPreventTitle": "阻止重复", "ToastsPreventIntro": "通过设置 <code>PreventDuplicates=\"true\"</code> 重复点击下方按钮时，仅弹窗一次", "ToastsPreventText": "阻止重复", "ToastsAsyncTitle": "线程阻塞通知", "ToastsAsyncIntro": "通过设置按钮 <code>IsAsync</code> 参数，使用同一个 <code>ToastOption</code> 更新弹窗信息提示不同步骤时的信息", "ToastsAsyncDemoTitle": "异步通知", "ToastsAsyncDemoStep1Text": "正在打包文档，请稍等...", "ToastsAsyncDemoStep2Text": "打包完成，正在下载...", "ToastsAsyncDemoStep3Text": "下载完成，自动关窗", "ToastsWarning": "警告通知", "ToastsAsyncText": "线程阻塞通知示例", "ToastsCloseTitle": "Toast 手动关闭", "ToastsCloseIntro": "不会自动关闭，需要人工点击关闭按钮，可通过设置 <code>OnCloseAsync</code> 回调委托自定义关闭弹窗后事件", "ToastsCloseNotificationText": "成功通知", "ToastsPositionTitle": "Toast 显示位置", "ToastsPositionIntro": "提供设置 Toast 弹窗出现位置", "ToastsPositionDescription": "通过设置 <code>Toast</code> 组件的 <code>Placement</code> 值来设置弹窗出现的位置，默认值为 <code>BottomEnd</code> 除此之外的值均为右上角", "ToastsPositionAttentionText": "特别注意", "ToastsPositionTips": "本组件使用注入服务的形式提供功能，使用时用户体验效果非常舒适，随时随地的调用。<br/>注入服务提供以下几种快捷调用方法：", "ToastsPositionTips2": "示例如下", "ToastsPositionSaved": "保存成功", "ToastsPositionAutoClose": "保存数据成功，4 秒后自动关闭", "ToastsPositionCategory": "通知类别", "ToastsPositionBoxTitle": "通知框 Title 值", "ToastsPositionNotification": "消息通知", "ToastsPositionContent": "通知正文内容", "ToastsPositionAddsNew": "系统增加新组件啦，4 秒后自动关闭", "ToastsPositionNote1": "特别说明: 可以通过 <code>BootstrapBlazorOptions</code> 全局统一配置参数可以设置整个系统内的 <code>Toast</code> 组件 <code>Delay</code> 参数值", "ToastsPositionNote2": "通过配置文件 <code>appsetting.json</code> 文件配置，适用于 <code>Server-Side App</code>", "ToastsPositionConfigured": "通过 <code>Startup</code> 文件中的 <code>ConfigureServices</code> 方法配置，适用于 <code>Server-Side App</code> 和 <code>WebAssembly App</code>", "ToastsPositionDisappearance": "统一设置 Toast 组件自动消失时间", "ToastsPositionServerSide": "或者，仅适用于 <code>Server-Side App</code>", "ToastsPositionAddComponent": "增加 BootstrapBlazor 组件", "ToastsPositionAutomaticDisappearance": "统一设置 Toast 组件自动消失时间", "ToastsShowHeaderTitle": "不显示 Header", "ToastsShowHeaderIntro": "通过设置 <code>ShowHeader=\"false\"</code> 关闭 <code>Header</code>", "ShowHeaderText": "Toast", "ToastsHeaderTemplateTitle": "自定义标题栏", "ToastsHeaderTemplateIntro": "通过设置 <code>HeaderTemplate</code> 自定义标题栏内容", "ToastsAttrCategory": "Toast 类型", "ToastsAttrTitle": "Toast 标题", "ToastsAttrContent": "Toast 内容，支持 Html 标签", "ToastsAttrDelay": "自动关闭时间", "ToastsAttrIsAutoHide": "是否自动关闭提示框", "ToastsAttrIsHtml": "内容是否包含 HTML", "ToastsAttrPlacement": "弹窗位置"}, "BootstrapBlazor.Server.Components.Samples.Timers": {"TimersTitle": "Timer 计时器", "TimersDescription": "用于时间倒计时", "TimersNormalTitle": "基本用法", "TimersNormalIntro": "通过设置 <code>Value</code> 属性设定倒计时时间", "TimersColorTitle": "颜色", "TimersColorIntro": "通过设置 <code>Color</code> 属性设定圆形进度条颜色"}, "BootstrapBlazor.Server.Components.Samples.SweetAlerts": {"SweetAlertsTitle": "SweetAlert 弹窗组件", "SweetAlertsDescription": "模态对话框，多用于动作过程中进行询问后继续，或者显示执行结果", "SweetAlertsTipsTitle": "组件使用介绍", "SweetAlertsTips1": "1. 注入服务 <code>SwalService</code>", "SweetAlertsTips2": "2. 调用其实例 <code>api</code>", "SweetAlertsPreTitleText": "我是 Title", "SweetAlertsPreContentText": "我是 Content", "SweetAlertsNormalTitle": "基础用法", "SweetAlertsNormalIntro": "通过注入服务调用 <code>Swal</code> 来弹出一个对话框", "SweetAlertsNormalSuccess": "成功", "SweetAlertsPopups": "弹窗", "SweetAlertsNormalFail": "失败", "SweetAlertsNormalWarn": "警告", "SweetAlertsNormalHint": "提示", "SweetAlertsNormalDoubt": "疑问", "SweetAlertsDisplayTitle": "设置显示内容", "SweetAlertsDisplayIntro": "通过设置 <code>Title</code> <code>Content</code> 用于显示弹窗标题与内容", "SweetAlertsButtonTitle": "自定义按钮", "SweetAlertsButtonIntro": "通过设置 <code>ButtonTemplate</code> 自定义弹窗内按钮", "SweetAlertsComponentTitle": "显示自定义组件", "SweetAlertsComponentIntro": "通过设置 <code>Component</code> 弹窗内容为自定义组件", "SweetAlertsModalTitle": "模态对话框", "SweetAlertsModalIntro": "通过调用 <code>await SwalService.ShowModal</code> 方法弹出模态框，点击弹窗内按钮关闭弹窗后，后续代码继续执行", "SweetAlertsModalDescription": "本示例代码通过调用 <code>await SwalService.ShowModal</code> 方法弹出模态框，只有关闭弹窗后，后续代码才继续执行", "SweetAlertsModalTips": "<code>IsConfirm</code> 参数表示弹窗为确认窗口，自动生成 <code>取消</code> <code>确认</code> 两个按钮", "SweetAlertsFooterTitle": "显示 Footer 信息", "SweetAlertsFooterIntro": "通过设置 <code>FooterTemplate</code> 自定义 Footer 模板", "SweetAlertsFooterDescription": "参数 <code>ShowFooter</code> 默认为 <code>false</code> 不显示页脚模板，需要显示设置为 <code>true</code>", "SweetAlertsFooterButtonText": "带 Footer 弹窗", "SweetAlertsAutoCloseTitle": "自动关闭功能", "SweetAlertsAutoCloseIntro": "通过设置 <code>IsAutoHide</code> <code>Delay</code> 属性，自定义自动关闭时间", "SweetAlertsAutoCloseDescription": "参数 <code>IsAutoHide</code> 默认为 <code>false</code> 不启用自动关闭功能, 参数 <code>Delay</code> 默认为 4000 毫秒", "SweetAlertsAutoCloseButtonText": "自动关闭弹窗", "SweetAlertsCloseTitle": "代码关闭功能", "SweetAlertsCloseIntro": "通过调用 <code>SwalOption</code> <code>CloseAsync</code> 方法关闭弹窗", "SweetAlertsCloseDescription": "不启用自动关闭功能 <code>IsAutoHide=\"false\"</code> 并且隐藏关闭按钮 <code>ShowClose=\"false\"</code>, 可通过代码主动关闭弹窗", "SweetAlertsCloseButtonText": "代码关闭弹窗", "SwalOptionTitle": "模态对话框示例", "SwalOptionContent": "模态对话框内容，不同按钮返回不同值", "SwalConsoleInfo": "模态弹窗返回值为", "AttrCategory": "弹出框类型", "AttrTitle": "弹窗标题", "AttrContent": "弹窗内容", "AttrDelay": "自动关闭时间间隔", "AttrAutoHide": "是否自动关闭", "AttrShowClose": "是否显示关闭按钮", "AttrShowFooter": "是否显示页脚模板", "AttrIsConfirm": "是否为确认弹窗模式", "AttrBodyContext": "弹窗参数", "AttrBodyTemplate": "Body 模板", "AttrFooterTemplate": "Footer 模板", "AttrButtonTemplate": "模态按钮模板"}, "BootstrapBlazor.Server.Components.Samples.Spinners": {"SpinnersTitle": "Spinner 旋转图标", "SpinnersDescription": "加载数据时显示动效", "SpinnersTipsTitle": "何时使用", "SpinnersTips": "页面局部处于等待异步数据或正在渲染过程时，合适的加载动效会有效缓解用户的焦虑。", "SpinnersNormalTitle": "基础用法", "SpinnersNormalIntro": "默认旋转图标", "SpinnersColorTitle": "Spinner 带有颜色的旋转图标", "SpinnersColorIntro": "提供基本颜色的旋转图标", "SpinnersGrowingTitle": "Growing Spinner 旋转图标", "SpinnersGrowingIntro": "生长式旋转图标", "SpinnersGrowingColorTitle": "Growing Spinner 带有颜色的旋转图标", "SpinnersGrowingColorIntro": "提供基本颜色的旋转图标", "SpinnersSizeTitle": "Spinner 旋转图标大小", "SpinnersSizeIntro": "图标样式大小", "SpinnersFlexTitle": "Flex 布局", "SpinnersFlexIntro": "自定义布局", "SpinnersCustomTitle": "自定义标题", "SpinnersCustomIntro": "带有文字的自定义布局", "SpinnersCustomLoading": "Loading...", "SpinnersFloatTitle": "悬浮", "SpinnersFloatIntro": "使用 Float 布局"}, "BootstrapBlazor.Server.Components.Samples.Splittings": {"SplittingTitle": "Loader 加载动画", "SplittingDescription": "加载数据时显示动效", "SplittingTipsTitle": "何时使用", "SplittingTips": "页面局部处于等待异步数据或正在渲染过程时，合适的加载动效会有效缓解用户的焦虑。", "SplittingNormalTitle": "基础用法", "SplittingNormalIntro": "默认加载动画", "SplittingTextTitle": "自定义文本", "SplittingTextIntro": "修改显示的文本内容", "SplittingColorTitle": "颜色", "SplittingColorIntro": "设置 <code>Color</code> 更改进度条颜色", "SplittingColumnsTitle": "分割粒度", "SplittingColumnsIntro": "设置 <code>Columns</code> 更改进度条分割粒度", "SplittingColumnsText": "分割粒度", "SplittingText": "加载中 。。。"}, "BootstrapBlazor.Server.Components.Samples.SearchDialogs": {"SearchDialogsTitle": "SearchDialog 搜索弹窗", "SearchDialogsSubTitle": "通过绑定数据模型自动呈现搜索弹窗", "SearchDialogsDescription": "<code>SearchDialog</code> 组件是 <code>Dialog</code> 组件的扩展，适用于数据弹出窗设置搜索条件。", "SearchDialogsTips": "通过调用注入服务 <code>DialogService</code> 的 <code>ShowSearchDialog</code> 方法直接弹出搜索条件弹窗，大大减少代码量。<code>SearchDialogOption</code> 配置类继承 <code>DialogOption</code> ，更多参数设置请点击 <a href=\"dialog\" target=\"_blank\">[传送门]</a>", "SearchDialogsNormalTitle": "基础用法", "SearchDialogsNormalIntro": "通过绑定 <code>TModel</code> 数据模型，自动生成模型各个字段的搜索表单", "SearchDialogsNormalButtonText": "点击弹出搜索弹窗", "SearchDialogsFieldTitle": "自定义搜索弹窗内显示的条件字段", "SearchDialogsFieldIntro": "通过设定 <code>Columns</code> 参数显式设置显示的搜索字段", "SearchDialogsFieldButtonText": "点击弹出搜索弹窗", "SearchDialogsLayoutTitle": "设置搜索弹窗内布局方式", "SearchDialogsLayoutIntro": "通过设定 <code>RowType</code> 参数显式设置弹窗内组件布局方式，默认为上下布局，可设置值 <code>inline</code> 水平布局", "SearchDialogsLayoutButtonText1": "搜索弹窗(左对齐)", "SearchDialogsLayoutButtonText2": "搜索弹窗(右对齐)", "SearchDialogOptionAttr": "SearchDialogOption 属性"}, "BootstrapBlazor.Server.Components.Samples.PopoverConfirms": {"PopoverConfirmsTitle": "PopConfirm 气泡确认框", "PopoverConfirmsDescription": "点击元素，弹出气泡确认框。", "PopoverConfirmsNormalTitle": "基础用法", "PopoverConfirmsNormalIntro": "<code>PopConfirm</code> 的属性与 <code>Popover</code> 很类似，因此对于重复属性，请参考 <a href='/popover'><code>Popover</code><a> 的文档，在此文档中不做详尽解释。", "PopoverConfirmsNormalPopupBoxText": "下面弹框", "PopoverConfirmsNormalPopupBoxContent": "这是一段内容确定删除吗？", "PopoverConfirmsNormalRightPopup": "右侧弹窗", "PopoverConfirmsNormalRightPopupContent": "这是一段内容确定删除吗？", "PopoverConfirmsNormalLeftPopup": "左侧弹窗", "PopoverConfirmsNormalLeftPopupContent": "这是一段内容确定删除吗？", "PopoverConfirmsNormalPopupAbove": "上面弹窗", "PopoverConfirmsNormalPopupAboveContent": "是一段内容确定删除吗？", "PopoverConfirmsContentTitle": "Content 更改确认弹窗显示文字", "PopoverConfirmsContentIntro": "通过设置 <code>Content</code> 属性更改确认弹窗显示文字", "PopoverConfirmsContentDelete": "删除确认按钮", "PopoverConfirmsContentDeleteContent": "确定删除数据吗？", "PopoverConfirmsIsAsyncTitle": "异步确认", "PopoverConfirmsIsAsyncIntro": "通过设置 <code>IsAsync</code> 属性点击确认按钮时异步进行数据提交", "PopoverConfirmsIsAsyncConfirmationText": "异步确认", "PopoverConfirmsIsAsyncConfirmationContent": "确定删除数据吗？", "PopoverConfirmsFormTitle": "表单提交", "PopoverConfirmsFormIntro": "通过设置 <code>ButtonType</code> 属性值为 <code>ButtonType.Submit</code> 使确认确认按钮点击后进行异步表单数据提交", "PopoverConfirmsFormSubmitFormText": "提交表单", "PopoverConfirmsFormSubmitFormContent": "确定提交数据吗？", "PopoverConfirmsIsLinkTitle": "超链接按钮", "PopoverConfirmsIsLinkIntro": "通过设置 <code>IsLink</code> 属性组件使用 <code>A</code> 标签进行渲染", "PopoverConfirmsIsLinkHyperLinkText": "我是超链接", "PopoverConfirmsIsLinkHyperLinkContent": "确定删除数据吗？", "PopoverConfirmsCustomClassTitle": "自定义样式", "PopoverConfirmsCustomClassIntro": "设置 <code>CssClass</code> 自定义组件样式", "PopoverConfirmsCustomClassCustomButtonText": "自定义样式按钮", "PopoverConfirmsCustomClassCustomButtonContent": "确定删除数据吗？", "PopoverConfirmsBodyTemplateTitle": "自定义 PopConfirmButton 内容", "PopoverConfirmsBodyTemplateIntro": "通过设置 <code>BodyTemplate</code> 属性，自定义内容", "PopoverConfirmsBodyTemplateButtonText": "自定义内容", "PopoverConfirmsShowButtonsTitle": "自定义组件", "PopoverConfirmsShowButtonsIntro": "通过设置 <code>ShowCloseButton=\"false\"</code> <code>ShowConfirmButton=\"false\"</code> 不显示内置功能按钮，在自定义组件中自定义一个 <b>审批</b> 按钮", "PopoverConfirmsShowButtonsButtonText": "自定义组件", "PopoverConfirmsShowButtonsDesc": "自定义组件内可通过级联参数调用组件内部的 <code>Close</code> 或者 <code>Confirm</code> 方法", "PopoverConfirmsTriggerTitle": "触发方式", "PopoverConfirmsTriggerIntro": "通过设置 <code>Trigger</code> 参数来设置组件弹出确认框的方式，默认值为 <code>click</code> 还可以设置 <code>hover focus</code> 可组合使用"}, "BootstrapBlazor.Server.Components.Components.CustomPopConfirmContent": {"CustomPopConfirmContentText": "自定义弹窗内容", "CustomPopConfirmContentButtonText": "审批"}, "BootstrapBlazor.Server.Components.Samples.Popovers": {"PopoversTitle": "Popover 弹出窗组件", "PopoversDescription": "点击/鼠标移入元素，弹出气泡式的卡片浮层", "PopoversNormalTitle": "基础用法", "PopoversNormalIntro": "点击文本输入框弹出 <code>Popover</code> 弹出框，<code>Placement</code> 设置弹出框的位置 二次点击时关闭弹出框", "PopoversButtonTitle": "按钮激活弹出框", "PopoversButtonIntro": "点击按钮后弹出 <code>Popover</code> 弹出框", "PopoversButtonButtonText": "Click 激活/关闭", "PopoversTemplateTitle": "自定义模板", "PopoversTemplateIntro": "通过设置 <code>Template</code> 模板，自定义弹窗内容", "PopoversTemplateButtonText": "自定义模板", "PopoversTemplateTitleText": "自定义模板", "PopoversCssClassTitle": "自定义样式", "PopoversCssClassIntro": "通过设置 <code>Popover</code> 参数 <code>CssClass</code> 对弹窗进行自定义样式", "PopoversCssClassDescription": "设置 <code>Popover</code> 参数 <code>CssClass=\"custom-popover\"</code> 进行自定义样式", "PopoversCssClassButtonText": "Click 激活/关闭"}, "BootstrapBlazor.Server.Components.Samples.Progress": {"ProgressTitle": "Progress 进度条", "ProgressDescription": "用于展示操作进度，告知用户当前状态和预期", "ProgressNormalTitle": "进度条组件", "ProgressNormalIntro": "常用的进度条", "ProgressDisplayValueTitle": "显示值", "ProgressDisplayValueIntro": "通过 <code>IsShowValue</code> 设置进度条值显示", "ProgressHeightTitle": "设置进度条高度", "ProgressHeightIntro": "通过 <code>Height</code> 进度条高度设置", "ProgressColorTitle": "带颜色进度条", "ProgressColorIntro": "设置进度条颜色", "ProgressStripeTitle": "是否显示条纹", "ProgressStripeIntro": "通过 <code>IsStriped</code> 设置进度条的条纹设置", "ProgressDynamicTitle": "是否动态显示", "ProgressDynamicIntro": "通过 <code>IsAnimated</code> 设置进度条的动态显示", "ProgressBindingTitle": "双向绑定数据", "ProgressBindingIntro": "绑定数据", "ProgressDecimalsTitle": "小数", "ProgressDecimalsIntro": "通过设置 <code>Round</code> 参数调整保留小数点位数，默认为 <code>0</code>，通过设置 <code>MidpointRounding</code> 参数调整四舍五入模式，默认为 <code>MidpointRounding.AwayFromZero</code> 四舍五入"}, "BootstrapBlazor.Server.Components.Samples.Responsives": {"ResponsiveTitle": "Responsive 断点监听", "ResponsiveDescription": "根据参数条件，确定是否重新渲染响应布局的内容，通常用于响应布局", "ResponsiveNormalTitle": "基础用法", "ResponsiveNormalIntro": "调整浏览器窗口大小，观察 <code>Breakpoint</code> 的变化", "ResponsiveNormalIntroCurrentText": "当前"}, "BootstrapBlazor.Server.Components.Samples.Modals": {"ModalsTitle": "Modal 模态框", "ModalsDescription": "在保留当前页面状态的情况下，告知用户并承载相关操作", "ModalsNormalTitle": "基本用法", "ModalsNormalIntro": "弹出一个对话框，适合需要定制性更大的场景", "ModalsNormalPopupTitle": "弹窗标题", "ModalsNormalPopupText": "弹窗正文", "ModalsNormalIsKeyboard": "通过设置 <code>Modal</code> 组件的 <code>IsKeyboard:true</code> 参数，开启弹窗是否支持 <kbd>ESC</kbd>", "ModalsNormalPopups": "弹窗", "ModalsNormalDefaultPopup": "默认弹窗", "ModalsNormalDefaultPopupText": "我是弹窗内正文", "ModalsIsBackdropTitle": "IsBackdrop 背景关闭模式", "ModalsIsBackdropIntro": "点击弹窗以外区域默认关闭弹窗效果", "ModalsIsBackdropToClose": "点击背景可关闭弹窗", "ModalsTitlePopupWindowText": "我是弹窗内正文", "ModalsDialogSizeTitle": "弹框大小", "ModalsDialogSizeIntro": "通过 <code>Size</code> 设置弹框组件的大小", "ModalsResize": "弹框大小", "ModalsDialogResizeTitle": "调整大小弹窗", "ModalsDialogResizeIntro": "通过设置 <code>ShowResize=\"true\"</code> 可以通过鼠标拉动弹窗右下角进行窗口大小调整", "ModalsSmallPopup": "小弹窗", "ModalsBigPopup": "大弹窗", "ModalsOverSizedPopup": "超大弹窗", "ModalsSuperLargePopup": "超超大弹窗", "ModalsFullScreenSizeTitle": "全屏弹窗", "ModalsFullScreenSizeIntro": "设置属性 <code>FullScreenSize</code> 即可", "ModalsFullScreenPopup": "全屏弹窗", "ModalsFullScreenPopup992": "全屏弹窗(<992px)", "ModalsLargeFullScreenPopup": "大全屏弹窗", "ModalsFullScreenPopup1200": "全屏弹窗(<1200px)", "ModalsLargeFullScreenPopupWindow": "超大全屏弹窗", "ModalsFullScreenPopup1400": "全屏弹窗(<1400px)", "ModalsSuperLargeFullScreenPopupWindow": "超超大全屏弹窗", "ModalsCenterVerticallyTitle": "垂直居中", "ModalsCenterVerticallyIntro": "通过 <code>IsCentered</code> 设置弹框组件的垂直居中", "ModalsVerticallyCenteredPopup": "垂直居中的弹窗", "ModalsLongContentTitle": "超长内容", "ModalsLongContentIntro": "通过 <code>IsScrolling</code> 针对超出内容设置弹框组件滚轮滑动功能", "ModalsVeryLongContent": "内容超长的弹窗", "ModalsScrollBarPopup": "内置滚动条弹窗", "ModalsIsDraggableTitle": "可拖拽弹窗", "ModalsIsDraggableIntro": "点击弹窗标题栏对弹窗进行拖拽", "ModalsMaximizeTitle": "最大化按钮", "ModalsMaximizeIntro": "通过设置 <code>ShowMaximinzeButton</code> 弹窗显示最大化按钮", "ModalsMaximizePopup": "可最大化弹窗", "ModalsShownCallbackAsyncTitle": "弹窗已显示回调方法", "ModalsShownCallbackAsyncIntro": "通过设置 <code>ShownCallbackAsync</code> 回调委托，弹窗显示后回调此方法", "ModalsAttributesFirstAfterRenderCallbackAsync": "首次页面渲染后的回调方法，进入页面后就立即弹窗", "ModalsAttributeHeaderTemplate": "模态头部模板", "ModalsAttributeBodyTemplate": "模态弹窗内容模板", "ModalsAttributeChildContent": "内容", "ModalsAttributeFooterTemplate": "模态底部的ModalFooter组件", "ModalsAttributeIsBackdrop": "是否关闭后台弹出窗口", "ModalsAttributeIsKeyboard": "是否响应ESC键盘", "ModalsAttributeIsCentered": "是否垂直居中", "ModalsAttributeIsScrolling": "弹出窗口的文本过长时是否滚动", "ModalsAttributeIsFade": "是否启用淡入淡出动画效果", "ModalsAttributeIsDraggable": "是否启用拖动效果", "ModalsAttributeShowCloseButton": "是否显示关闭按钮", "ModalsAttributeShowFooter": "是否显示页脚", "ModalsAttributeSize": "大小", "ModalsAttributeFullScreenSize": "全屏显示", "ModalsAttributeTitle": "弹出窗口标题", "ModalsAttributeShowMaximizeButton": "是否显示弹出的最大化按钮", "ModalsAttributeShownCallbackAsync": "弹出菜单显示回调方法"}, "BootstrapBlazor.Server.Components.Samples.Masks": {"MaskTitle": "遮罩组件", "MaskDescription": "通过调用服务显示，隐藏方法，显示一个遮罩层对数据进行遮罩操作", "MaskNormalTitle": "基本用法", "MaskNormalIntro": "调用 <code>MaskService</code> 遮罩服务示例方法 <code>Show</code> 方法显示一个遮罩，3 秒后调用实例方法 <code>Close</code> 关闭遮罩", "ShowMaskButtonText": "打开", "MaskContainerTitle": "指定容器", "MaskContainerIntro": "通过设置 <code>MaskOption</code> 参数 <code>ContainerId</code> 指定遮罩出现位置", "MultipleMaskContainerTitle": "多遮罩", "MultipleMaskContainerIntro": "通过自定义设置 <code>Mask</code> 组件可实现网页内多个局部进行遮罩操作", "MultipleMaskContainerDesc": "组件内自己设置 <code>Mask</code> 组件，调用 <code>MaskService</code> 实例方法 <code>Show</code> 时，通过第二个参数指定 <code>Mask</code> 实例即可"}, "BootstrapBlazor.Server.Components.Samples.Messages": {"MessagesTitle": "Message 消息提示", "MessagesDescription": "常用于主动操作后的反馈提示。与 Toast 的区别是后者更多用于系统级通知的被动提醒", "MessagesIntro": "组件使用介绍：", "MessagesTips1": "1. 注入服务 <code>MessageService</code>", "MessagesTips2": "2. 调用其实例 <code>api</code>", "MessagesTips3": "这是一条提示消息", "MessagesNormalTitle": "基础用法", "MessagesNormalIntro": "从顶部出现，4 秒后自动消失", "MessagesMessagePrompt": "打开消息提示", "MessagesAsyncTitle": "线程阻塞通知", "MessagesAsyncIntro": "通过设置按钮 <code>IsAsync</code> 参数，使用同一个 <code>MessageOption</code> 更新弹窗信息提示不同步骤时的信息", "MessagesAsyncDemoStep1Text": "正在打包文档，请稍等...", "MessagesAsyncDemoStep2Text": "打包完成，正在下载...", "MessagesAsyncDemoStep3Text": "下载完成，自动关窗", "MessagesAsyncText": "线程阻塞通知示例", "MessagesIconTitle": "带图标的消息框", "MessagesIconIntro": "通过设置 <code>MessageOption</code> 的 <code>Icon</code> 属性，更改消息框左侧小图标", "MessagesCloseButtonTitle": "带关闭按钮的消息框", "MessagesCloseButtonIntro": "通过设置 <code>MessageOption</code> 的 <code>ShowDismiss</code> 属性，更改消息框右侧出现关闭按钮", "MessagesLeftBoardTitle": "左侧带边框的消息框", "MessagesLeftBoardIntro": "通过设置 <code>MessageOption</code> 的 <code>ShowBar</code> 属性，更改消息框左侧边框样式", "MessagesDifferentColorTitle": "不同颜色的消息框", "MessagesDifferentColorIntro": "通过设置 <code>MessageOption</code> 的 <code>Color</code> 属性，更改消息框颜色", "MessagesDifferentColorPrimary": "Primary 消息", "MessagesDifferentColorSuccess": "Success 消息", "MessagesDifferentColorInfo": "Info 消息", "MessagesDifferentColorDanger": "Danger 消息", "MessagesDifferentColorWarning": "Warning 消息", "MessagesDifferentColorSecondary": "Secondary 消息", "MessagesPositionTitle": "消息框弹出位置", "MessagesPositionIntro": "通过设置 <code>MessageService</code> 服务的组件参数，指定已经设置底部显示位置的 <code>Message</code> 组件", "MessagesItem": "MessageOption 属性", "MessagesTemplateTitle": "自定义模板", "MessagesTemplateIntro": "通过设置 <code>ChildContent</code> 模板可以实现丰富的自定义样式与内容的提示信息", "MessagesTemplatePrompt": "自定义消息", "MessagesShowModeTitle": "显示模式", "MessagesShowModeIntro": "通过设置 <code>ShowMode</code> 参数，指定显示模式", "MessagesShowModePrompt": "只显示最后一条消息"}, "BootstrapBlazor.Server.Components.Samples.Lights": {"LightsTitle": "Light 指示灯", "LightsDescription": "提供各种状态的指示灯", "LightsNormalTitle": "普通用法", "LightsNormalIntro": "用于状态指示", "LightsFlashingTitle": "闪烁", "LightsFlashingIntro": "通过设置属性 <code>IsFlash</code> 使指示灯进行闪烁", "LightsColorTitle": "变色", "LightsColorIntro": "通过设置属性 <code>Color</code> 值使指示灯进行变色", "LightsTooltipTextTitle": "提示文字", "LightsTooltipTextIntro": "通过设置属性 <code>TooltipText</code> 值使鼠标悬浮指示灯上时提示 <code>tooltip</code> 文字", "TooltipText": "我是提示文字信息", "LightsFlatTitle": "扁平化", "LightsFlatIntro": "通过设置属性 <code>IsFlat</code> 使指示灯扁平化"}, "BootstrapBlazor.Server.Components.Samples.Charts.Index": {"Chart": "Chart 图表", "ChartIntro": "通过给定数据，绘画各种图表的组件", "ChartIntro2": "组件数据在 <code>OnInit</code> 回调委托中进行设置即可"}, "BootstrapBlazor.Server.Components.Samples.Charts.Bubble": {"BubbleNormalTitle": "Bubble 图", "BubbleNormalIntro": "通过设置 <code>ChartType</code> 更改图表为 <code>bubble</code> 图", "BubbleNormalRandomData": "随机数据", "BubbleNormalAddDataSet": "添加数据集", "BubbleNormalRemoveDataset": "移除数据集", "BubbleNormalAddData": "添加数据", "BubbleNormalRemoveData": "移除数据", "BubbleNormalReload": "重载", "BubbleBarAspectRatioTitle": "图表比例", "BubbleBarAspectRatioIntro": "设置了高度和宽度,会自动禁用约束图表比例, 图表充满容器"}, "BootstrapBlazor.Server.Components.Samples.Charts.Doughnut": {"DoughnutNormalTitle": "Doughnut 图", "DoughnutNormalIntro": "通过设置 <code>ChartType</code> 更改图表为 <code>doughnut</code> 图", "DoughnutNormalRandomData": "随机数据", "DoughnutNormalAddDataset": "添加数据集", "DoughnutNormalRemoveDataset": "移除数据集", "DoughnutNormalAddingData": "添加数据", "DoughnutNormalRemoveData": "移除数据", "DoughnutNormalHalf": "半圆/全圆", "DoughnutNormalReload": "重载", "DoughnutAspectRatioTitle": "图表比例", "DoughnutAspectRatioIntro": "设置了高度和宽度,会自动禁用约束图表比例, 图表充满容器"}, "BootstrapBlazor.Server.Components.Samples.Charts.Pie": {"PieNormalTitle": "Pie 图", "PieNormalIntro": "通过设置 <code>ChartType</code> 更改图表为 <code>pie</code> 图", "PieNormalRandomData": "随机数据", "PieNormalAddDataset": "添加数据集", "PieNormalRemoveDataset": "移除数据集", "PieNormalAddingData": "添加数据", "PieNormalRemoveData": "移除数据", "PieNormalReload": "重载", "PieAspectRatioTitle": "图表比例", "PieAspectRatioIntro": "设置了高度和宽度,会自动禁用约束图表比例, 图表充满容器", "PieLegendPositionTitle": "图例位置", "PieLegendPositionIntro": "通过 <code>LegendPostion</code> 设置图例位置"}, "BootstrapBlazor.Server.Components.Samples.Charts.Line": {"LineOnInitTitle": "Line 图", "LineOnInitIntro": "使用 <code>OnInit</code> 回调委托方法，对初始化数据进行赋值后，即可进行绘图操作，通过设置 <code>BorderWidth</code> 属性，可以设置折线图线宽，默认值3，通过设置 <code>ChartOptions.LegendLabelsFontSize</code> 属性，可以设置图例 <code>Lables</code> 字体大小", "LineOnInitRandomData": "随机数据", "LineOnInitAddDataset": "添加数据集", "LineOnInitRemoveDataset": "移除数据集", "LineOnInitAddingData": "添加数据", "LineOnInitRemoveData": "移除数据", "LineOnInitContinueData": "连续数据", "LineAnimation": "动画", "LineContinueTitle": "Plot", "LineContinueIntro": "调用实例方法 <code>Reload</code>，重新调整数据使曲线图向左平移，设置 <code>IsAnimation</code> 关闭动画效果", "LineTensionTitle": "Line 图", "LineTensionIntro": "使用设置 <code>ChartDataset</code> 实例的 <code>Tension</code> 参数，调整折线的曲率，默认位 <b>0.4f</b>", "LineNullableTitle": "Line 图", "LineNullableIntro": "使用设置 <code>ChartDataset</code> 实例的 <code>Data</code> 参数中含 <code>null</code>，折线图使用虚线连接", "LineOnInitReload": "重载", "LineTwoYAxesTitle": "双Y轴", "LineTwoYAxesIntro": "显示第二Y轴:对齐方式,标题和对应数据组", "LineBarAspectRatioTitle": "图表比例", "LineBarAspectRatioIntro": "设置了高度和宽度,会自动禁用约束图表比例, 图表充满容器", "LineChartJSTitle": "通过JS生成Chart", "LineChartJSIntro": "由于 <code>BootstrapBlazor.Chart</code> 底层引用了 <code>Chart.JS</code> 所以我们可以通过JS方式来调用并生成图表。", "AppendDataTitle": "数据参数扩展", "AppendDataIntro": "由于 <code>ChartDataSource</code> 没有完全封装 <code>Chart.JS</code> 中所有参数，当我们需要设置一些未提供的参数时，可以通过 <code>AppendData</code> 来完成", "CustomTooltipTitle": "自定义 Tooltip", "CustomTooltipIntro": "自定义 <code>Tooltip</code> 需要使用客户端脚本设置", "CustomTooltipLi1": "<code>Chart</code> 组件设置 <b>Id</b> 参数", "CustomTooltipLi2": "创建客户端 <code>JavaScript</code> 脚本，本例使用隔离脚本方式导出方法名为 <code>customTooltip</code>，参数依次为 <b>组件 Id</b> <b>回调引用实例</b> <b>回调方法名称(TooltipLog)</b>", "CustomTooltipLi3": "本例中重载基类方法 <code>InvokeInitAsync</code> 通过 <code>await InvokeVoidAsync(\"customTooltip\", CustomTooltipId, Interop, nameof(TooltipLog))</code> 语句调用客户端脚本"}, "BootstrapBlazor.Server.Components.Samples.Charts.Bar": {"BarTypeTitle": "Bar 图", "BarTypeIntro": "通过设置 <code>ChartType</code> 更改图表为 <code>bar</code> 图", "BarTypeAnimationOn": "开启动画", "BarTypeAnimationOff": "关闭动画", "BarTypeRandomData": "随机数据", "BarTypeAddDataSet": "添加数据集", "BarTypeRemoveDataSet": "移除数据集", "BarTypeAddData": "添加数据", "BarTypeRemoveData": "移除数据", "BarStackedTitle": "堆砌排列", "BarStackedIntro": "通过设置 X/Y 轴 <code>Stacked</code> 属性，控制是否堆砌排列", "BarTypeReload": "重载", "BarTwoYAxesTitle": "双Y轴", "BarTwoYAxesIntro": "显示第二Y轴:对齐方式,标题和对应数据组", "BarAspectRatioTitle": "图表比例", "BarAspectRatioIntro": "设置了高度和宽度,会自动禁用约束图表比例, 图表充满容器", "BarShowDataLabelTitle": "显示图表数据值", "BarShowDataLabelIntro": "通过设置<code>ShowDataLabel</code> 属性，控制是否显示数据值", "BarColorSeparatelyTitle": "单独设置柱状图颜色", "BarColorSeparatelyIntro": "通过设置<code>BarColorSeparately</code>属性，控制是否单独设置柱状图颜色，以及通过<code>BackgroundColor</code> 集合设置柱状图颜色"}, "BootstrapBlazor.Server.Components.Samples.Transitions": {"TransitionsTitle": "Transition 过渡效果", "TransitionsDescription": "BootstarpBlazor 应用在部分组件的过渡动画，你也可以直接使用。", "TransitionsNormalTitle": "基础用法", "TransitionsNormalIntro": "基础动画效果演示", "TransitionsEndCallbackTitle": "动画执行完成回调", "TransitionsEndCallbackIntro": "动画执行完成后执行回调函数", "TransitionsDurationTitle": "设置动画时长", "TransitionsDurationIntro": "通过设置 <code>Duration</code> 参数控制动画时长单位为毫秒"}, "BootstrapBlazor.Server.Components.Samples.EditDialogs": {"Title": "EditDialog 编辑弹窗", "Description": "通过绑定数据模型自动呈现编辑弹窗", "SubDescription": "<code>EditDialog</code> 组件是 <code>Dialog</code> 组件的扩展，适用于数据弹出窗编辑。", "Tip": "通过调用注入服务 <code>DialogService</code> 的 <code>ShowEditDialog</code> 方法直接弹出编辑对话框，大大减少代码量。<code>EditDialogOption</code> 配置类继承 <code>DialogOption</code>，更多参数设置请点击 <a href=\"dialog\" target=\"_blank\">[传送门]</a>\"", "NormalTitle": "基础用法", "NormalIntro": "通过绑定 <code>TModel</code> 数据模型，自动生成模型各个字段的可编辑表单", "NoRenderTitle": "设置绑定模型部分属性不显示", "NoRenderIntro": "通过设置 <code>IEditorItem</code> 实例的 <b>地址</b> <b>数量</b> 参数 <code>Ignore=true</code>, 实现编辑弹窗不显示", "EditDialogOption": "EditDialogOption 属性", "LeftAlignedButton": "编辑弹窗(左对齐)", "RightAlignedButton": "编辑弹窗(右对齐)", "PopupButton": "弹窗"}, "BootstrapBlazor.Server.Components.Samples.BaiduOcr": {"Title": "IBaiduOcr 百度文字识别服务", "SubTitle": "文字识别服务", "VatInvoiceTitle": "增值税发票文字识别", "VatInvoiceIntro": "通过上传增值税发票图片调用百度 Ocr 接口进行文字识别", "BaiduOcrDesc": "本组件通过调用 Baidu AI 平台文字识别接口进行增值税发票文字识别。支持对增值税普票、专票、全电发票（新版全国统一电子发票，专票/普票）、卷票、区块链发票的所有字段进行结构化识别，包括发票基本信息、销售方及购买方信息、商品信息、价税信息等，其中五要素字段的识别准确率超过 99.9%； 同时，支持对增值税卷票的 21 个关键字段进行识别，包括发票类型、发票代码、发票号码、机打号码、机器编号、收款人、销售方名称、销售方纳税人识别号、开票日期、购买方名称、购买方纳税人识别号、项目、单价、数量、金额、税额、合计金额(小写)、合计金额(大写)、校验码、省、市，四要素字段的识别准确率可达95%。上传图片不能超过 4M", "BaiduOcrIntro": "使用方法", "BaiduOcrStep1": "1. 通过注入服务获得 <code>IBaiduOcr</code> 实例", "BaiduOcrStep2": "2. 调用服务相对应的识别方法即可", "VerifyVatInvoiceTitle": "增值税验真", "VerifyVatInvoiceIntro": "通过调用 <code>IBaiduOcr</code> 服务实例的发票验真方法 <code>VerifyInvoiceAsync</code> 返回 <code>InvoiceVerifyResult</code> 其属性 <code>Valid</code> 为 <code>true</code> 时为真"}, "BootstrapBlazor.Server.Components.Samples.Topologies": {"TopologiesTitle": "Topology 人机交互界面", "TopologiesDescription": "通过 <code>Topology</code> 开源组件进行人机交互界面的渲染", "TopologiesNormalTitle": "基础用法", "TopologiesNormalIntro": "加载网站导出的 <code><PERSON><PERSON></code> 文件即可，点击风扇下方数字框可进行控制操作"}, "BootstrapBlazor.Server.Components.Samples.TreeViews": {"TreeViewsTitle": "Tree 树形控件", "TreeViewsDescription": "用清晰的层级结构展示信息，可展开或折叠", "TreeViewsTips1": "组件为泛型组件需要使用 <code>TItem</code> 指定绑定的数据模型，本例中模型为 <code>TreeFoo</code> 需要设置", "TreeViewsTips2": "设置 <code>TreeViewItem</code> 其 <code>IsExpand</code> 参数控制当前子节点是否展开", "TreeViewsTips3": "点击子项展开小箭头时，通过 <code>OnExpandNodeAsync</code> 回调委托方法获取子项数据集合", "TreeViewsTips4": "保持节点状态回落机制，<code>ModelEqualityComparer</code> <code>CustomKeyAttribute</code> <code>IEqualityComparer&lt;TItem&gt;</code> <code>Equals</code> 重载方法", "TreeViewsTips5": "组件将会保持 <code>展开</code> <code>收缩</code> <code>选中</code> 状态", "TreeViewsTips6": "通过 <code>TreeViewItem&lt;TItem&gt;.IsExpand</code> 设置节点是否 <b>展开</b> 状态", "TreeViewsTips7": "通过 <code>TreeViewItem&lt;TItem&gt;.IsActive</code> 设置节点是否 <b>选中</b> 状态", "TreeViewsTips8": "通过 <code>TreeViewItem&lt;TItem&gt;.Checked</code> 设置节点是否 <b>复选/单选</b> 状态", "TreeViewsTips9": "第一步：设置 <code>TItem</code> 泛型模型", "TreeViewsTips10": "第二步：设置 <code>Items</code> 获得组件数据源 <b>注意</b> 数据源类型为 <code>IEnumerable&lt;TreeViewItem&lt;TItem&gt;&gt;</code>", "TreeViewsTips11": "第三步：设置 <code>OnExpandNodeAsync</code> 回调委托响应节点展开获取子项数据源集合", "TreeViewsTips12": "第四步：设置 <code>ModelEqualityComparer</code> 提供组件识别模型比较委托方法，<b>注意</b> 本设置为可选项 通过上面讲解的回落机制进行降级搜索", "TreeViewNormalTitle": "基础用法", "TreeViewNormalIntro": "基础的树形结构展示", "TreeViewNormalDescription": "通过设置 <code>OnTreeItemClick</code> 属性监控树形控件节点被点击时的事件，点击树形控件节点时下面日志显示选中节点的数据", "TreeViewCheckboxTitle": "多选框", "TreeViewCheckboxIntro": "适用于需要选择层级时使用", "TreeViewCheckboxTips1": "通过设置 <code>OnTreeItemChecked</code> 属性监控树形控件节点被勾选时的事件，选中树形控件节点前复选框时下面日志显示选中节点的数据", "TreeViewCheckboxTips2": "<code>Tree</code> 组件数据加载内部将会保持各个节点状态，<b>刷新</b> 按钮将更新数据源 <code>Items</code>，组件重新初始化，点击 <code>追加节点</code> 按钮时，仅更改数据源内部数据未重新赋值，所以各个节点状态保持不变", "TreeViewNormalRadioListDisplayText": "是否重置", "TreeViewCheckboxCheckBoxDisplayText1": "自动选中子节点", "TreeViewCheckboxCheckBoxDisplayText2": "自动选中父节点", "TreeViewCheckboxButtonText": "刷新", "TreeViewCheckboxAddButtonText": "追加节点", "TreeViewDraggableTitle": "可拖拽节点", "TreeViewDraggableIntro": "使树中的节点可以进行跨层级拖拽操作", "TreeViewDraggableDescription": "通过设置 <code>AllowDrag</code> 属性开启节点拖拽功能，使用 <code>OnDragItemEndAsync</code> 回调委托方法响应拖拽节点放置事件", "TreeViewTreeDisableTitle": "禁用状态", "TreeViewTreeDisableIntro": "可将 Tree 的某些节点设置为禁用状态", "TreeViewTreeDisableDescription": "通过设置数据源 <code>TreeViewItem</code> 对象的 <code>Disabled</code> 属性，来控制此节点是否可以进行勾选动作，设置为 <code>false</code> 时不影响节点展开/收缩功能", "TreeViewAccordionModelTitle": "手风琴模式", "TreeViewAccordionModelIntro": "对于同一级的节点，每次只能展开一个", "TreeViewAccordionModelDescription": "通过设置 <code>Tree</code> 组件的 <code>IsAccordion</code> 属性开启手风琴效果", "TreeViewDefaultExpandTitle": "默认展开和默认选中", "TreeViewDefaultExpandIntro": "可将 <code>Tree</code> 的某些节点设置为默认展开或默认选中", "TreeViewDefaultExpandDescription": "通过设置 <code>TreeViewItem</code> 对象的 <code>IsExpand</code> 属性，来控制此节点是否默认为展开状态，本例中 <b>导航二</b> 默认为展开状态，其余节点默认为收缩状态", "TreeViewTreeDisplayIconTitle": "显示图标", "TreeViewTreeDisplayIconIntro": "通过设置 <code>ShowIcon</code> 来控制组件是否显示图标", "TreeViewTreeDisplayIconDescription": "通过设置 <code>TreeViewItem</code> 对象的 <code>ShowIcon</code> 属性，来控制此节点是否显示图标", "TreeViewTreeClickExpandTitle": "点击节点展开收缩功能", "TreeViewTreeClickExpandIntro": "通过设置 <code>ClickToggleNode</code> 来控制点击节点时是否进行展开收缩操作", "TreeViewTreeClickExpandDescription": "通过设置 <code>TreeViewItem</code> 对象的 <code>ClickToggleNode</code> 属性，来控制此节点是否通过点击来实现展开收缩操作", "TreeViewTreeValidationFormTitle": "Tree 组件内置到验证表单中", "TreeViewTreeValidationFormIntro": "<code>Tree</code> 组件内部可开启 <code>Checkbox</code> 内置到验证表单时会显示 <code>DisplayName</code> 此功能在树状组件内需要禁止", "TreeViewTreeValidationFormDescription": "通过设置 <code>ShowCheckbox</code> 属性显示 <code>Checkbox</code> 内置到验证组件 <code>ValidateForm</code> 中不显示 <code>DisplayName</code>", "TreeViewTreeLazyLoadingTitle": "懒加载", "TreeViewTreeLazyLoadingIntro": "展开节点时动态添加子节点", "TreeViewTreeLazyLoadingDescription": "通过设置节点 <code>HasChildNode</code> 控制是否显示节点小箭头图片 。通过Tree的 <code>OnExpandNodeAsync</code> 委托添加节点", "TreeViewTreeCustomNodeTitle": "自定义节点", "TreeViewTreeCustomNodeIntro": "通过设置 <code>TreeViewItem</code> <code>Template</code> 来实现自己的节点模板", "TreeViewTreeNodeColorTitle": "节点颜色", "TreeViewTreeNodeColorIntro": "通过设置 <code>TreeViewItem</code> <code>CssClass</code> 来实现自己的节点样式", "TreeViewCheckedItemsTitle": "获取所有选中节点", "TreeViewCheckedItemsIntro": "通过设置 <code>OnTreeItemChecked</code> 回调委托获取所有节点", "TreeViewShowSkeletonTitle": "加载骨架屏", "TreeViewShowSkeletonIntro": "当组件 <code>Items</code> 为 <code>null</code> 时，通过设置 <code>ShowSkeleton=\"true\"</code> 使异步加载数据时组件显示骨架屏，设置 <code>ShowSkeleton=\"false\"</code> 时显示默认正在加载动画", "TreeViewShowSkeletonButtonText": "异步加载", "TreeViewShowSearchTitle": "显示搜索栏", "TreeViewShowSearchIntro": "通过设置 <code>ShowSearch</code> 显示搜索栏，通过 <code>OnSearchAsync</code> 回调方法设置数据源刷新页面即可", "TreeViewSetActiveTitle": "设置激活节点", "TreeViewSetActiveIntro": "通过调用 <code>SetActiveItem</code> 方法设置当前激活节点", "TreeViewSetActiveDisplayText": "当前激活节点", "TreeViewsAttribute": "TreeViewItem 属性", "TreeViewsDisableWholeTreeView": "是否禁用整个 TreeView", "TreeViewsWhetherToExpandWhenDisable": "禁用时候是否可以展开或折叠子节点", "OnMaxSelectedCountExceedTitle": "可选最大数量提示", "OnMaxSelectedCountExceedContent": "最多只能选择 {0} 项", "TreeViewMaxSelectedCountTitle": "最大选择数量", "TreeViewMaxSelectedCountIntro": "通过设置 <code>MaxSelectedCount</code> 属性控制最大可选数量，通过 <code>OnMaxSelectedCountExceed</code> 回调处理逻辑", "TreeViewMaxSelectedCountDesc": "选中节点超过 2 个时，弹出 <code>Toast</code> 提示栏", "TreeViewEnableKeyboardArrowUpDownTitle": "键盘支持", "TreeViewEnableKeyboardArrowUpDownIntro": "通过设置 <code>EnableKeyboardArrowUpDown=\"true\"</code> 支持键盘上下箭头操作。<kbd>左箭头</kbd> 收起节点，<kbd>右箭头</kbd> 展开节点，<kbd>上箭头</kbd> 向上移动节点，<kbd>下箭头</kbd> 向下移动节点，<kbd>空格</kbd> 选中节点", "TreeViewVirtualizeTitle": "虚拟滚动", "TreeViewVirtualizeIntro": "通过设置 <code>IsVirtualize=\"true\"</code> 开启虚拟滚动，支持大数据", "TreeViewVirtualizeDescription": "组件内部使用 <code>Virtualize</code> 来实现虚拟滚动逻辑，对浏览器压力会减少很多；但是如果树状结构数据比较多，比如 <b>全选</b> 等操作必须对所有数据进行标记，导致内存中确实有大数据存在，目前还没有解决这个问题，目前此组件由于大数据对 <b>CPU</b> 压力还是比较大的", "TreeViewShowToolbarTitle": "显示工具栏", "TreeViewShowToolbarIntro": "通过设置 <code>ShowToolbar=\"true\"</code> 显示工具栏", "TreeViewShowToolbarDesc": "开启后鼠标悬浮节点时，右侧出现工具栏图标，可自定义工具栏功能；通过设置 <code>OnUpdateCallbackAsync</code> 回调方法进行数据的更新；通过设置 <code>ToolbarTemplate</code> 定义工具栏模板，如未设置时使用内部默认更改节点名称模板"}, "BootstrapBlazor.Server.Components.Samples.SwitchButtons": {"SwitchButtonsTitle": "Switch Button 状态切换按钮", "SwitchButtonsDescription": "点击按钮后切换状态", "SwitchButtonsNormalTitle": "基础用法", "SwitchButtonsNormalIntro": "点击组件自动切换状态", "SwitchButtonsToggleStateTitle": "初始化状态", "SwitchButtonsToggleStateIntro": "通过设置 <code>ToggleState</code> 初始化组件状态", "SwitchButtonsToggleStateDescription": "可通过设置 <code>ToggleStateChanged</code> 回调方法获得当前组件状态", "SwitchButtonsOnClickTitle": "点击回调方法", "SwitchButtonsOnClickIntro": "通过设置 <code>ToggleState</code> 初始化组件状态", "SwitchButtonsOnClickDescription": "<code>OnClick</code> 回调是 <code>EventCallback</code> 会自动刷新当前组件或者页面，若不需要刷新组件或者页面可使用 <code>ToggleStateChanged</code>"}, "BootstrapBlazor.Server.Components.Samples.Downloads": {"DownloadsTitle": "Download 文件下载", "DownloadsSubTitle": "用于直接下载物理文件", "DownloadsTips1": "特别注意", "DownloadsTips2": "<code>Download</code> 组件底层使用了 <code>DotNetStreamReference</code> 对象，这允许将文件数据流式传输到客户端，此方法会将整个文件加载到客户端内存中，若要下载相对较大的文件 (<code>&gt;= 250 MB</code>)，建议遵循 <code>MVC</code> 从 <code>Url</code> 下载", "DownloadsExample": "示例", "DownloadsExampleButtonText": "下载文件", "DownloadsExampleRazorCodeTitle": "<code>Razor</code> 代码", "DownloadsExampleRazorCodeContent": "<Button OnClick=\"DownloadFileAsync\">点我下载文件</Button>", "DownloadsExampleCodeTitle": "<code>C#</code> 代码", "DownloadsExampleTestFile": "测试文件.txt", "DownloadsExampleContent": "自行生成并写入的文本，这里可以换成图片或其他内容", "DownloadNormalTitle": "普通下载", "DownloadNormalIntro": "通过设置物理路径直接下载文件", "DownloadNormalButtonText": "下载", "DownloadFolderTitle": "文件夹下载", "DownloadFolderIntro": "通过设置文件夹物理路径，将目录内文件打包压缩后下载", "DownloadFolderButtonText": "下载", "DownloadBigFileTitle": "大文件下载测试", "DownloadBigFileIntro": "这里模拟生成了一个 <code>100万行</code> 的文本文件，大概 <b>58M</b>，可以自行测试", "DownloadBigFileButtonText": "按钮设置 <code>IsAsync</code> 值为 <code>true</code> 进行异步下载操作", "DownloadImageTitle": "获取图片并显示", "DownloadImageIntro": "模拟直接由前端页面生成验证码或者上传图片不保存直接显示的情况。", "DownloadImageButtonText": "由于测试使用了 <code>wwwroot</code> 下的文件，没有代码生成，<code>wasm</code> 无法访问 <code>wwwroot</code> 文件夹，故此测试只有<code>ssr</code> 模式可用。<code>wasm</code> 请自行测试。"}, "BootstrapBlazor.Server.Components.Samples.Dialogs": {"Title": "Dialog 对话框组件", "Description": "通过注入服务调用 <code>Show</code> 方法弹出窗口进行人机交互", "Tip1": "组件使用介绍", "Tip2": "注入服务 <code>DialogService</code> <a href=\"dialog-service\" target=\"_blank\">[传送门]</a>", "Tip3": "调用其 <code>DialogOption</code> 实例 <code>OnCloseAsync</code> 方法", "Tip4": "级联参数关闭弹窗方法", "Tip5": "关窗按钮", "DialogTitle": "数据查询窗口", "KeyboardTitle": "基本用法", "KeyboardIntro": "通过设置 <code>DialogOption</code> 属性对模态框进行基本属性设置", "ResizeTitle": "调整大小", "ResizeIntro": "通过设置 <code>ShowResize=\"true\"</code> 可以通过鼠标拉动弹窗右下角进行窗口大小调整", "CustomerHeaderTitle": "自定义标题栏", "CustomerHeaderIntro": "通过设置 <code>HeaderTemplate</code> 属性对模态框标题栏进行自定义设置", "ComponentTitle": "弹出复杂组件", "ComponentIntro": "通过调用 <code>Show&lt;Counter&gt;()</code> 来弹出一个自定义组件", "ComponentTip": "本例中弹出对话框中包含一个示例网站的自带 <code>Counter</code> 组件", "BodyContextTitle": "弹窗传参", "BodyContextIntro": "通过设置 <code>BodyContext</code> 属性值，可以把参数传递给弹窗中的组件内", "BodyContextTip": "本例中点击按钮时设置 <code>BodyContext</code> 值为 <code>我是传参</code>，弹窗内容为自定义组件 <code>DemoComponent</code>，组件内通过级联参数获取到其值", "ApplyTitle": "实战应用", "ApplyIntro": "本例中通过传递一个主键，在弹窗内的组件通过此主键进行数据查询，并将结果显示在弹窗内", "CloseDialogByCodeTitle": "代码关闭弹窗", "CloseDialogByCodeIntro": "本例讲解如何通过代码打开与关闭弹窗", "CloseDialogByCodeTip": "利用弹窗参数 <code>DialogOption</code> 实例方法 <code>CloseDialogAsync</code> 即可关闭弹窗", "DisableHeaderCloseButtonTitle": "禁用 Header 中的关闭按钮", "DisableHeaderCloseButtonIntro": "本例讲解如何通过代码打开与关闭弹窗", "DisableHeaderCloseButtonTip": "设置参数 <code>ShowHeaderCloseButton</code> 禁止弹窗 <code>Header</code> 右侧显示 <b>关闭</b> 按钮", "MultipleDialogTitle": "多级弹窗", "MultipleDialogIntro": "点击弹窗内部按钮继续弹出对话窗", "MultipleDialogTip1": "功能介绍", "MultipleDialogTip2": "点击按钮弹出对话窗", "MultipleDialogTip3": "切换弹窗内 <code>Tab</code> 组件的第三个标签页 <b>角色管理</b>", "MultipleDialogTip4": "点击标签页中的弹窗继续弹出对话框", "MultipleDialogTip5": "关闭当前对话框后之前打开的对话框 <b>保持状态</b>", "MultipleDialogDesc": "目前多级弹窗已经实现，每个 <code>ModalDialog</code> 均可以独立设置 <code>IsBackdrop</code> <code>IsKeyboard</code> 参数，修复了上一个版本按下 <kbd>ESC</kbd> 弹窗全部消失问题", "ModalDialogTitle": "模态对话框", "ModalDialogIntro": "通过 <code>ShowModal</code> 方法弹出线程阻塞模式的对话框", "ModalDialogTip1": "功能介绍", "ModalDialogTip2": "点击按钮弹出模态弹窗", "ModalDialogTip3": "更改模态弹窗内数值点击 <code>确认</code> 按钮时数值 <b>更新</b>", "ModalDialogTip4": "更改模态弹窗内数值点击 <code>取消</code> 或者 <code>关闭</code> 按钮时数值 <b>不更新</b>", "ModalDialogTip5": "再次点击弹出模态弹窗时，数值保持一致", "EditDialogTitle": "编辑对话框", "EditDialogIntro": "通过 <code>ShowEditDialog</code> 方法弹出保存对话框", "EditDialogTip1": "功能介绍", "EditDialogTip2": "点击按钮弹出编辑弹窗", "EditDialogTip3": "通过 <code>EditDialogOption</code> 参数对弹窗进行设置", "EditDialogTip4": "设计出发点通过给定 <code>Model</code> 或者 <code>Items</code> 自动生成带客户端验证的表单窗口", "SearchDialogTitle": "搜索对话框", "SearchDialogIntro": "通过 <code>ShowSearchDialog</code> 方法弹出保存对话框", "SearchDialogTip1": "功能介绍", "SearchDialogTip2": "点击按钮弹出搜索弹窗", "SearchDialogTip3": "通过 <code>SearchDialogOption</code> 参数对弹窗进行设置", "SearchDialogTip4": "设计出发点通过给定 <code>Model</code> 或者 <code>Items</code> 自动生成搜索窗口", "SaveDialogTitle": "保存对话框", "SaveDialogIntro": "通过 <code>ShowSaveDialog</code> 方法弹出保存对话框", "SaveDialogTip1": "功能介绍", "SaveDialogTip2": "点击按钮弹出保存弹窗", "SaveDialogTip3": "设计出发点通过给定 <code>TComponent</code> 自动生成保存窗口，通过设置 <code>saveCallback</code> 在保存回调方法中进行数据处理，<code>TComponent</code> 泛型组件所需要参数可以通过 <code>parameters</code> 进行传递", "SizeTitle": "对话框大小", "SizeIntro": "通过设置 <code>Size</code> <code>FullScreenSize</code> 参数组合可以实现非常灵活的窗体大小控制", "PrintDialogTitle": "打印按钮", "PrintDialogIntro": "通过设置 <code>ShowPrintButton</code> 使 <code>Header</code> 上显示一个打印预览按钮", "PrintDialogTip": "通过设置 <code>PrintButtonText</code> 更改 <b>打印预览</b> 按钮文字", "ShowMaximizeButtonTitle": "全屏弹窗", "ShowMaximizeButtonIntro": "通过设置 <code>ShowMaximizeButton</code> 使 <code>Header</code> 上显示一个窗口最大化按钮", "ErrorLoggerTitle": "异常捕获", "ErrorLoggerIntro": "通过 <code>BootstrapBlazorRoot</code> 内置 <code>ErrorLogger</code> 对弹窗内错误进行全局异常捕获", "EmailTitle": "实战演练", "EmailIntro": "模拟一个邮件应用，弹窗选择收件人后填入下方的收件人框。", "EmailTip1": "功能介绍", "EmailTip2": "点击按钮弹出模态弹窗", "EmailTip3": "通过级联传参 <code>BodyContext</code> 传递 <b>10</b> 到弹窗中初始化数据", "EmailTip4": "选中 <code>Table</code> 组件中的行数据，通过双向绑定对 <code>SelectedRows</code> 数据进行 <b>更新</b>", "EmailTip5": "点击 <b>选中</b> 按钮通过双向绑定对 <code>Emails</code> 数据进行 <b>更新</b>", "EmailTip6": "点击 <code>取消</code> 或者 <code>关闭</code> 按钮时 <code>Emails</code> 值 <b>不更新</b>", "EmailTip7": "再次点击弹出模态弹窗时，组件内行选中状态保持一致", "EmailTip8": "弹窗中未选择用户时禁止关闭弹窗", "EmailLabel": "收件人", "Attribute": "DialogOption 属性", "EmailDialogTitle": "选择收件人", "EmailDialogButtonYes": "选择", "EmailInput": "请输入 ...", "HeaderToolbarTemplateTitle": "标题栏自定义按钮", "HeaderToolbarTemplateIntro": "通过设置 <code>HeaderToolbarTemplate</code> 自定义 <code>Header</code> 中工具栏按钮", "KeyboardTip": "通过设置 <code>DialogOption</code> <code>IsKeyboard</code> 参数，开启弹窗是否支持 <kbd>ESC</kbd>，请点击后面按钮设置后再点击 <b>弹窗</b> 按钮测试效果", "KeyboardOpenDialogButton": "点击打开弹窗", "CustomerHeaderOpenDialogButton": "弹窗", "HeaderToolbarTemplateButtonText": "打印", "HeaderToolbarTemplateDialogTitle": "自定义工具栏示例", "HeaderToolbarTemplateToastContent": "打印按钮被点击，可以处理自己的业务逻辑", "ComponentOpenDialogButton": "点击打开 Dialog", "BodyContextOpenDialogButton": "Dialog 传参示例", "ApplyDisplayText": "主键参数", "ApplyOpenDialogButton": "弹窗", "MultipleDialogButton": "弹窗", "ModalDialogButton": "点击打开 Dialog", "EditDialogButton": "编辑弹窗", "SearchDialogButton": "搜索弹窗", "SaveDialogButton": "保存弹窗", "SizeButton": "全屏弹窗(&lt; 1200px)", "PrintDialogButton": "点击打开 Dialog", "ShowMaximizeButton": "点击打开 Dialog", "ErrorLoggerButton": "全局异常测试", "ExportPdfDialogTitle": "带导出 Pdf 功能的弹窗", "ExportPdfDialogIntro": "通过设置 <code>ShowExportPdfButtonInHeader</code> 使 <code>Header</code> 上显示一个导出 pdf 按钮", "ExportPdfDialogTip": "可通过设置 <code>ExportPdfButtonOptions</code> 对更多参数进行设置", "ExportPdfButton": "导出 Pdf 弹窗", "ConfirmDialogButton": "弹出模态框", "ConfirmDialogModalTitle": "文字确认模态框", "ConfirmDialogModalContent": "<p>这是一个文字确认模态框</p><div class=\"text-danger\">这是警告信息</div>"}, "BootstrapBlazor.Server.Components.Samples.Dispatches": {"Title": "Dispatch 消息分发", "Description": "通过注入服务调用实例方法全站弹出窗口进行消息通知", "Tips": "本组件使用注入服务的形式提供功能，通常用于全站消息推送等功能；使用本服务使需要在代码中进行 <b>订阅</b> 与 <b>取消订阅</b> 操作", "NormalTip": "本站通过此组件功能实现了代码仓库有提交时全站提示功能", "NormalTips1": "1. 获得 <b>注入服务</b> 指定消息实体类", "NormalTips2": "2. <b>订阅</b> 消息通知", "NormalTips3": "3. 页面或者组件销毁时 <b>取消订阅</b>", "NormalTips4": "4. 通知实现方法 <code>Notify</code>", "NormalTipsTitle": "通知标题", "NormalTipsContent": "通知内容", "NormalTips5": "本例中使用 <code>Toast</code> 组件进行通知，实战中可以使用其他任意内置消息组件或者自定义组件进行通知提示操作", "DispatchTitle": "实战应用", "DispatchIntro": "点击按钮进行消息分发，所有打开此页面的人均能收到此按钮分发的消息", "DispatchTip": "由于本功能为全站推送通知，为防止恶意使用，下例中按钮通知后禁用 <b>30</b> 秒；消息注入代码在 <code>MainLayout</code> 模板中，注意订阅与分发的泛型对象要一致", "DispatchNoticeButtonText": "通知", "DispatchNoticeMessage": "测试通知消息"}, "BootstrapBlazor.Server.Components.Samples.Drawers": {"Title": "Drawer 抽屉", "Description": "有些时候, Dialog 组件并不满足我们的需求, 比如你的表单很长, 亦或是你需要临时展示一些文档, Drawer 拥有和 Dialog 几乎相同的 API, 在 UI 上带来不一样的体验", "DrawerTips": "有时候我们的弹窗希望在某个容器内展开，可通过在特定容器内置 <code>&lt;Drawer/&gt;</code> 抽屉组件，然后设置参数 <code>Position=\"absolute\"</code> 相对定位来实现此需求；注意：父容器样式需要设置 <code>position: relative;</code>", "NormalTitle": "基本用法", "NormalIntro": "呼出一个临时的侧边栏, 可以从多个方向呼出", "PlacementTitle": "点击遮罩关闭", "PlacementIntro": "通过设置 <code>IsBackdrop</code> 属性为 <code>true</code>，点击遮罩部分时自动关闭抽屉", "NoBackdropTitle": "无遮罩效果", "NoBackdropIntro": "通过设置 <code>ShowBackdrop=\"false\"</code> 不显示遮罩，这种模式下可以与抽屉下面网页元素进行交互操作", "DrawerServiceTitle": "调用服务打开抽屉", "DrawerServiceIntro": "通过调用 <code>DrawerService</code> 服务打开抽屉弹窗", "IsKeyboardTitle": "ESC 按键支持", "IsKeyboardIntro": "组件默认使用 <kbd>ESC</kbd> 按键关闭抽屉弹窗，可通过 <code>IsKeyboard=\"true\"</code> 开启此功能", "BodyScrollTitle": "页面滚动", "BodyScrollIntro": "通过设置 <code>BodyScroll</code> 控制抽屉弹窗显示时是否允许滚动 <code>Body</code> 默认 false 不允许滚动", "Open": "点我打开", "Content": "抽屉内布局、组件等完全可以自定义", "Close": "关闭抽屉", "PlacementContent": "点击遮罩阴影部分自动关闭抽屉"}, "BootstrapBlazor.Server.Components.Samples.Consoles": {"Title": "Console 控制台", "Description": "控制台组件，一般用于后台任务的输出", "NormalTitle": "基本用法", "NormalIntro": "显示后台推送的消息", "OnClearTitle": "可清空的控制台", "OnClearIntro": "通过设置 <code>OnClear</code> 回调方法对数据集进行清空操作，由于本例与上例使用相同数据源，会导致上例中数据源更新延时", "ColorTitle": "不同颜色的消息", "ColorIntro": "通过设置 <code>ConsoleMessageItem</code> 的 <code>Color</code> 参数进行对颜色的更改", "IsAutoScrollTitle": "自动滚屏", "IsAutoScrollIntro": "通过设置 <code>ShowAutoScroll</code> 属性值开启或者关闭自动滚屏功能", "ShowAutoScrollDescription": "设置 <code>ShowAutoScroll=\"true\"</code> 显示自动滚屏选项", "IsAutoScrollDescription": "通过设置 <code>IsAutoScroll</code> 设置开启自动滚屏", "ConsoleMessageItem": "ConsoleMessageItem 属性", "ShowAutoScrollTitle": "自动滚屏控制", "ShowAutoScrollIntro": "通过设置 <code>ShowAutoScroll</code> 显示 <code>Checkbox</code> 控制是否自动滚动屏幕"}, "BootstrapBlazor.Server.Components.Samples.Speeches.Index": {"SpeechTitle": "Speech 语音识别与合成", "SpeechDescription": "通过麦克风语音采集转换为文字（STT），或者通过文字通过语音朗读出来（TTS）", "SpeechDescription1": "本套组件内置 <code>Azure</code> 与 <code>Baidu</code> 语音服务，本示例依赖于", "SpeechDescription2": "使用本组件时需要引用其组件包", "SpeechLiTitle": "实战体验", "SpeechLi1": "点击 <b>开始</b> 按钮后，对着电脑说 <b><i>请帮我把灯打开</i></b>", "SpeechLi2": "电脑接收到指令后，会询问 <b><i>您确认要把灯打开吗？请您确认！</i></b>", "SpeechLi3": "您对着电脑说： <b><i>确认</i></b>", "SpeechLi4": "电脑执行指令，在下方文本框内输入 <b><i>已经为您打开</i></b> 文本", "SpeechButtonText": "开始体验", "SpeechGroupBoxTitle": "指令输出区", "SpeechGroupBoxHeaderText": "指令面板", "SpeechTips1": "1. 注册服务", "SpeechTips1Text": "增加 Baidu 语音服务", "SpeechTips2": "2. 使用服务", "SpeechTips2Text1": "语音识别服务", "SpeechTips2Text2": "语音合成服务"}, "BootstrapBlazor.Server.Components.Samples.Speeches.SpeechWaves": {"SpeechWaveTitle": "SpeechWave 语音波形图", "SpeechWavesDescription": "开始采集语音时显示的波形动态图", "NormalTitle": "基础用法", "NormalIntro": "显示波形图", "ShowUsedTimeTitle": "显示时长", "ShowUsedTimeIntro": "通过 <code>ShowUsedTime</code>", "ValueTitle": "参数控制是否显示", "ValueIntro": "通过 <code>Show</code> 值控制是否显示波形图", "ValueButtonText1": "隐藏", "ValueButtonText2": "显示", "ShowAttr": "是否开始", "ShowUsedTimeAttr": "是否显示时长", "OnTimeoutAttr": "识别结束后超时回调方法", "TotalTimeSecondAttr": "语音识别设置总时长超出过调用 OnTimeout 回调"}, "BootstrapBlazor.Server.Components.Samples.Speeches.Synthesizers": {"SynthesizersTitle": "Synthesizer 语音合成", "SynthesizersSubTitle": "将文本内容转化为语音", "SynthesizerNormalTitle": "普通用法", "SynthesizerNormalIntro": "点击开始合成后对文本内容进行语音合成"}, "BootstrapBlazor.Server.Components.Samples.Speeches.Recognizers": {"RecognizersTitle": "Recognizer 语音识别", "RecognizersSubTitle": "通过语音采集设备将语音转化为文本", "RecognizerNormalTitle": "普通用法", "RecognizerNormalIntro": "点击开始识别后对录入语音进行识别", "RecognizerNormalDescription": "使用说明", "RecognizerNormalTips": "点击 <b>开始识别</b> 后，脚本运行可能会提示要求录音权限，授权后开始讲话，5秒后（可配置）右侧文本框显示语音识别结果，请勿讲完话后直接点击 <p>结束识别</p>"}, "BootstrapBlazor.Server.Components.Samples.DragDrops": {"Title": "DragDrop 拖拽", "Description": "用于拖拽使用", "NormalTitle": "基本用法", "NormalIntro": "简单拖拽", "CopyItemTitle": "复制到新容器", "CopyItemIntro": "使用<code>CopyItem</code>复制一份新的到新位置", "AcceptsTitle": "限制拖入的内容", "AcceptsIntro": "使用<code>Accepts</code>只允许左侧拖入10并且使用<code>AllowsDrag</code>限制2不能被拖动，使用<code>MaxItems</code>限制右侧最多拥有6个节点", "EventTitle": "各种事件", "EventIntro": "当拖入数量超限时<code>OnItemDropRejectedByMaxItemLimit</code>，当拖拽被禁止时<code>OnItemDropRejected</code>，返回底下的Item<code>OnReplacedItemDrop</code>,返回放下的Item<code>OnItemDrop</code>", "A1": "最大数量,null为不限制", "A2": "内容组件", "M1": "是否运行拖放", "M2": "节点是否允许被拖拽", "M3": "复制一个新的 Item 到目标位置", "M4": "针对 Item 添加特殊的 css class", "M5": "Item 释放时的事件", "M6": "Item 释放被拒绝时的事件", "M7": "当 Item 在另一个 Item 上，不是空白处被释放时的事件", "M8": "Item 因为 Dropzone 内最大数量超限被拒绝时的事件"}, "BootstrapBlazor.Server.Components.Samples.Labels": {"LabelsTitle": "组件标签", "LabelsDescription": "本套组件中有 <code>ValidateForm</code> <code>EditorForm</code> 以及多种继承 <code>ValidateBase&lt;TValue&gt;</code> 的 <b>表单组件</b>，这些组件中有一套特殊的显示前置标签逻辑，现在我们统一的梳理一下：", "LabelsDescriptionTips1": "<a href='/validate-form' target='_blank'>ValidateForm</a> 组件是 <b>可验证的</b> 表单组件，此组件内的表单组件会自动进行数据合规性检查，如果数据不合规将会阻止 <b>提交(Submit)</b> 动作，是数据提交中使用最最最频繁的组件", "LabelsDescriptionTips2": "<a href='/editor-form' target='_blank'>EditorForm</a> 组件是普通的表单组件，此组件绑定 <code>Model</code> 后即可自动生成整个表单，大大减少重复性编码，外面套上 <code>ValidateForm</code> 即可开启 <b>数据合规性检查</b> 非常方便、简洁、高效", "LabelsDescriptionTips3": "以 <a href='input' target='_blank'>BootstrapInput</a> 输入框组件为例，阐述一下是否显示 <code>Label</code> 逻辑", "LabelsTips": "<code>ShowLabel</code> 的逻辑即就近原则，离自身越近的设置生效，如表单组件内置到 <code>ValidateForm</code> 组件中，即使 <code>ValidateForm</code> 设置 <code>ShowLabel=true</code>，表单组件自身设置 <code>ShowLabel=false</code> 时，标签最终结果为 <b>不显示</b>", "LabelsNormalTitle": "单独使用", "LabelsNormalIntro": "适用于数据录入", "LabelsEditorFormTitle": "EditorForm 中使用", "LabelsEditorFormIntro": "未套 <code>ValidateForm</code> 中使用", "LabelsValidateForm1Title": "EditorForm 内置 ValidateForm 中使用", "LabelsValidateForm1Intro": "内置 <code>ValidateForm</code> 中使用", "LabelsValidateForm2Title": "ValidateForm 中使用", "LabelsValidateForm2Intro": "默认自动开启显示标签", "LabelsNormalDescription": "未使用双向绑定时", "LabelsNormalTips1": "默认不会显示 <code>Label</code>", "LabelsNormalTips2": "通过 <code>ShowLabel</code> 属性进行控制是否显示", "LabelsNormalTips3": "设置 <code>DisplayText</code> 时显示内容", "LabelsNormalTips4": "未设置时渲染一个无内容的 <code>label</code> 组件进行占位", "LabelsNormalGroupBox1Title": "未双向绑定", "LabelsNormalGroupBox1Tips1": "第一个文本框未进行任何设置，不显示标签", "LabelsNormalGroupBox1Tips2": "第二个文本框设置 <code>ShowLabel='true' DisplayText=''</code> 显示无内容的占位标签", "LabelsNormalGroupBox1Tips3": "第三个文本框设置 <code>ShowLabel='true' DisplayText='Name'</code> 显示设置的内容标签", "LabelsNormalGroupBox1Tips4": "第四个文本框设置 <code>ShowLabel='true' DisplayText='@@null'</code> 显示无内容的占位标签", "LabelsNormalDescription2": "使用双向绑定时", "LabelsNormalGroupBox2Title": "双向绑定", "LabelsNormalGroupBox2Tips1": "第一个文本框设置 <code>@bind-Value='Dummy.Name'</code>，不显示标签", "LabelsNormalGroupBox2Tips2": "第二个文本框设置 <code>@bind-Value='Dummy.Name' ShowLabel='true' DisplayText='@Localizer[nameof(Foo.Address)]'</code> 显示设置的内容", "LabelsNormalGroupBox2Tips3": "第三个文本框设置 <code>@bind-Value='Dummy.Name' ShowLabel='true' DisplayText=''</code> 显示无内容占位标签", "LabelsNormalGroupBox2Tips4": "第四个文本框设置 <code>@bind-Value='Dummy.Name' ShowLabel='true' DisplayText='@null'</code> 显示资源文件机制下的标签内容 <code>Label</code>", "LabelsEditorFormDescription2": "<b>显示标签</b><div>未设置 <coe>EditorForm</coe> 组件的 <code>ShowLabel</code> 属性等同于设置为 <code>true</code>，所有组件 <b>显示</b> 标签</div>", "LabelsEditorFormDescription1": "<b>不显示标签</b><div>设置 <code>ShowLabel=\"false\"</code>，组件内的所有表单组件 <b>不显示</b> 标签</div>", "EditorFormLabelAlignRight": "<b>右对齐</b><div>通过 <code>LabelAlign=\"Alignment.Right\"</code> 设置标签右对齐</div>", "EditorFormLabelAlignCenter": "<b>居中对齐</b><div>通过 <code>LabelAlign=\"Alignment.Center\"</code> 设置标签居中对齐</div>", "LabelsValidateForm1Description1": "<b>显示标签</b><div>未设置 <coe>EditorForm</coe> 组件的 <code>ShowLabel</code> 属性，未设置时等同于设置为 <code>ShowLabel=\"true\"</code>，所有组件 <b>显示</b> 标签</div>", "LabelsValidateForm1Description2": "<b>不显示标签</b><div>设置 <code>ShowLabel=\"false\"</code>，组件内的所有表单组件 <b>不显示</b> 标签</div>", "LabelsValidateForm2Description1": "<b>显示标签</b><div>未设置 <coe>EditorForm</coe> 组件的 <code>ShowLabel</code> 属性，未设置时等同于设置为 <code>true</code>，所有组件 <b>显示</b> 标签</div>", "LabelsValidateForm2Description2": "<b>不显示标签</b><div>设置 <code>ShowLabel=\"false\"</code>，组件内的所有表单组件 <b>不显示</b> 标签</div>", "LabelsValidateForm2Description3": "<b>显示标签</b><div>设置样式 <code>form-inline</code>，组件内的所有表单组件标签前置</div>", "ValidateFormAlignRight": "<b>右对齐</b><div>通过设置样式 <code>form-inline-end</code> 标签右对齐</div>", "ValidateFormAlignCenter": "<b>居中对齐</b><div>通过设置样式 <code>form-inline-center</code> 标签居中对齐</div>", "LabelsRowLabelWidthTitle": "样式设置标签宽度", "LabelsRowLabelWidthIntro": "通过设置样式变量 <code>--bb-row-label-width</code> 控制标签宽度", "LabelsWidthTitle": "参数设置标签宽度", "LabelsWidthIntro": "通过设置变量 <code>LabelWidth</code> 控制标签宽度", "LabelsWidthDescription": "组件 <code>ValidateForm</code> <code>EditorForm</code> <code>BootstrapLabelSetting</code> <code>ValidateForm</code> 均可以设置标签宽度值，组件采用 <b>就近原则</b> 来确定最终值", "LabelsWidthCode1": "如上代码所示, 根据就近原则最终取值 <b>180</b>"}, "BootstrapBlazor.Server.Components.Pages.Install": {"InstallTitle": "安装", "GitP1": "Git 工具有很多种大家比较熟悉的，这里我介绍两款 Git 客户端工具", "GitP2": "推荐使用 <a href='https://fork.dev'>Fork</a> 工具进行安装，它能更好地和 <a href='https://visualstudio.microsoft.com/vs/getting-started/' target='_blank'>Visual Studio</a> 开发工具配合使用。", "NugetInstall": "Nuget 安装", "NugetP1": "使用 <a href='https://www.nuget.org/packages?q=BootstrapBlazor' target='_blank'>nuget.org</a> 进行 BootstrapBlazor 组件的安装", "EnvBuildTitle": "项目环境搭建", "EnvLi1": "安装 .net core sdk <a href='https://www.microsoft.com/net/download' target='_blank'>官方网址</a>", "EnvLi2": "安装 Visual Studio 最新版 <a href='https://visualstudio.microsoft.com/vs/getting-started' target='_blank'>官方网址</a>", "EnvLi3": "拉取项目代码 BootstrapBlazor <a href='{0}' target='_blank'>仓库网址</a>"}, "BootstrapBlazor.Server.Components.Components.Video": {"H1": "B 站相关视频链接", "L1": "[传送门]", "L2": "暂无"}, "BootstrapBlazor.Server.Components.Components.UpdateIntro": {"H1": "组件库 更新到", "B1": "首先感谢您对本套组件的关注，目前本套组件已经拥有将近 200 个组件，本组件是基于", "B2": "企业级组件库，提供如布局、导航、表单、数据、通知、图标、语音等几大类通用组件，每一个组件都经过精心设计，具有模块化、响应式和优秀的性能。从更多实际场景出发，满足多种场景的需求，极大的减少开发者时间成本，大大缩短开发周期，大幅提高开发效率，并提供了一套", "B3": "通用权限管理系统", "B4": "示例工程", "B5": "产品是由一支专业的全职技术团队进行维护，高效的响应速度，多元化的解决方案，长期提供支持，并提供企业级支持。目前已在多家知名国企内部使用，项目最高在线 <b>1200</b> 人稳定运行。右侧为国内人数最多的中文 Blazor QQ 社区二维码，欢迎扫描加群。", "P1": "组件更新到", "P2": "更新日志", "P3": "传送门", "P4": "如果组件给您带来了方便，请您帮忙给项目点亮"}, "BootstrapBlazor.Server.Components.Pages.Localization": {"Title": "本地化", "P1": "本地化是为给定语言和地区定制应用程序的过程.", "P2": "组件允许您将其 UI 元素转换为所需的语言。这包括按钮、过滤器操作符属性等文本。组件内部默认使用当前请求", "P3": "文化语言，本文将向您展示如何在应用程序中使用此功能", "T1": "阅读以下知识点前请先查看", "T2": "微软官方文档", "T3": "由于", "T4": "模式无法获取系统语言文化信息，默认文化信息为", "N1": "本地化在组件中的工作原理", "N2": "组件额外支持使用", "N3": "类型的键值信息作为资源文件，将其解析为", "N4": "中呈现的字符串", "N5": "包自带以下资源文件", "N6": "中文（zh）", "N7": "英语（en）", "N8": "德语（de）", "N9": "葡萄牙语（pu）", "N10": "组件内置本地化语言回退机制，如请求文化为", "N11": "时，如未提供相对应的文化资源文件时，内置逻辑通过父级文化进行尝试本地化，以", "N12": "为例回退机制如下", "N13": "到", "N14": "如果设置的本地化语言未提供资源文件回落后仍无法找到资源文件后，使用", "N15": "参数设置的文化信息进行本地化，默认为", "N16": "特别注意", "N17": "由于某些系统如", "N18": "等程序运行后线程无法获得文化信息，可以通过配置文件设置默认文化信息", "N19": "开启本地化功能", "N20": "配置文件", "N21": "通过", "N22": "设置回退文化信息，即未找到当前请求的文化信息时使用此配置文化，通过", "N23": "设置支持的文化集合", "N24": "启用 .NET 核心本地化服务", "N25": "增加 Bootstrap Blazor 组件", "N26": "增加多语言支持配置信息", "N27": "启用本地化", "N28": "增加 BootstrapBlazor 组件", "N29": "增加本地化服务配置信息", "N30": "启用本地化中间件并设置支持的文化信息", "N31": "实现 UI 本地化信息存储（例如，cookie）", "N32": "添加允许用户更改本地化的 UI", "N33": "请选择语言", "N34": "使用 api 方式 适用于 Server-Side 模式", "N35": "附加", "N36": "资源文件", "N37": "组件库即支持微软默认的", "N38": "格式资源，也支持嵌入式", "N39": "格式资源，以及特定物理", "N40": "文件", "N41": "资源文件为合并关系，寻址规则优先级为", "N42": "微软 resx 嵌入式资源文件", "N43": "外置 json 物理文件", "N44": "json 嵌入式资源文件", "N45": "内置 json 资源文件", "N46": "忽略文化信息丢失日志", "N47": "设置 RESX 格式多语言资源文件 如 Program.{CultureName}.resx", "N48": "设置 Json 格式嵌入式资源文件", "N49": "设置 Json 物理路径文件", "N50": "或者使用服务扩展方法", "N51": "忽略本地化键值文化信息丢失", "N52": "附加自己的 json 多语言文化资源文件 如 zh-TW.json", "N53": "设置 Json 物理路径文件", "N54": "启用 .NET 核心本地化服务", "N55": "增加 BootstrapBlazor 组件", "N56": "增加本地化", "N57": "实现 UI 本地化信息存储（例如，localStorage）", "N58": "与 Server-Side 一致", "N59": "更改默认语言设置", "N60": "设置默认文化为 zh-CN", "N61": "配置是否显示缺失本地化资源信息", "N62": "增加 BootstrapBlazor 服务", "N63": "忽略本地化键值文化信息丢失", "LocalizationFileDesc": "组件内置本地化资源文件为 <code>en</code> <code>zh</code> 由网友提供的其他本地化资源文件 <code>de</code> <code>es</code> <code>pt</code> <code>zh-TW</code> 放置在项目文件夹 <code>localization</code> 内，可自行下载通过注入服务引入到项目中", "AdditionalJsonFile": "额外本地化语言 <code>json</code> 文件", "ES": "西班牙语（es）", "TW": "中國台灣（zh-TW）"}, "BootstrapBlazor.Server.Components.Pages.Template": {"Title": "项目模板", "SubTitle": "Bootstrap Blazor App 模板", "P1": "为了方便大家利用这套组件快速搭建项目，作者制作了 <b>项目模板（<code>Project Templates</code>）</b>，使用 <code>dotnet new</code> 命令行模式，使用步骤如下：", "P2": "1. 安装项目模板", "P3": "2. 创建工程", "P4": "创建工程后在当前文件夹内会生成 <code>BootstrapBlazorApp</code> 解决方案，<code>src</code> 目录内包含 <code>Server-Side</code> <code>Wasm</code> 两种类型的工程，均可以直接 <kbd>F5</kbd> 运行", "P5": "3. 更新项目模板", "P6": "注:此命令为检查是否有可用于当前安装的模板包的更新并安装这些更新。", "P7": "4. 卸载项目模板"}, "BootstrapBlazor.Server.Components.Pages.Globalization": {"Title": "全球化", "SubTitle": "ASP.NET Core Blazor 全球化", "P1": "阅读以下知识点前请先查看 <a href='https://learn.microsoft.com/zh-cn/aspnet/core/blazor/globalization-localization?WT.mc_id=DT-MVP-5004174' target='_blank'>微软官方文档</a>"}, "BootstrapBlazor.Server.Components.Components.InstallContent": {"P5": "使用 BootstrapBlazor Project Template 扩展创建项目", "InstallByTemplate": "您可以通过 <a href=\"template\">[传送门]</a> 下载安装扩展后，通过扩展创建项目, <code>强烈推荐</code>", "P9": "使用 Visual Studio 创建项目", "P10": "步骤一、创建项目", "P11": "打开 Visual Studio", "P12": "创建一个新项目", "P13": "选择", "P14": "并单击", "P15": "下一步", "P16": "为项目选择一个名称和位置，然后点击", "P17": "步骤二、将 BootstrapBlazor 组件添加到现有项目", "P18": "通过", "P19": "源搜索", "P20": "右键点击解决方案中的项目并选择", "P21": "安装 BootstrapBlazor 包", "P22": "添加样式表文件", "P23": "在宿主文件中增加主题样式表", "P24": "需引用 BootstrapBlazor.FontAwesome 包", "P25": "添加 JavaScript 文件", "P26": "增加代码", "P27": "注册 BootstrapBlazor 服务", "P28": "增加命名空间引用", "P29": "将以下内容添加到", "P30": "文件中，以便", "P31": "文件中能识别组件", "AddRootText": "增加 <code>BootstrapBlazorRoot</code> 组件到 <code>App.razor</code> 文件中 (用下面代码替换原内容即可)", "P35": "正在玩命开发中", "P36": "步骤三、页面中使用组件", "P37": "最后一步是在页面中使用", "P38": "组件并在浏览器中运行它。例如", "P39": "在页面中增加一个", "P40": "按钮", "P41": "测试", "P42": "在", "P43": "中", "P44": "运行应用程序", "Tips2": "如果使用微软自带模板创建工程，请移除默认 <code>Bootstrap</code> 样式表 <code>&lt;link rel='stylesheet' href='css/bootstrap/bootstrap.min.css' /&gt;</code>。如果使用 <code>FontAwesome</code> 图标库可安装扩展包 <code>BootstrapBlazor.FontAwesome</code>"}, "BootstrapBlazor.Server.Components.Pages.Install_Server": {"Title": "服务器端 Blazor 安装教程", "D1": "选择", "D2": "项目类型，然后点击", "Configure": "配置服务", "CodeComment": "添加本行代码"}, "BootstrapBlazor.Server.Components.Pages.Theme": {"H1": "组件主题", "H2": "测试", "P1": "目前网站主题支持如下", "P2": "Motronic (已集成)", "P3": "Lay<PERSON> (完善中)", "P4": "<PERSON><PERSON> (完善中)", "P5": "Ant Design (完善中)", "P6": "主题切换", "P7": "如切换为", "P8": "主题，则请在原有", "P9": "内增加样式文件如下", "P10": "增加此行"}, "BootstrapBlazor.Server.Components.Pages.Install_Maui": {"H1": "环境准备", "P1": "确保系统安装", "P2": "版本", "P3": "使用 Visual Studio 创建项目", "P4": "步骤一、创建项目", "P5": "创建一个新项目", "P6": "选择", "P7": ".NET MAUI Blazor 应用", "P8": "并单击", "P9": "下一步", "P10": "为项目选择一个名称和位置，然后点击", "P11": "下一步", "P12": "创建", "P13": "步骤二、将 BootstrapBlazor 组件添加到现有项目", "P14": "1. 通过 <b>nuget.org</b> 源搜索 <code>BootstrapBlazor</code>", "P15": "右键点击解决方案中的项目并选择", "P16": "2. 安装 BootstrapBlazor 包最新版", "P17": "3. 添加样式表文件", "P18": "在宿主文件中增加主题样式表", "P19": "需引用 BootstrapBlazor.FontAwesome 包", "P20": "4. 添加 JavaScript 文件", "P21": "增加代码", "P22": "5. 注册 BootstrapBlazor 服务", "P23": "6. 增加命名空间引用", "P24": "将以下内容添加到", "P25": "文件中，以便", "P26": "文件中能识别组件", "AddRootText": "7. 增加 <code>BootstrapBlazorRoot</code> 组件到 <code>Routes.razor</code> 文件中", "P30": "正在玩命开发中", "P31": "步骤三、页面中使用组件", "P32": "最后一步是在页面中使用 <code>BootstrapBlazor</code> 组件并在浏览器中运行它。例如：", "P33": "请移除默认的 <code>Bootstrap</code> 样式链接 <code>&lt;link rel='stylesheet' href='css/bootstrap/bootstrap.min.css' /&gt;</code>; 本项目图标使用 <code>FontAwesome</code> 图标库，请自行引用或者引用 <code>BootstrapBlazor.FontAwesome</code> 包", "P34": "1. 在页面中增加一个", "P35": "按钮", "P36": "测试", "P37": "2. 在", "P38": "中", "P39": "运行应用程序", "P40": "同时使用 Razor 和 Xaml 的混合模式", "P41": "1. Pages文件夹中添加一个名为", "P42": "的 MAUI ContentView 控件文件", "P43": "2. 修改", "P44": "文件中的", "P45": "为", "P46": "（也可使用", "P47": "等其他导航组件）", "P48": "在", "P49": "中", "P50": "运行应用程序", "P51": "MAUI Blazor 安装教程"}, "BootstrapBlazor.Server.Components.Pages.Install_wasm": {"Install_wasmHeading": "客户端 Blazor 安装教程", "Install_wasmDescription": "选择 <b>Blazor WebAssembly App</b> 项目类型，然后点击 <b>Create</b>", "Title": "客户端渲染模式", "Install_wasmCodeComment": "添加本行代码"}, "BootstrapBlazor.Server.Components.Samples.Dividers": {"Title": "Divider 分割线", "Description": "区隔内容的分割线。", "NormalTitle": "基础用法", "NormalIntro": "对不同章节的文本段落进行分割。", "AlignmentTitle": "设置文案", "AlignmentIntro": "可以在分割线上自定义文案内容。", "IconTitle": "设置图标", "IconIntro": "可以在分割线上自定义文案内容。", "VerticalTitle": "垂直分割", "VerticalIntro": "进行垂直显示分割线", "ChildContentTitle": "自定义内容", "ChildContentIntro": "通过设置 <code>ChildContent</code> 内容可以自定义任意内容", "Desc1": "设置分割线显示文字", "Desc2": "设置分割线显示图标", "Desc3": "设置分割线显示文字对齐方式", "Desc4": "设置分割线是否为垂直分割", "Desc5": "ChildContent 模板", "NormalContent1": "青春是一个短暂的美梦, 当你醒来时, 它早已消失无踪", "NormalContent2": "少量的邪恶足以抵消全部高贵的品质, 害得人声名狼藉", "AlignmentDivider1": "少年包青天", "AlignmentDivider2": "英雄联盟", "AlignmentDivider3": "学习语录", "AlignmentContent1": "头上一片晴天，心中一个想念", "AlignmentContent2": "骑士归来之时，断剑重铸之日", "AlignmentContent3": "一只萌萌哒图标", "AlignmentContent4": "学习使我快乐", "IconContent1": "头上一片晴天，心中一个想念", "IconContent2": "骑士归来之时，断剑重铸之日", "IconContent3": "一只萌萌哒图标", "IconContent4": "学习使我快乐", "IconBookmark": "书签", "VerticalContent1": "雨纷纷", "VerticalContent2": "旧故里", "VerticalContent3": "草木深", "VerticalDivider": "分割线", "ChildContent1": "头上一片晴天，心中一个想念", "ChildContent2": "骑士归来之时，断剑重铸之日", "DividerChildContent": "我是自定义内容 <code>div</code> 元素"}, "BootstrapBlazor.Server.Components.Samples.Scrolls": {"ScrollTitle": "Scroll 滚动条", "ScrollDescription": "给高度或者宽度超标的组件增加滚动条", "ScrollTips": "其元素必须拥有固定高度时才可呈现滚动条，可以通过外套元素设置其 <code>height</code> 属性", "ScrollToBottom": "滚动到底部", "ScrollNormalTitle": "普通用法", "ScrollNormalIntro": "给组件增加滚动条，通过设置 <code>Height</code> 高度值为 200px 使内部子元素高度为 500px 时出现滚动条", "Desc1": "子组件", "Desc2": "组件高度", "ScrollNormalDescription": "请滚动右侧滚动条", "ScrollNormalBottom": "我是最底端"}, "BootstrapBlazor.Server.Components.Samples.LayoutPages": {"H1": "整页面级别组件", "P1": "通过内置组件", "P2": "进行整页面布局，可通过", "P3": "或者安装", "P4": "项目插件选择", "P5": "Bootstrap Blazor 项目模板", "P6": "即可自动生成，详细文档请点击", "P7": "传送门", "P8": "特别注意", "P9": "组件开启", "P10": "多标签", "P11": "模式后，如果有", "P12": "Razor 组件", "P13": "在额外的程序集中时，请正确设置", "P14": "属性值，以便标签组件内路由正确解析，相关文档", "P15": "传送门", "P16": "布局调整", "P17": "显示页脚", "P18": "多标签", "P19": "单页", "P20": "固定调整", "P21": "固定标签", "P22": "固定页头", "P23": "固定页脚", "P24": "收缩调整", "P25": "请点击", "P26": "中的 <b>按钮</b>", "P27": "高度调整", "P28": "视图高度", "P29": "Tab 测试", "P30": "点击", "P31": "测试", "P32": "测试", "P33": "按钮后，会增加一个新的标签页", "P34": "以代码的形式导航到新标签页", "P35": "导航"}, "BootstrapBlazor.Server.Components.Samples.Layouts": {"LayoutsTitle": "Layout 布局", "LayoutsDescription1": "用于布局的容器组件，方便快速搭建页面的基本结构：", "LayoutsDescription1_Container": "外层容器。当子元素中包含 <code>Header</code> 或 <code>Footer</code> 时，全部子元素会垂直上下排列，否则会水平左右排列。", "LayoutsDescription1_Header": "顶栏容器", "LayoutsDescription1_Side": "侧边栏容器", "LayoutsDescription1_Main": "主要区域容器", "LayoutsDescription1_Footer": "底栏容器", "LayoutsDescription2": "组件概述", "LayoutsDescription2_Layout": "布局容器，其下可嵌套 <code>Header</code> <code>Sider</code> <code>Main</code> <code>Footer</code> 或 <code>Layout</code> 本身，可以放在任何父容器中", "LayoutsDescription2_Header": "顶部布局，自带默认样式，其下可嵌套任何元素", "LayoutsDescription2_Side": "侧边栏，自带默认样式及基本功能，其下可嵌套任何元素", "LayoutsDescription2_Main": "内容部分，自带默认样式，其下可嵌套任何元素", "LayoutsDescription2_Footer": "底部布局，自带默认样式，其下可嵌套任何元素", "LayoutsTips1": " 以上组件采用了 flex 布局，使用前请确定目标浏览器是否兼容。此外，<code>Layout</code> 的子元素只能是后四者，后四者的父元素也只能是 <code>Layout</code>", "LayoutsTips2": "注意事项", "LayoutsTips3": "为了方便大家利用这套组件快速搭建项目，作者制作了 <b>项目模板（<code>Project Templates</code>）</b>，使用 <code>dotnet new</code> 命令行，与 <code>Bootstrap Blazor App Extension 扩展插件</code> 通过 <code>Visual Studio</code> 新建项目创建，具体使用方法与步骤请点击 <a href='template' target='_blank'>传送门</a>", "LayoutsDemosTitle": "常见页面布局", "LayoutsUpAndDownTitle": "上中下布局", "LayoutsUpAndDownIntro": "常见于互联网网站布局", "LayoutsMiddleLeftRightTitle": "中部左右结构布局", "LayoutsMiddleLeftRightIntro": "中部采用左右结构，多用于后台管理网站布局，当布局模板中增加 <code>Side</code> 组件时默认采用上中下，中部采用左右结构布局", "LayoutsLeftRightTitle": "左右结构", "LayoutsLeftRightIntro": "右侧采用上中下结构，多用于后台管理网站布局，通过设置 <code>IsFullSide</code> 属性值来控制布局为左右结构", "LayoutsCustomPercentTitle": "自定义侧边栏宽度支持百分比", "LayoutsCustomPercentIntro": "通过设置 <code>SideWidth</code> 属性控制侧边栏宽度，支持百分比写法，设置 <code>0</code> 时关闭设置宽度功能，采用内部子控件撑满宽度特性", "LayoutsAppTitle": "实战应用", "LayoutsAppIntro": "实际项目中的例子", "LayoutsPageTitle": "整页面级别的组件", "LayoutsPageIntro": "通过 <code>IsPage</code> 对整页面布局", "LayoutsPageTips1": "通过设置 <code>IsPage</code> 属性设置 <code>Layout</code> 组件是否掌控整页面级别的布局方式，设置为 <code>true</code> 后，本组件采用整屏布局", "LayoutsPageHrefTitle": "整页面组件生成器", "Layouts_Header_Description": "页头组件模板", "Layouts_Side_Description": "侧边栏组件模板", "Layouts_SideWidth_Description": "侧边栏宽度，支持百分比，设置 0 时关闭宽度功能", "Layouts_Main_Description": "内容组件模板", "Layouts_Footer_Description": "页脚组件模板", "Layouts_Menus_Description": "整页面布局时侧边栏菜单数据集合", "Layouts_IsFullSide_Description": "侧边栏是否占满整个左边", "Layouts_IsPage_Description": "是否为整页面布局", "Layouts_IsFixedFooter_Description": "是否固定 Footer 组件", "Layouts_IsFixedHeader_Description": "是否固定 Header 组件", "Layouts_ShowCollapseBar_Description": "是否显示收缩展开 Bar", "Layouts_ShowSplitBar_Description": "是否显示左右分割栏", "Layouts_SidebarMinWidth_Description": "侧边栏最小宽度", "Layouts_SidebarMaxWidth_Description": "侧边栏最大宽度", "Layouts_ShowFooter_Description": "是否显示 Footer 模板", "Layouts_ShowGotoTop_Description": "是否显示返回顶端按钮", "Layouts_UseTabSet_Description": "是否开启多标签模式", "Layouts_IsFixedTabHeader_Description": "是否固定多标签 Header", "Layouts_AdditionalAssemblies_Description": "额外程序集合，传递给 Tab 组件使用", "Layouts_OnCollapsed_Description": "收缩展开回调委托", "Layouts_OnClickMenu_Description": "点击菜单项时回调委托", "Layouts_TabDefaultUrl_Description": "设置 Tab 组件默认标签页", "Layouts_IsAccordion_Description": "是否设置手风琴菜单", "LayoutsAppTips1": "实际使用过程中，侧边栏一般有两种用法", "LayoutsAppTips2": "侧边栏充满整个屏幕，当菜单总体高度超出屏幕后出现滚动条", "LayoutsAppTips3": "侧边栏不做处理高度由内容自动撑开", "LayoutsAppTips4": "由于存在多种运用方式，所以本组件未进行封装，由自己决定如何进行布局，下面是实际运用中的实战示例", "LayoutsAppTips5": "为了方便大家学习与比较以下实战示例中尽量使用 <code>style</code> 内联样式", "LayoutsAppTips6": "本例点击左侧菜单展开后出现滚动条", "LayoutsAppTips7": "右侧面板中可用区域默认为全部，适用于带 <code>Tab</code> 组件的布局，本例中为了美观 <code>Main</code> 模板中内置了一个 <code>div</code> 并且设置样式为 <code>style='padding: 1rem;'</code>", "LayoutsAppTips8": "本例点击左侧菜单展开后不出现滚动条会撑开父容器高度", "LayoutsAppTips9": " <code>Layout</code> 组件与 <code>Tab</code> 组件配合使用实战示例请参阅 <a href='/tab'>Tab 组件</a>", "ShowTabContextMenu": "是否显示右键菜单", "BeforeTabContextMenuTemplate": "前面右键菜单模板", "TabContextMenuTemplate": "右键菜单后续模板", "TabContextMenuRefreshIcon": "右键菜单刷新按钮图标", "TabContextMenuCloseIcon": "右键菜单关闭按钮图标", "TabContextMenuCloseOtherIcon": "右键菜单关闭其他按钮图标", "TabContextMenuCloseAllIcon": "右键菜单关闭全部按钮图标", "OnBeforeShowContextMenu": "右键菜单弹出前回调方法"}, "BootstrapBlazor.Server.Components.Samples.Footers": {"FootersTitle": "Footer 页脚组件", "FootersDescription": "显示在网页的最下方，提供返回顶端按钮", "FootersTips": "<code>Footer</code> 组件使用时注意样式表 <code>position</code> 属性的设置", "FooterNormalTitle": "基础用法", "FooterNormalIntro": "传递滚动条组件的 ID 给页脚组件", "FootersContent1": "底部显示 Footer 组件，点击返回顶端时页面自动滚动回顶端", "FootersContent2": "本示例传递的是组件客户端 ID", "Desc1": "页脚组件显示的文字", "Desc2": "页脚组件控制的滚动条组件 ID", "ShowGotoDesc": "是否显示右侧导航小组件", "ChildContentDesc": "子组件模板", "FooterShowGotoTitle": "是否显示 Goto 导航", "FooterShowGotoIntro": "通过设置 <code>ShowGoto</code> 参数，关闭 <code>Footer</code> 右侧导航小组件", "FooterChildContentTitle": "自定义内容", "FooterChildContentIntro": "通过设置 <code>ChildContent</code> 参数自定义组件内部显示内容"}, "BootstrapBlazor.Server.Components.Samples.Repeaters": {"RepeaterTitle": "Repeat 组件", "RepeaterDescription": "用户多个重复区域的组件", "RepeaterNormalTitle": "基础用法", "RepeaterNormalIntro": "通过设置 <code>ItemTemplate</code> 模板自定义重复区域内容", "RepeaterLoadingTitle": "数据加载", "RepeaterLoadingIntro": "未提供 <code>Items</code> 时通过设置 <code>ShowLoading</code> 控制是否显示正在加载提示信息，可通过 <code>LoadingTemplate</code> 自定义加载信息", "RepeaterEmptyTitle": "空数据", "RepeaterEmptyIntro": "数据集合 <code>Items</code> 为空时 <code>ShowEmpty</code> 控制是否显示无数据提示信息，可通过 <code>EmptyTemplate</code> 自定义空数据模板，可通过 <code>EmptyText</code> 自定义空数据文字信息", "RepeaterContainerTitle": "容器模板", "RepeaterContainerIntro": "通过设置 <code>ContainerTemplate</code> 容器模板，模板内重复部分仍然通过 <code>ItemTemplate</code> 渲染，本例中通过 <code>ContainerTemplate</code> 将组件渲染成 <code>Table</code>"}, "BootstrapBlazor.Server.Components.Samples.Rows": {"RowsTitle": "Row布局组件", "RowsDescription": "可简单设置一行显示的组件数量", "RowCommonTitle": "放置普通控件", "RowCommonIntro": "将自己的组件放到 <code>Row</code> 内部即可", "RowCommonDescription": "行显示 3 个，采用 <code>Row</code>", "RowFormInlineTitle": "放置表单控件（内联）", "RowFormInlineIntro": "当放置表单控件时，可以指定 <code>RowType</code> 为 <code>Inline</code>，会将 <b>Label</b> 显示在左边，控件充满剩余空间", "RowFormTitle": "放置表单控件", "RowFormIntro": "当放置表单控件时，可以指定 <code>RowType</code> 为 <code>Normal</code>，会将 <b>Label</b> 显示在上边，控件充满", "RowFormInlineDescription": "本例中 <code>Row</code> 组件内置于 <code>ValidateForm</code> 组件内，自动增加前置 <code>Label</code> 标签", "RowNestedTitle": "嵌套使用", "RowNestedIntro": "<code>Row</code> 组件支持嵌套使用，比如下面最外层的 <code>Row</code> 设置一行显示两个控件，第一个是 <code>TextBox</code>，第二个还是 <code>Row</code>，第二个 <code>Row</code> 继续设置显示两个控件", "RowSpanTitle": "跨列功能", "RowSpanIntro": "<code>Row</code> 组件可以通过指定 <code>ColSpan</code> 值设置跨列数，组合这些功能可以实现复杂布局", "RowSpanTips1": "行显示 4 个", "RowSpanTips2": "行显示 2 个", "RowSpanTips3": "行显示 4 个 <code>Address</code> 占 2 列", "RowSpanTips4": "行显示 4 个，第二个组件 <code>ColSpan</code> 设置为 3", "RowSpanTips5": "行显示 2 个，第一个组件 <code>ColSpan</code> 设置为 3", "RowSpanTips6": "行显示一个组件", "RowsItemsPerRow": "设置一行显示几个控件", "RowsRowType": "设置排版格式，子Row如果不指定，会使用父Row的设置", "RowsColSpan": "设置子Row跨父Row列数", "RowsMaxCount": "设置行内最多显示的控件数", "Normal": "正常", "Inline": "行内"}, "BootstrapBlazor.Server.Components.Samples.Skeletons": {"SkeletonsTitle": "Skeleton 骨架屏", "SkeletonsDescription": "在需要等待加载内容的位置提供一个占位图形组合", "SkeletonsTipsTitle": "何时使用", "SkeletonsTips1": "网络较慢，需要长时间等待加载处理的情况下", "SkeletonsTips2": "图文信息内容较多的列表/卡片中", "SkeletonsTips3": "只在第一次加载数据的时候使用", "SkeletonsTips4": "可以被 Spin 完全代替，但是在可用的场景下可以比 Spin 提供更好的视觉效果和用户体验", "SkeletonsImgTitle": "图片骨架屏", "SkeletonsImgIntro": "适用于头像、图片等类型加载时显示", "SkeletonsImgDescription": "通过设置 <code>Circle</code> 属性可以设置为圆形显示", "SkeletonsParagraphTitle": "段落骨架屏", "SkeletonsParagraphIntro": "适用于大段文字等类型加载时显示", "SkeletonsParagraphDescription": "默认段落骨架屏仅显示三行，如果需要多行占位，请放置多个 <code>SkeletonParagraph</code> 即可", "SkeletonsFormTitle": "表单骨架屏", "SkeletonsFormIntro": "适用于编辑表单加载时显示", "SkeletonsTableTitle": "表格骨架屏", "SkeletonsTableIntro": "适用于编辑表格加载时显示", "SkeletonsTreeTitle": "树骨架屏", "SkeletonsTreeIntro": "适用于树组件加载时显示"}, "BootstrapBlazor.Server.Components.Pages.Breakpoints": {"Heading": "断点", "Heading1": "断点是可自定义的宽度，它决定了响应式布局在 Bootstrap 中跨设备或视口大小的行为方式", "Paragraph1": "可用断点", "Paragraph2": "Bootstrap 包括六个默认断点，有时称为网格层，用于响应式构建", "TableHeading1": "断点名称", "TableHeading2": "类后缀", "TableHeading3": "阈值", "Name1": "超小", "Name2": "小号", "Name3": "中号", "Name4": "大号", "Name5": "超大", "Name6": "特大", "Amount": "像素", "Footer1": "组件内默认已经适配特大屏，很多默认都是", "Footer2": "如", "Footer3": "默认弹窗大小为\","}, "BootstrapBlazor.Server.Components.Pages.Layout": {"Heading": "组件分层", "Para1": "由于弹窗组件比较多，再某些场景下会使用多种弹窗进行组合，本章节讲述如何对组件进行分层管理。下面是", "Para2": "提供的内置", "Para3": "值", "Para4": "本套组件额外增加了", "Para5": "各组件分层如下", "Para6": "测试环节", "Button": "测试"}, "BootstrapBlazor.Server.Components.Samples.GlobalException": {"Title": "全局异常", "Introduce": "<p>增加组件 <code>ErrorLogger</code> 通过本组件可以对全局的日志、异常进行统一输出；目前由于 <code>Blazor</code> 框架并未提供一个类似 <code>MVC</code> 的 <b>全局异常</b> 整体解决方案；通过使用 <code>ErrorLogger</code> 组件 <b>无需任何额外代码</b> 即可进行全局异常捕获与处理</p>", "H1": "使用方法", "Step1": "1. <code>Startup</code> 文件中增加 <code>AddLogging</code> 开启 <code>net core</code> 系统日志功能，支持其他第三方 <code>Logger</code> 组件", "Step1Introduce": "使用 <code>AddFileLogger</code> 需要引用 <b>Longbow.Logging</b> 组件包 <a href='https://www.nuget.org/packages/Longbow.Logging/' target='_blank'>[传送门]</a>", "Step2": "2. 使用 <code>BootstrapBlazorRoot</code> 已经内置默认开启，可通过设置 <code>EnableErrorLogger</code> 值 <code>false</code> 关闭", "Step3": "3. 代码中输入错误信息", "Step4": "4. 控制台输出可见日志信息", "Step5": "5. 控制是否显示错误明细信息", "Step5Intro": "通过配置文件 <code>appsettings.json</code>，开发环境下为 <code>appsettings.Development.json</code>，未设置时默认为 <code>false</code> 不显示，默认行为仅做弹窗提示防止敏感信息暴露", "Block1Title": "测试", "Block1Intro": "本功能是通过代码产生异常信息，由全局捕获组件统一处理", "ExceptionTestIntroduce": "本例代码中写了一个除以零的错误代码，并且未使用 <code>try/catch</code> 对异常进行捕获，系统并不会崩溃导致不可用，<ul class='ul-demo'><li><code>Debug</code> 模式下会显示错误的 <b>描述信息</b> 与 <b>堆栈信息</b></li><li><code>Release</code> 模式下默认使用 <code>Toast</code> 进行弹窗提示</li></ul>", "ButtonText": "测试", "Block2Title": "自定义错误处理", "Block2Intro": "通过设置 <code>OnErrorHandleAsync</code> 回调方法，设置自定义异常处理逻辑", "DialogTitle": "弹窗中异常捕获", "DialogIntro": "点击按钮弹出弹窗，弹窗内按钮触发异常，错误显示在弹窗内", "DialogText": "弹窗", "PageErrorTitle": "页面异常捕获", "PageErrorIntro": "点击按钮导航到页面生命周期内出错的页面，错误显示在当前页面，不影响菜单以及整体页面布局"}, "BootstrapBlazor.Server.Components.Pages.GlobalOption": {"Title": "全局配置", "SubTitle": "组件库提供一种对当前应用程序中所有组件进行配置的方法，通过 <code>BootstrapBlazorOptions</code> 全局配置类实现此功能"}, "BootstrapBlazor.Server.Components.Samples.Splits": {"SplitsTitle": "Split 面板分割", "SplitsNormalTitle": "基础用法", "SplitsNormalIntro": "左右分割", "SplitsPanel1": "我是左侧面板", "SplitsPanel2": "我是右侧面板", "SplitsPercentTitle": "设置初始化百分比", "SplitsPercentIntro": "通过设置 <code>Basis</code> 属性来设置初始化位置占比", "SplitsVerticalTitle": "垂直分割", "SplitsVerticalIntro": "通过设置 <code>IsVertical</code> 属性控制垂直分割面板", "SplitsPanel3": "我是上部面板", "SplitsPanel4": "我是底部面板", "SplitsNestedTitle": "嵌套使用", "SplitsNestedIntro": "通过嵌套 <code>Split</code> 组件进行组合布局", "SplitsMinimumTitle": "最小值", "SplitsMinimumIntro": "通过设置 <code>FirstPaneMinimumSize</code> <code>SecondPaneMinimumSize</code> 值限制面板最小尺寸，支持所有单位。本例中设置左右面板最小值均为 <code>10%</code>", "SplitsPanel5": "上边面板", "SplitsPanel6": "下边面板", "SplitsPanel7": "右边面板", "SplitsCollapsibleTitle": "折叠/展开", "SplitsCollapsibleIntro": "通过设置 <code>IsCollapsible</code> 属性来设置是否可以折叠面板", "SplitsPanel8": "上边面板", "SplitsPanel9": "下边面板", "SplitsPanel10": "右边面板", "SplitsIsVertical": "垂直分割", "SplitsBasis": "第一个窗格位置占比", "SplitsFirstPaneTemplate": "第一个窗格模板", "SplitsSecondPaneTemplate": "第二个窗格模板", "SplitsIsCollapsible": "是否可以展开收起面板", "SplitsIsKeepOriginalSize": "折叠后恢复时是否保持原始大小", "SplitsShowBarHandle": "是否显示拖动条", "SplitsBarHandleShow": "显示拖动栏", "SplitsBarHandleHide": "隐藏拖动栏", "SplitsOnResizedAsync": "面板尺寸改变时回调方法", "SplitsCollapsibleTrue": "显示调整按钮", "SplitsCollapsibleFalse": "隐藏调整按钮", "SplitsFirstPaneMinimumSize": "第一个窗格最小尺寸支持任意单位如 10px 20% 5em 1rem 未提供单位时默认为 px", "SplitsSecondPaneMinimumSize": "第二个窗格最小尺寸支持任意单位如 10px 20% 5em 1rem 未提供单位时默认为 px", "SplitSetLeftTitle": "代码设置面板宽度", "SplitSetLeftIntro": "通过组件实例方法 <code>SetLeftWidth</code> 设置左侧/上侧面板宽度，右侧/下侧面板宽度自适应"}, "BootstrapBlazor.Server.Components.Samples.Dropdowns": {"Title": "Dropdown 下拉菜单", "Description": "将动作或菜单折叠到下拉菜单中", "NormalTitle": "基础用法", "NormalIntro": "使用 <code>TagName='a'</code> 开启带有 button 标签的下拉表", "NullTitle": "Dropdown 空下拉菜单", "NullIntro": "允许空 <code>Items</code> 存在的下拉菜单", "ColorTitle": "带有颜色的下拉框", "ColorIntro": "提供各种颜色的警告信息框 引用 <code>Color='Color.Primary'</code> 等颜色及样式类来定义下拉菜单的外在表现", "SplitTitle": "分裂式按钮下拉菜单", "SplitIntro": "可用与单个按钮下拉菜单近似的标记创建分裂式下拉菜单，添加 <code>ShowSplit='true'</code> 插入此符号为下拉选项作适当的间隔（距）处理。", "IsAsyncTitle": "异步按钮", "IsAsyncIntro": "通过设置 <code>IsAsync</code> 属性按钮是否为 <b>异步请求按钮</b>，默认为 <code>false</code>，<b>注意</b> 需要设置 <code>ShowSplit=\"true\"</code> 时才生效", "SizeTitle": "尺寸大小定义", "SizeIntro": "下拉菜单有各种大小规格可以选用 <code>Size</code> 属性，包括预设及分裂式按钮下拉菜单。", "DirectionTitle": "展开方向", "DirectionIntro": "增加 <code>Direction='Direction.Dropup'</code> 样式，使下拉菜单向上展开。", "AlignmentTitle": "菜单对齐", "AlignmentIntro": "默认情况下，通过设置 <code>MenuAlignment=Alignment.Right</code> 使下拉菜单右侧对齐", "ItemsTitle": "绑定数据源", "ItemsIntro": "点击右侧按钮时，下拉框内菜单项会增加", "RadioTitle": "绑定数据源", "RadioIntro": "改变选项时，下拉框内菜单项会增加", "CascadeTitle": "级联绑定", "CascadeIntro": "通过选择第一个下拉框不同选项，第二个下拉框动态填充内容。", "IsFixedButtonTextTitle": "固定按钮文本", "IsFixedButtonTextIntro": "通过设置 <code>IsFixedButtonText</code> 使更改下拉选项时按钮文本不变", "FixedButtonTextTitle": "设置固定按钮文字", "FixedButtonTextIntro": "通过设置 <code>FixedButtonText</code> 来设置固定按钮的初始文字", "ButtonTemplateTitle": "按钮模板", "ButtonTemplateIntro": "通过设置 <code>ButtonTemlate</code> 可以自定义显示内容", "AttributeValue": "当前选中的值", "AttributeClass": "样式", "AttributeColor": "颜色", "AttributeDirection": "下拉框弹出方向", "AttributeItems": "下拉框值", "AttributeMenuAlignment": "菜单对齐方式", "AttributeMenuItem": "菜单项渲染标签", "AttributeResponsive": "菜单对齐", "AttributeShowSplit": "分裂式按钮下拉菜单", "AttributeIsAsync": "是否为异步按钮", "AttributeSize": "尺寸", "AttributeTagName": "标签", "AttributeButtonTemplate": "按钮模板", "AttributeItemTemplate": "菜单项模板", "AttributeItemsTemplate": "下拉菜单模板", "EventDesc1": "点击按钮时触发此事件", "EventDesc2": "点击按钮时触发此事件并且不刷新当前组件，用于提高性能时使用", "EventOnSelectedItemChanged": "下拉框值发生改变时触发", "FixedButtonText": "固定按钮显示文字", "Item1": "北京", "Item2": "上海", "Item3": "广州", "AddItem": "添加", "RemoveItem": "删除", "City": "城市", "DropdownCascadeItem1": "请选择 ...", "DropdownCascadeItem2": "北京", "DropdownCascadeItem3": "上海", "DropdownCascadeItem4": "杭州", "DropdownCascadeItem5": "朝阳区", "DropdownCascadeItem6": "海淀区", "DropdownCascadeItem7": "静安区", "DropdownCascadeItem8": "黄浦区", "DropdownItemTemplateTitle": "下拉项模板", "DropdownItemTemplateIntro": "通过设置 <code>ItemTemplate</code> 可以自定义下拉项显示内容，本例中通过自定义模板使用 <code>Tooltip</code> 组件增加鼠标悬浮是显示提示功能"}, "BootstrapBlazor.Server.Components.Samples.GoTops": {"Title": "GoTop 返回顶端组件", "Description": "点击后返回指定容器的顶端", "NormalTitle": "基础用法", "NormalIntro": "点击后返回顶端", "Desc1": "页脚组件控制的滚动条组件 ID", "Description1": "底部显示 Footer 组件，点击返回顶端时页面自动滚动回顶端", "Description2": "本示例传递的是组件客户端 ID"}, "BootstrapBlazor.Server.Components.Samples.Menus": {"MenusTitle": "Menu 导航菜单", "MenusDescription": "为页面和功能提供导航的菜单列表。", "MenusTips1": "<code>Menu</code> 组件一般用法为菜单导航，即点击菜单后地址栏进行重定向导航，但是在实战中有时候不需要导航，通过设置 <code>OnClick</code> 回调委托，自定义处理逻辑，此时通过设置属性 <code>DisableNavigation</code> 即可，本例中由于都是模拟菜单点击并未真正的进行地址栏跳转导航所以所有 <code>Menu</code> 均设置为 <code>true</code> 禁止导航", "MenusTopBarTitle": "顶栏", "MenusTopBarIntro": "适用广泛的基础用法。", "MenusBottomBarTitle": "底栏", "MenusBottomBarIntro": "设置参数 <code>IsBottom</code> 值为 <b>true</b> 菜单栏在容器底部，适用移动端", "MenusBottomBarTips": "由于 <b>底栏菜单</b> 是相对与父容器定位的，需设置父级节点样式 <code>position-relative</code>，或者自定义样式 <code>position-fixed</code>", "MenusIconTopTitle": "带图标的顶栏菜单", "MenusIconTopIntro": "适用简单的网站应用，通过设置菜单项 <code>MenuItem</code> 的 <code>Icon</code> 属性设置菜单图标", "MenusLeftRightLayoutTitle": "侧栏", "MenusLeftRightLayoutIntro": "适用于左右结构布局的网站，通过设置 <code>IsVertical</code> 更改导航菜单为侧栏", "MenusIconLeftTitle": "带图标的侧栏菜单", "MenusIconLeftIntro": "通过设置菜单项 <code>MenuItem</code> 的 <code>Icon</code> 属性设置菜单图标", "MenusAccordionTitle": "手风琴效果的侧栏", "MenusAccordionIntro": "通过设置 <code>IsAccordion</code> 属性设置手风琴特效侧栏菜单", "MenusClickShrinkTitle": "带收缩的侧栏效果", "MenusClickShrinkIntro": "通过设置 <code>IsCollapsed</code> 属性设置侧栏菜单为收起状态", "MenusClickShrinkAlertText": "请注意 <code>IsCollapsed</code> 属性仅当 <code>IsVertical</code> 为真时才生效即仅侧边栏菜单时可用", "MenusClickShrinkDescription": "本例中使用布局组件 <code>Layout</code> 来进行网页构建", "Block7aTitle": "点击展开收缩左侧菜单", "MenusClickShrinkSpanSpan": "我是网站标题", "MenusClickShrinkMenuTitle": "后台管理", "MenusWidgetTitle": "带挂件的菜单", "MenusWidgetIntro": "通过设置 <code>MenuItem</code> 的 <code>Component</code> 属性设置自定义组件到菜单中", "MenusCustomNodeTitle": "自定义节点收缩", "MenusCustomNodeIntro": "通过设置 <code>MenuItem</code> 的 <code>IsCollapsed</code> 属性设置节点是否收起", "MenusCustomNodeDescription": "本例中 <b>权限设置</b> 节点为展开状态，其余节点为收起状态", "MenusDynamicTitle": "动态更改菜单", "MenusDynamicIntro": "通过代码动态设置 <code>Items</code> 属性值更改菜单项", "MenusDynamicButton1Text": "更新菜单", "MenusDynamicButton2Text": "重置菜单", "MenusPartDisableTitle": "部分菜单禁用功能", "MenusPartDisableIntro": "通过设置 <code>MenuItem</code> 的 <code>IsDisabled</code> 属性设置节点是否禁用", "MenusPartDisableDescription1": "本例中 <b>导航二</b> 节点为禁用状态，菜单与其子菜单均不可点击", "MenusPartDisableDescription2": "侧栏的禁用示例", "MenusAttr_Items": "菜单组件数据集合", "MenusAttr_IsVertical": "是否为侧栏", "MenusAttr_IsBottom": "是否为底栏", "MenusAttr_IsAccordion": "是否手风琴效果", "MenusAttr_IsScrollIntoView": "是否自动滚动到可视区域", "MenusAttr_DisableNavigation": "是否禁止地址栏导航", "MenusAttr_OnClick": "菜单项被点击时回调此方法", "System": "系统设置", "Website": "网站设置", "Task": "任务设置", "Authorize": "权限设置", "User": "用户设置", "Menu": "菜单设置", "Role": "角色设置", "Log": "日志设置", "Access": "访问日志", "Login": "登录日志", "Operation": "操作日志", "Menu1": "菜单一", "Menu2": "菜单二", "Menu3": "菜单三", "SubMenu1": "子菜单 1", "SubMenu2": "子菜单 2", "SubMenu3": "子菜单 3", "SubMenu11": "孙菜单 11", "SubMenu12": "孙菜单 12", "SubMenu21": "孙菜单 21", "SubMenu22": "孙菜单 22", "SubMenu31": "曾孙菜单 31", "SubMenu32": "曾孙菜单 32", "SubMenu41": "曾曾孙菜单 41", "SubMenu42": "曾曾孙菜单 42"}, "BootstrapBlazor.Server.Components.Samples.Navigation": {"NavsTitle": "Nav 导航组件", "NavsDescription": "为网站提供导航功能的菜单。", "NavsNormalTitle": "基本导航样式", "NavsNormalIntro": "本例中通过设置 <code>Items</code> 属性，通过程序 api 对导航组件进行赋值，并且在导航组件的 <code>ChildContext</code> 中直接写入 <code>NavLink</code> 组件", "NavsAlignTitle": "对齐", "NavsAlignIntro": "通过设置 <code>Alignment</code> 属性来控制组件对齐方式", "NavsDivider": "分割线", "NavsVerticalTitle": "垂直", "NavsVerticalIntro": "本例中通过设置 <code>IsVertical</code> 属性控制导航是否为垂直分布", "NavsPillsTitle": "胶囊式", "NavsPillsIntro": "通过设置 <code>IsPills</code> 属性控制导航菜单背景色", "NavsFillAndAlignTitle": "填充和对齐", "NavsFillAndAlignIntro": "通过设置 <code>IsFill</code> 属性控制导航菜单均匀分布填满整个导航栏", "NavsWideTitle": "等宽", "NavsWideIntro": "通过设置 <code>IsJustified</code> 属性使每个导航项目将具有相同的宽度。", "NavsChildContent": "内容", "NavsAlignment": "组件对齐方式", "NavsIsVertical": "垂直分布", "NavsIsPills": "胶囊", "NavsIsFill": "填充", "NavsIsJustified": "等宽"}, "BootstrapBlazor.Server.Components.Samples.Paginations": {"PaginationsTitle": "Pagination 分页", "PaginationsDescription": "当数据量过多时，使用分页分解数据。", "PaginationsNormalTitle": "基础用法", "PaginationsNormalIntro": "可以通过下拉框选取每页显示数据数量", "PaginationsDisplayTextTitle": "仅显示文本提示", "PaginationsDisplayTextIntro": "只有一页时不显示切换页码组件，仅显示文本提示", "PaginationsMaxPageLinkCountTitle": "仅显示分页组件", "PaginationsMaxPageLinkCountIntro": "通过 <code>ShowPaginationInfo='false'</code> 设置不显示文本提示", "PaginationsAlignmentTitle": "对齐方式", "PaginationsAlignmentIntro": "设置 <code>Alignment</code> 控制页码对齐方式", "PaginationsGotoTitle": "跳转导航", "PaginationsGotoIntro": "通过设置 <code>ShowGotoNavigator</code> 参数控制是否显示跳转导航，可以通过 <code>GotoTemplate</code> 自定义跳转组件", "PaginationsPageInfoTextTitle": "分页信息", "PaginationsPageInfoTextIntro": "通过设置 <code>ShowInfo</code> 参数控制是否显示分页信息", "PaginationsInfoTotal": "共 20 页", "PaginationsInfoTemplateTitle": "分页模板", "PaginationsInfoTemplateIntro": "通过 <code>PageInfoTemplate</code> 自定义分页信息", "PaginationsPageIndexAttr": "当前页码", "PaginationsPageCountAttr": "页码总数", "PaginationsMaxPageLinkCountAttr": "翻页页码数量", "PaginationsOnPageLinkClickAttr": "点击页码回调方法", "PaginationsAlignmentAttr": "对齐方式", "PaginationsShowPageInfoAttr": "是否显示分页信息", "PaginationsPageInfoTextAttr": "分页信息文字", "PaginationsPageInfoTemplateAttr": "分页信息模板", "PaginationsShowGotoNavigatorAttr": "是否显示跳转", "PaginationsGotoNavigatorLabelTextAttr": "跳转文字", "PaginationsGotoTemplateAttr": "跳转模板", "PaginationsPrevPageIconAttr": "上一页图标", "PaginationsPrevEllipsisPageIconAttr": "向前翻页图标", "PaginationsNextPageIconAttr": "下一页图标", "PaginationsNextEllipsisPageIconAttr": "向后翻页图标"}, "BootstrapBlazor.Server.Components.Samples.Steps": {"StepsTitle": "Step 步骤条", "StepsDescription": "引导用户按照流程完成任务的导航条", "StepsTipsTitle": "引导用户按照流程完成任务的分步导航条，可根据实际应用场景设定步骤，步骤不得少于 2 步", "StepsTips": "<code>Steps</code> 组件支持通过设置 <code>Items</code> 属性或者直接内嵌 <code>Step</code> 组件两种方式进行 UI 呈现", "StepsNormalTitle": "基础用法", "StepsNormalIntro": "简单的步骤条，通过直接绑定数据源 <code>Items</code> 即可", "StepsNormalPrevButtonText": "上一步", "StepsNormalNextButtonText": "下一步", "StepsNormalResetButtonText": "重 置", "StepsNormalFinishedTemplateDesc": "通过设置 <code>FinishedTemplate</code> 可以实现步骤全部完成后的后续处理逻辑，比如显示已完成，如需要延时一定时间再跳转到一个新页面，需要自定义组件，再其 <code>OnAfterRenderAsync</code> 生命周期中处理", "StepsStepTitle": "基础用法", "StepsStepIntro": "简单的步骤条，组件内部直接使用 <code>Step</code> 组件设置步骤", "Step1Text": "第一步", "Step2Text": "第二步", "Step3Text": "完成", "Step1Title": "步骤一", "Step2Title": "步骤二", "Step3Title": "步骤三", "StepDesc": "这是一段描述性文字", "StepsDescTitle": "有描述的步骤条", "StepsDescIntro": "每个步骤有其对应的步骤状态描述", "StepsTitleTemplateTitle": "Title 模板", "StepsTitleTemplateIntro": "通过设置 <code>TitleTemplate</code> 自定义 <code>Title</code> 文字", "StepsHeaderTemplateTitle": "Header 模板", "StepsHeaderTemplateIntro": "通过设置 <code>HeaderTemplate</code> 自定义 <code>Header</code>", "StepsVerticalTitle": "竖式步骤条", "StepsVerticalIntro": "通过设置 <code>IsVertical=\"true\"</code> 使步骤条竖式", "AttributeTitle": "StepItem 属性", "StepsItems": "设置步骤数据集合", "StepsIsVertical": "显示方向", "StepsAttrStepIndex": "步骤顺序号", "StepsAttrText": "步骤显示文字", "StepsAttrTitle": "步骤显示标题", "StepsAttrIcon": "步骤显示图标", "StepsAttrFinishedIcon": "步骤完成显示图标", "StepsAttrDescription": "描述信息", "StepsAttrHeaderTemplate": "设置当前步骤的标题模板", "StepsAttrTitleTemplate": "设置当前步骤的描述模板", "StepsAttrChildContent": "设置当前步骤的内容模板"}, "BootstrapBlazor.Server.Components.Samples.Tabs": {"TabsTitle": "Tabs 标签页", "TabsSubTitle": "分隔内容上有关联但属于不同类别的数据集合。", "TabsDescription": "Tab 组件从设计上采用模板的设计形式，使用本组件时通过将 <code>TabItem</code> 子组件添加到 <code>TabItems</code> 模板中即可", "TabsTipsTitle": "<code>Tab</code> 组件一般有两种用法：", "TabsTips1": "作为数据分割", "TabsTips2": "页面导航", "TabsTips3": "本组件默认行为为数据分割，点击 <code>TabItem</code> 标题时并不会进行导航行为，如果需要进行地址栏导航时请设置 <code>ClickTabToNavigation</code> 属性为 <code>true</code>，此时点击 <code>TabItem</code> 标题时地址栏将会重定向导航，多用于后台管理系统与 <code>Menu</code> 组件进行联动使用，实战可参考 <a href='layout-page' target='_blank'>后台模板模拟器</a> 中的 <code>多标签</code> 模式，如果有 <code>Razor 组件</code> 在额外的程序集中时，请正确设置 <code>AdditionalAssemblies</code> 属性值，以便标签组件内路由正确解析，相关文档 <a href='https://learn.microsoft.com/zh-cn/aspnet/core/blazor/fundamentals/routing?wt.mc_id=DT-MVP-5004174' target='_blank'>[传送门]</a>", "TabsTips4": "本组件会根据宽度高度等进行自适应适配，适当的时候可以出现左右或者上下的滚动箭头", "TabsInfoTitle": "标签页导航时设置附加信息", "TabsItemMenuTitle": "配置 Tab 与 Menu 联动字典，该方法无需再单独设置 TabItemOption 即可自动获取标签页属性。", "TabsInfoItem2": "使用内置扩展方法 <code>Navigator.NavigateTo(\"provider\", \"url\", \"text\", \"icon\", \"closable\")</code> 导航时即可设置标签页属性", "TabsInfoItem4": "通过设置 <code>razor</code> 页面标签 <code>@attribute [TabItemOption(Text = \"LayoutPage\", Icon = \"fa-solid fa-font-awesome\")]</code>", "TabsQATitle": "常见问题", "TabsQA1": "通过级联参数获得当前 <code>TabSet</code>", "TabsQA2": "通过级联参数获得当前 <code>TabItem</code>", "TabsQA3": "网页中点击链接如何跳转到新标签页 <code>TabItem</code>", "TabsQA4": "代码中如何导航到新标签页 <code>TabItem</code>", "TabItem1Text": "用户管理", "TabItem1Content": "我是用户管理", "TabItem2Text": "菜单管理", "TabItem2Content": "我是菜单管理", "TabItem3Text": "角色管理", "TabItem3Content": "我是角色管理", "TabItem4Text": "部门管理", "TabItem4Content": "我是部门管理", "TabItem5Text": "系统日志", "TabItem5Content": "我是系统日志", "TabItem6Text": "登录日志", "TabItem6Content": "我是部门管理", "TabItem7Text": "定时任务管理", "TabItem7Content": "我是定时任务管理", "TabItem8Text": "计数组件", "TabItem9Text": "天气预报", "TabsNormalTitle": "基础用法", "TabsNormalIntro": "基础的、简洁的标签页。", "TabsCardTitle": "选项卡样式", "TabsCardIntro": "通过 <code>IsCard='true'</code> 设置选项卡样式的标签页。", "TabsBorderTitle": "卡片化", "TabsBorderIntro": "通过 <code>IsBorderCard='true'</code> 设置卡片化的标签页。", "TabsIconTitle": "图标", "TabsIconIntro": "通过设置 <code>TabItem</code> 组件的 <code>Icon</code> 属性对标签页设置图标", "TabsClosableTitle": "关闭", "TabsClosableIntro": "通过设置 <code>ShowClose</code> 属性对标签页显示关闭按钮", "TabsClosableTips": "<p><code>Tab</code> 组件开启 <code>ShowClose</code> 后，<code>TabItem</code> 属性 <code>Closable</code> 可对标签页单独设置是否可关闭，默认为 <code>true</code>；本例中 <b>用户管理</b> 标签页不提供关闭功能</p>", "TabsPlacementTitle": "位置", "TabsPlacementIntro": "通过设置 <code>Placement</code> 属性更改标签位置，更改为左右时会出现上下滚动小箭头进行 <code>TabItem</code> 切换", "DividerText": "Tab 分割线", "TabsAddTabItemTitle": "自定义增加标签页触发器", "TabsAddTabItemIntro": "通过调用组件 api 动态添加/删除 <code>TabItem</code>", "AddButtonText": "添加", "RemoveButtonText": "移除", "ActiveButtonText": "激活第一个", "TabsComponentTitle": "内置其他组件", "TabsComponentIntro": "<code>TabItem</code> 中内置其他组件", "TabsComponentDescription": "<code>Tab</code> 组件各个面板内的内容默认是保持状态的，本例中面板切换时保持原有数据", "TabsDynamicTabItemTitle": "程序动态添加 TabItem 面板", "TabsDynamicTabItemIntro": "通过此功能可以实现侧边栏中点击菜单链接，右侧数据区上部呈现多标签", "TabsDynamicTabItemDescription": "本例中右侧 <code>Tab</code> 面板内状态一直保持，关闭后重新打开时组件重新加载", "Block9Div": "网站 Header", "TabsAppTitle": "实战 Tab 组件", "TabsAppIntro": "通过设置 <code>ShowExtendButtons</code> 属性为 <code>true</code>，开启组件左右移动按钮与关闭下拉菜单，实战中非常实用", "TabsAppDescription": "通过 <b>添加</b> <b>删除</b> 按钮动态调整 <code>TabItem</code> 数量，使其超出容器数量查看，左右移动效果；<b>用户管理</b> 设置为不可关闭；功能按钮无法关闭这个标签页。组件默认是循环切换标签页的，可以通过 <code>IsLoopSwitchTabItem=\"false\"</code> 关闭这个功能", "TabsIsOnlyRenderActiveTitle": "仅渲染当前标签", "TabsIsOnlyRenderActiveIntro": "通过设置 <code>IsOnlyRenderActiveTab</code> 参数使组件仅渲染当前活动标签", "BlockSetTextTitle": "设置标签文本", "BlockSetTextIntro": "调用实例方法 <code>SetText</code> 更新标签属性", "BlockSetTextDesc": "点击 <code>设置</code> 按钮，通过调用 <code>TabItem</code> 实例方法的 <code>SetText</code> 方法设置本标签的显示文本", "BlockSetTextButtonText": "设置", "TabsLazyLoadTitle": "懒加载标签页功能", "TabsLazyLoadIntro": "通过设置 <code>IsLazyLoadTabItem=\"true\"</code> 开启标签页懒加载，标签组件首次加载时仅加载 <code>IsActive</code> 的标签页，其余不加载，点击后才加载", "TabsHeaderTemplateTitle": "Header 模板", "TabsHeaderTemplateIntro": "通过设置 <code>HeaderTemplate</code> 对 <code>Header</code> 进行自定义设置", "BlockHeaderTemplateHeaderText": "待办事宜", "BlockHeaderTemplateDesc": "在 <code>HeaderTemplate</code> 中使用 <code>Badge</code> 设置挂件", "TabsSetHeaderTemplateTitle": "代码设置 Header 标题", "TabsSetHeaderTemplateIntro": "通过设置 <code>TabItem</code> 参数 <code>Text</code> 进行自定义标题设置", "TabsSetTabItemHeaderTextTitle": "代码设置标题图标", "TabsSetTabItemHeaderTextIntro": "通过设置 <code>TabItem</code> 实例方法 <code>SetHeader</code> 进行自定义标题设置", "TabsDragTitle": "拖拽标签页", "TabsDragIntro": "通过设置 <code>AllowDrag</code> 开启标签页拖拽功能，放下标签页后回调 <code>OnDragItemEndAsync</code> 方法", "TabsAtt1IsBorderCard": "是否为带边框卡片样式", "TabAtt2IsCard": "是否为卡片样式", "TabAtt3IsOnlyRenderActiveTab": "是否仅渲染 Active 标签", "TabAtt4ShowClose": "是否显示关闭按钮", "TabAtt5ShowExtendButtons": "是否显示扩展按钮", "TabAttShowNavigatorButtons": "是否显示前后导航按钮", "TabAttShowActiveBar": "是否显示活动标签", "TabAttIsLazyLoadTabItem": "是否延时加载标签内容", "TabAttIsLoopSwitchTabItem": "是否循环切换标签", "TabAtt6ClickTabToNavigation": "点击标题时是否导航", "TabAtt7Placement": "设置标签位置", "TabAtt8Height": "设置标签高度", "TabAtt9Items": "TabItem 集合", "TabAtt10ChildContent": "ChildContent 模板", "TabAttHeaderTemplate": "Header 模板", "TabAtt11AdditionalAssemblies": "额外程序集合，用于初始化路由", "TabAtt12OnClickTab": "点击 TabItem 标题时回调委托方法", "TabAttAllowDrag": "是否允许拖拽标签页", "TabAttOnDragItemEndAsync": "拖拽标签页回调方法", "Att13": "设置标签页显示标题集合，未设置时内部尝试使用菜单项数据", "TabMethod1AddTab": "添加 TabItem 到 Tab 中，可指定新增Tab的位置", "TabMethod2RemoveTab": "移除 TabItem", "TabMethod3ActiveTab": "设置指定 TabItem 为激活状态", "TabMethod4ClickPrevTab": "切换到上一个标签方法", "TabMethod5ClickNextTab": "切换到下一个标签方法", "TabMethod6CloseCurrentTab": "关闭当前标签页方法", "TabMethod7CloseOtherTabs": "关闭其他标签页方法", "TabMethod8CloseAllTabs": "关闭所有标签页方法", "TabMethod9GetActiveTab": "获得当前标签页方法", "BackAddTabText": "我是新建的 Tab, 名称是 {0}", "BackText1": "计数器", "BackText2": "天气预报", "AttTitle": "Attributes 属性", "MethodTitle": "Method 方法", "TabDefaultUrl": "默认标签页关闭所有标签页时默认显现此页面", "AttributeOnCloseTabItemAsync": "关闭标签页回调方法", "AttributeOnClickTabItemAsync": "点击 TabItem 时回调方法", "AttributeNotAuthorized": "NotAuthorized 模板", "AttributeNotFound": "NotFound 模板", "AttributeExcludeUrls": "排除地址支持通配符", "AttributeButtonTemplate": "按钮模板", "TabsDisabledTitle": "禁用", "TabsDisabledIntro": "通过设置 <code>IsDisabled=\"true\"</code> 禁用当前 <code>TabItem</code> 禁止点击、拖动、关闭等操作", "TabsChromeStyleTitle": "Chrome 样式", "TabsChromeStyleIntro": "通过设置 <code>TabStyle=\"TabStyle.Chrome\"</code> 设置 Chrome 浏览器标签页样式，目前仅支持 <code>Placement=\"Placement.Top\"</code> 模式", "TabAtt2TabStyle": "设置标签页样式", "TabsCapsuleStyleTitle": "胶囊样式", "TabsCapsuleStyleIntro": "通过设置 <code>TabStyle=\"TabStyle.Capsule\"</code> 设置标签页为胶囊样式，目前仅支持 <code>Placement=\"Placement.Top\"</code>  <code>Placement=\"Placement.Bottom\"</code> 模式", "AttributeToolbarTemplate": "Toolbar 模板", "TabsToolbarTitle": "工具栏", "TabsToolbarIntro": "通过设置 <code>ShowToolbar</code> 显示标签页工具栏，默认显示 <b>刷新</b> <b>全屏</b> 按钮，可以通过 <code>ShowRefreshToolbarButton</code> <code>ShowFullscreenToolbarButton</code> 控制是否显示，点击 <b>刷新</b> 按钮后触发 <code>OnToolbarRefreshCallback</code> 回调方法，可以通过 <code>ToolbarTemplate</code> 自定义工具栏按钮", "AttributeShowToolbar": "是否显示工具栏", "AttributeShowRefreshToolbarButton": "是否显示工具栏刷新按钮", "AttributeShowFullscreenToolbarButton": "是否显示工具栏全屏按钮", "AttributeRefreshToolbarTooltipText": "工具栏刷新按钮提示框文字", "AttributeFullscreenToolbarTooltipText": "工具栏全屏按钮提示框文字", "AttributeRefreshToolbarButtonIcon": "工具栏刷新按钮图标", "AttributeFullscreenToolbarButtonIcon": "工具栏全屏按钮图标", "TabsToolbarDesc": "点击按钮计数器数值增加后，点击工具栏的 <b>刷新</b> 按钮计数器清零", "AttributeOnToolbarRefreshCallback": "点击工具栏刷新按钮回调方法", "ContextRefresh": "刷新", "ContextClose": "关闭", "ContextCloseOther": "关闭其他", "ContextCloseAll": "关闭全部", "TabsContextMenuTitle": "右键菜单", "TabsContextMenuIntro": "通过内置 <code>ContextMenuZone</code> 组件，点击标签页右键时弹窗自定义右键菜单", "TabsContextMenuDesc": "被禁用的标签页可以通过设置 <code>OnBeforeShowContextMenu</code> 回调方法根据返回值控制右键菜单是否显示，未设置时禁用标签页也显示右键菜单", "AttributeShowContextMenu": "是否显示右键菜单", "AttributeContextMenuRefreshIcon": "右键菜单刷新按钮图标", "AttributeContextMenuCloseIcon": "右键菜单关闭按钮图标", "AttributeContextMenuCloseOtherIcon": "右键菜单关闭其他按钮图标", "AttributeContextMenuCloseAllIcon": "右键菜单关闭全部按钮图标", "AttributeContextMenuFullScreenIcon": "右键菜单全屏按钮图标", "AttributeOnBeforeShowContextMenu": "右键菜单弹出前回调方法", "ShowTabInHeaderDesc": "<code>Layout</code> 组件中开启多标签功能后，可以通过设置 <code>ShowTabInHeader=\"true\"</code> 将 <code>Tab</code> 标签头渲染到 <code>Layout</code> Header 模板中，效果可以通过 <a href=\"template\" target=\"_blank\">项目模板</a> 新建工程查看"}, "BootstrapBlazor.Server.Components.Components.DemoTabItem": {"Info": "点击下方按钮，本 <code>TabItem</code> 标题更改为当前分钟与秒", "ButtonText": "更改标题"}, "BootstrapBlazor.Server.Components.Samples.AutoFills": {"Title": "AutoFill 自动填充组件", "Description": "通过智能感应提示选项，选中后自动填充表单", "NormalTitle": "基本用法", "NormalIntro": "填充表单", "NormalDesc": "录入 <code>Name</code> 姓名智能提示，选择提示项后自动填充下方表单 目前支持键盘 <kbd>Enter</kbd> <kbd>Esc</kbd> <kbd><i class=\"fa-solid fa-arrow-up\"></i></kbd> <kbd><i class=\"fa-solid fa-arrow-down\"></i></kbd>", "CustomFilterTitle": "自定义过滤条件", "CustomFilterIntro": "通过设置自定义过滤条件回调委托 <code>OnCustomFilter</code> 过滤数据", "CustomFilterDesc": "录入 <code>Name</code> 姓名智能提示，通过设置自定义过滤条件回调委托 <code>OnCustomFilter</code> 过滤数据，当前过滤条件为 <code>Name</code> 包含输入字符串 <code>Count</code> 并且 值大于 50", "ShowDropdownListOnFocusTitle": "关闭自动展开下拉框", "ShowDropdownListOnFocusIntro": "通过设置 <code>ShowDropdownListOnFocus=&quot;false&quot;</code>", "ShowDropdownListOnFocusDesc": "参数 <code>ShowDropdownListOnFocus</code> 默认值为 <code>true</code> 组件获得焦点后会自动展开候选项下拉框，设置为 <code>false</code> 后关闭这个特性", "Att1": "匹配数据时显示的数量", "Att2": "无匹配数据时显示提示信息", "Def2": "无匹配数据", "Att3": "匹配时是否忽略大小写", "Att4": "是否开启模糊查询", "Att5": "组件数据集合", "Att6": "防抖时间", "Att7": "自定义集合过滤规则", "Att8": "通过模型获得显示文本方法", "Att9": "选项改变回调方法", "Att10": "获得焦点时是否展开下拉候选菜单", "Att11": "候选项模板", "Att12": "是否跳过 Enter 按键处理", "Att13": "是否跳过 Esc 按键处理", "IsVirtualizeTitle": "虚拟滚动", "IsVirtualizeIntro": "通过设置 <code>IsVirtualize</code> 参数开启组件虚拟功能特性", "IsVirtualizeDescription": "组件虚拟滚动支持两种形式通过 <code>Items</code> 或者 <code>OnQueryAsync</code> 回调方法提供数据", "AttrIsVirtualize": "是否开启虚拟滚动"}, "BootstrapBlazor.Server.Components.Samples.AutoCompletes": {"Title": "AutoComplete 自动完成", "Description": "输入框自动完成功能", "Block1Title": "基础用法", "Block1Intro": "通过设置 <code>Items</code> 数据集合当用户键入信息时自动显示提示信息", "Block2Title": "模糊查询并忽略大小写", "Block2Intro": "通过设置 <code>IsLikeMatch</code> 值设置是否开启集合的模糊匹配，通过设置 <code>IgnoreCase</code> 来控制是否忽略大小写", "Block3Title": "自定义提示消息", "Block3Intro": "通过设置 <code>NoDataTip</code> 值设置自动完成数据未找到时显示的自定义提示消息", "Block4Title": "自定义候选项", "Block4Intro": "通过设置 <code>ValueChanged</code> 回调方法根据用户输入的数据进行重组数据集合再进行提示信息", "Block5Title": "显示标签", "Block5Intro": "组件双向绑定时会根据条件自动判断是否显示标签文字", "DebounceTitle": "设置防抖", "DebounceIntro": "通过设置 <code>Debounce</code> 时长，来开启 <code>js</code> 防抖", "BlockGroupTitle": "组合使用", "BlockGroupIntro": "内置 <code>BootstrapInputGroup</code> 中使用，与 <code>BootstrapInputGroupLabel</code> 组合使用", "ItemTemplateTitle": "候选项模板", "ItemTemplateIntro": "通过设置 <code>ItemTemplate</code> 对下拉框候选项进行自定义设置", "Att1": "是否显示前置标签", "Att2": "内容", "Att3": "内容", "Att4": "自动完成数据无匹配项时提示信息", "Att4DefaultValue": "无匹配数据", "Att5": "匹配数据时显示的数量", "Att6": "文本框值变化时回调委托方法", "Att7": "是否开启模糊匹配", "Att8": "匹配时是否忽略大小写", "Att9": "自定义集合过滤规则", "AttItemTemplate": "候选项模板", "Debounce": "js 防抖时间，毫秒", "SkipEnter": "是否跳过 Enter 按键处理", "SkipEsc": "是否跳过 Esc 按键处理", "OnSelectedItemChanged": "下拉菜单选择回调方法", "OnSelectedItemChangedTitle": "下拉菜单选选中回调", "OnSelectedItemChangedIntro": "点击下拉菜单或者 <kbd>Enter</kbd> 回车时触发此回调方法", "OnValueChanged": "Value 改变时回调方法", "NormalDescription": "本例中请键入 123 字符串显示查看效果，自动完成组件初始化时给了自动提示数据集并且数据集无变化", "LikeMatchDescription": "本例中请键入 123 字符串显示查看效果，自动完成组件初始化时给了自动提示数据集并且数据集无变化", "NoDataTipDescription": "本例中请键入 567 字符串由于自动完成信息中心无数据显示自定义提示信息 - <code>没有找到你想要的数据</code>", "NoDataTip": "没有找到你想要的数据", "ValueChangedDescription": "本例中请键入任意字符串显示查看效果，自动完成组件根据键入的字符串从新获取提示信息数据集动态变化", "ShowLabelDescription": "前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "Divider1Text": "双向绑定显示标签", "Divider2Text": "双向绑定不显示标签", "Divider3Text": "自定义 DisplayText", "AutoText": "自定义城市", "DebounceDescription": "本例中请键入任意字符串显示查看效果，自动完成组件在防抖时间内，只在最后一次录入后将结果发送到后端，这将大大提高性能", "BlockGroupPrevLabel": "前置标签", "BlockGroupSuffixLabel": "后置标签", "PopoverTitle": "悬浮弹窗", "PopoverIntro": "通过设置 <code>IsPopover</code> 参数，组件使用 <code>popover</code> 渲染 <code>UI</code> 防止由于父容器设置 <code>overflow: hidden;</code> 使弹窗无法显示问题"}, "BootstrapBlazor.Server.Components.Samples.FullScreens": {"FullScreensTitle": "FullScreen 全屏", "FullScreensDescription": "通过注入服务调用 <code>Show</code> 方法弹出窗口进行人机交互", "FullScreenNormalTitle": "基础用法", "FullScreenNormalIntro": "通过调用<code>FullScreenService</code> 服务实例的 <code>Show</code> 方法将整个网页进行全屏化", "FullScreenNormalButtonText1": "全屏", "FullScreenOptionDesc": "通过设置 <code>FullScreenOption</code> 对全屏化的窗口进行设置，可通过 <code>ElementReference</code> <code>Id</code> <code>Selector</code> 指定页面元素"}, "BootstrapBlazor.Server.Components.Samples.FullScreenButtons": {"FullScreenButtonTitle": "FullScreenButton 全屏按钮", "FullScreenButtonIntro": "通过 <code>FullScreenButton</code> 组件将整个网页或者指定元素进行全屏化", "FullScreenButtonNormalTitle": "基础用法", "FullScreenButtonNormalIntro": "可通过 <code>Icon</code> 设置按钮默认图标，通过 <code>FullScreenExitIcon</code> 属性设置退出全屏时图标", "FullScreenTitleLi1": "通过 <code>TargetId</code> 参数设置全屏元素 Id", "FullScreenTitleLi2": "通过 <code>Text</code> 属性设置当前页标题文字"}, "BootstrapBlazor.Server.Components.Samples.Buttons": {"Title": "Button 按钮", "Description": "常用的操作按钮。", "Block1Title": "基础用法", "Block1Intro": "基础的按钮用法。", "Block2Title": "不同风格", "Block2Intro": "通过设置 <code>ButtonStyle</code> 来显示不同的按钮风格", "Block3Title": "Outline 按钮", "Block3Intro": "通过设置 <code>IsOutline='true'</code> 设置按钮边框颜色样式。", "Block4Title": "不同尺寸", "Block4Intro": "Button 组件提供除了默认值以外的多种尺寸，通过设置 <code>Size</code> 属性可以在不同场景下选择合适的按钮尺寸。", "Block5Title": "禁用状态", "Block5Intro": "按钮不可用状态。通过设置 <code>IsDisabled</code> 属性", "Block6Title": "按钮组", "Block6Intro": "由多个按钮组成一个组合按钮", "Block7Title": "带图标的按钮", "Block7Intro": "通过设置 <code>Icon</code> 属性对按钮图标进行设置，图标为字体字符串如使用 <code>font-awesome</code> 图标时 <code>fa-solid fa-font-awesome</code>", "Block8Title": "二次封装按钮", "Block8Intro": "通过设置 <code>WinButton</code> 组件的 <code>Text</code> 属性对按钮显示文字进行设置，点击按钮是右侧显示被点击按钮的文字", "Block9Title": "异步请求按钮", "Block9Intro": "通过设置 <code>IsAsync</code> 属性按钮是否为 <b>异步请求按钮</b>，默认为 <code>false</code>", "ButtonAsyncDescription": "当按钮为异步请求按钮时，点击按钮后自身状态会改变为禁用状态，同时显示 <code>Loading</code> 小图标，异步请求结束后恢复正常，本例中点击 <b>异步请求</b> 按钮后，显示请求加载动画，5 秒后恢复正常", "EventDesc1": "点击按钮时触发此事件", "EventDesc2": "点击按钮时触发此事件并且不刷新当前组件，用于提高性能时使用", "Att1": "颜色", "Att2": "图标", "Att3": "异步加载时的动画图标", "Att4": "显示文字", "Att5": "尺寸", "Att6": "样式", "Att7": "填充按钮", "Att8": "是否禁用", "Att9": "是否有边框", "Att10": "是否为异步按钮", "Att11": "内容", "Att12": "按钮风格", "Att13": "按钮类型", "MethodDesc1": "设置按钮是否可用", "NoneButtonColor": "None 按钮", "PrimaryButton": "主要按钮", "SecondaryButton": "次要按钮", "SuccessButton": "成功按钮", "DangerButton": "危险按钮", "WarningButton": "警告按钮", "InformationButton": "信息按钮", "DarkButton": "黑暗按钮", "HighlightButton": "高亮按钮", "LinkButton": "链接按钮", "ExtraSmallButton": "超小按钮", "SmallButton": "小按钮", "NormalButton": "按钮", "MediumButton": "中等按钮", "LargeButton": "大按钮", "ExtraLargeButton": "超大按钮", "ButtonExtraExtraLargeText": "超超大按钮", "BlockButton": "Block 按钮", "Description1": "通过 <code>OnClick</code> 回调方法中设置自身 <code>IsDisabled</code> 属性，或者调用组件实例方法 <code>SetDisable</code> 均可实现按钮禁用效果", "Description2": "由于使用 <code>IsDisabled</code> 属性时，需要显式手动调用 <code>StateHasChanged</code> 方法，会导致按钮所在组件整体刷新，建议使用实例方法 <code>SetDisable</code> 仅对按钮进行刷新", "SubTitle": "性能比较：", "IsDisabledTip": "使用 <code>IsDisabled</code> 属性设置时本页面传输量为 <code>4.8K</code>", "SetDisableTip": "使用 <code>SetDisable</code> 方法设置时本页面传输量为 <code>280B</code>", "ButtonDisabled": "点击被禁用", "ButtonAvailable": "点击后使第一个按钮可用", "ButtonOne": "按钮一", "ButtonTwo": "按钮二", "ButtonStatus": "状态按钮", "ButtonProgress": "进度按钮", "ButtonAsync": "异步请求", "TooltipText": "按钮", "TooltipTitle": "按钮提示栏", "TooltipIntro": "通过设置 <code>TooltipText</code> <code>TooltipPlacement</code> <code>TooltipTrigger</code> 快捷设置按钮提示栏信息、位置、触发方式，更多功能请使用 <code>Tooltip</code> 组件实现，本例中第二个按钮是 <b>禁用</b> 状态，提示栏仍然可用", "TooltipDisabledText": "禁用按钮"}, "BootstrapBlazor.Server.Components.Samples.PulseButtons": {"Block1Title": "基础用法", "Block1Intro": "基础的按钮用法。", "PulseButtonHeader": "PulseButton 心跳按钮", "PulseButtonPara": "适用于突出显示功能吸引使用者关注，按钮默认为圆形", "PulseButtonPara2": "心跳按钮组件继承于按钮，所以在按钮原有功能基础上增加了一个心跳环，可参见网站右下角切换样式按钮"}, "BootstrapBlazor.Server.Components.Samples.Cascaders": {"Title": "Cascader 级联选择", "Block1Title": "Cascader 级联选择", "Block1Intro": "提供各种颜色的下拉选择框", "Block2Title": "Cascader 禁用级联选择", "Block2Intro": "级联选择不可用状态", "Block3Title": "Cascader 双向绑定", "Block3Intro": "通过 <code>Select</code> 组件绑定 <code>Value</code> 属性，改变级联选择选项时，文本框内的数值随之改变。", "Block3Desc": "通过设置 <code>Clearable=\"true\"</code> 参数，使组件获得焦点或者鼠标悬浮时显示一个 <b>清除</b> 小按钮", "Block4Title": "Cascader 客户端验证", "Block4Intro": "级联选择未选择时，点击提交按钮时拦截。", "Block5Title": "绑定泛型为 Guid 结构", "Block5Intro": "组件绑定值为 Guid 结构体示例", "Block6Title": "显示标签", "Block6Intro": "组件双向绑定时会根据条件自动判断是否显示标签文字", "BlockParentSelectableTitle": "不允许选中父节点", "BlockParentSelectableIntro": "组件只允许选中最后一级节点，点击父节点后不会触发事件", "BlockShowFullLevelsTitle": "显示全路径", "BlockShowFullLevelsIntro": "通过设置 <code>ShowFullLevels</code> 值控制是否显示全路径", "Description": "前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "Att1": "是否显示前置标签", "Att2": "前置标签显示文本", "Att3": "未选择时的占位显示文字", "Att3Default": "点击进行选择 ...", "Att4": "样式", "Att5": "颜色", "Att6": "是否禁用", "Att7": "数据集合", "Event1": "级联选择选项改变时触发此事件", "SubmitButtonText": "提交", "ValidateButtonText": "验证", "item1": "北京", "item1_child1": "Brunswick", "item1_child1_child": "so-and-so street", "item1_child2": "Fitzroy", "item1_child3": "Carlton", "item1_child4": "Thornbury", "item2": "上海", "item2_child1": "Millsons Point", "item2_child2": "Potts Point", "item2_child3": "North Sydney", "item3": "广州", "item3_child1": "Brisbane", "item3_child2": "Gold Cost", "Divider1": "双向绑定显示标签", "Divider2": "双向绑定不显示标签", "Divider3": "自定义 DisplayText", "CascaderText": "自定义城市"}, "BootstrapBlazor.Server.Components.Samples.CherryMarkdowns": {"Header": "CherryMarkdown", "Tip": "基于 CherryMarkdown 的富文本框组件", "MarkdownsNote": "如果编辑内容过多，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "NormalTitle": "基础用法", "NormalIntro": "使用双向绑定获取对应的 <code>html</code> 和 <code>markdown</code> 内容", "FileUploadTitle": "自行处理文件上传事件", "FileUploadIntro": "使用<code>OnFileUpload</code>事件处理文件上传事件，支持直接粘贴图片到浏览器", "CustomTitle": "自定义内容", "CustomIntro": "通过<code>ToolbarSettings</code>自定义工具栏，通过<code>EditorSettings</code>自定义编辑器样式", "ViewTitle": "浏览模式", "ViewIntro": "纯浏览模式，没有编辑器", "ApiTitle": "外部控制组件", "ApiIntro": "使用CherryMarkdown的Api在外部控制内容", "InsertCheckListButtonText": "插入一个CheckList", "InsertPictureButtonText": "插入一张图片"}, "BootstrapBlazor.Server.Components.Samples.Checkboxs": {"Title": "Checkbox 多选框", "Description": "一组备选项中进行多选", "NormalTitle": "基础用法", "NormalIntro": "单独使用可以表示两种状态之间的切换，列头或者表头使用时可以表示三种状态之间的切换。组件支持泛型数据绑定，通过 <code>TValue</code> 设置绑定数据类型，通过 <code>State</code> 设置组件状态", "DisabledTitle": "禁用复选框", "DisabledIntro": "复选框不可用状态，通过 <code>IsDisabled</code> 设置组件是否可用", "ShowLabelTitle": "颜色", "ShowLabelIntro": "通过设置 <code>Color</code> 属性改变组件背景色", "DisplayTextTitle": "大小", "DisplayTextIntro": "通过设置 <code>Size</code> 属性改变组件大小", "ShowAfterLabelTitle": "Label 文字", "ShowAfterLabelIntro": "复选框显示文字，通过 <code>DisplayText</code> 设置组件显示文本，点击显示文字时组件状态也会进行翻转", "ShowAfterLabelDescription": "设置 <code>DisplayText</code> 属性，或者通过双向绑定均可以显示文本信息", "OnStateChangedTitle": "双向绑定 boolean 数据", "OnStateChangedIntro": "绑定组件内变量，数据自动同步，绑定数据类型为 <code>boolean</code> 类型时自动翻转值", "BindStringTitle": "双向绑定 string 数据", "BindStringIntro": "绑定组件内变量，数据自动同步", "ValidateFormTitle": "表单中使用", "ValidateFormIntro": "在表单中使用 <code>Checkbox</code> 时，显示标签文字会放置到组件前面", "ValidateFormDescription": "前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "ItemTemplateTitle": "项目模板", "ItemTemplateIntro": "通过设置 <code>ItemTemplate</code> 自定义显示 UI", "OnBeforeStateChangedTitle": "选中前回调方法", "OnBeforeStateChangedIntro": "通过设置 <code>OnBeforeStateChanged</code> 回调方法，可取消选中逻辑", "OnBeforeStateChangedText": "弹窗确认", "OnBeforeStateChangedSwalTitle": "弹窗确认", "OnBeforeStateChangedSwalContent": "是否更改选中状态", "Att1": "是否显示前置标签", "Att2": "是否显示后置标签", "Att3": "前置标签显示文本", "Att4": "是否禁用", "Att5": "控件类型", "OnBeforeStateChanged": "选择框状态改变前回调此方法", "OnStateChanged": "选择框状态改变时回调此方法", "StateChanged": "State 状态改变回调方法", "StatusText1": "选中", "StatusText2": "未选", "StatusText3": "不确定", "Checkbox2Text": "双向绑定", "Checkbox3Text": "手写标签"}, "BootstrapBlazor.Server.Components.Samples.CheckboxLists": {"Title": "CheckboxList 多选框组", "CheckboxListsTip": "控件用于创建多选的复选框组", "NormalTitle": "基础用法", "NormalIntro": "通过数据绑定展现复选框组", "NormalTips1": "通过 <code>bind-Value</code> 设置双向绑定数据值", "NormalTips2": "通过 <code>Items</code> 设置候选数据源", "NormalTips3": "通过 <code>OnSelectedChanged</code> 回调方法获取改变项实例", "NormalTips4": "通过 <code>ShowLabelTooltip=\"true\"</code> 开启标签提示功能", "ValidateFormTitle": "客户端验证", "ValidateFormIntro": "内置于 <code>ValidateForm</code> 中使用", "ValidateFormTips1": "可以通过改变窗口大小，体验自适应布局", "ValidateFormTips2": "本例中绑定模型 <code>BindItem</code> 的 <code>Name</code> 字段，通过勾选项自动更改模型数据", "ValidateFormTips3": "由于内置于 <code>ValidateForm</code> 表单内，本例中增加了 <code>RequiredAttribute</code> 必选要求验证，当取消所有选项后提示验证结果", "ShowLabelTitle": "双向绑定集合", "ShowLabelIntro": "绑定值为集合", "ShowLabelTip": "<code>TValue</code> 设置为 <code>IEnumerable&lt;int&gt;</code> 泛型集合，绑定集合的 <code>ValueField</code> 指定字段必须与泛型类型一致", "ItemTemplateTitle": "项目模板", "ItemTemplateIntro": "通过设置 <code>ItemTemplate</code> 自定义显示 UI", "EnumTitle": "双向绑定枚举", "EnumIntro": "绑定值为枚举", "EnumTip": "当 <code>CheckboxList</code> 绑定一个枚举集合时，<code>Items</code> 不需要指定，<code>Items</code> 会被自动设置成枚举里面所有的值，如果需要绑定部分值时，请自行提供枚举集合 <code>Items</code>", "NoBorderTitle": "无边框", "NoBorderIntro": "通过设置 <code>ShowBorder=\"false\"</code> 不显示边框", "NoBorderTip": "当验证失败时显示红色边框", "VerticalTitle": "竖向排列", "VerticalIntro": "通过设置 <code>IsVertical=\"true\"</code> 使checkbox竖向排列", "DisabledTitle": "禁用", "DisabledIntro": "通过设置 <code>IsDisabled=\"true\"</code> 禁用", "IsButtonTitle": "按钮复选框", "IsButtonIntro": "设置 <code> IsButton=\"true\"</code>，将复选框样式改为按钮样式", "Att1": "数据源", "Att2": "是否禁用", "Att3": "组件值用于双向绑定", "Att4": "是否竖向排列", "Event1": "复选框状态改变时回调此方法", "Header": "共", "Counter": "项被选中 组件绑定值 value", "item1": "张三", "item2": "李四", "item3": "王五", "item4": "赵六", "Foo": "张三,李四", "Description": "TValue 设置为 <code>IEnumerable&lt;string&gt;</code> 泛型集合", "AttributeMaxSelectedCount": "选项最大数", "AttributeOnMaxSelectedCountExceed": "选项到达最大值后回调方法", "MaxSelectedCountTitle": "最大选择数量", "MaxSelectedCountIntro": "通过设置 <code>MaxSelectedCount</code> 属性控制最大可选数量，通过 <code>OnMaxSelectedCountExceed</code> 回调处理逻辑", "MaxSelectedCountDesc": "选中节点超过 2 个时，弹出 <code>Toast</code> 提示栏", "OnMaxSelectedCountExceedTitle": "可选最大数量提示", "OnMaxSelectedCountExceedContent": "最多只能选择 {0} 项", "CheckboxListGenericTitle": "泛型支持", "CheckboxListGenericIntro": "通过 <code>CheckboxListGeneric</code> 组件和 <code>SelectedItem&lt;TValue&gt;</code> 开启泛型支持"}, "BootstrapBlazor.Server.Components.Samples.ColorPickers": {"Title": "ColorPicker 颜色拾取器", "Description": "选择颜色使用", "NormalTitle": "基础用法", "NormalIntro": "通过设置 <code>Value</code> 属性设定颜色值", "ValueTitle": "设置初始值", "ValueIntro": "通过设置 <code>Value</code> 属性设定颜色值", "TemplateTitle": "显示模板", "TemplateIntro": "通过设置 <code>Template</code> 自定义显示模板", "ValueDescription": "设置 <code>Value='@Value'</code> 初始化默认值", "BindValueTitle": "双向绑定", "BindValueIntro": "通过设置 <code>Value</code> 属性设定颜色值", "DisabledTitle": "禁用", "DisabledIntro": "通过设置 <code>IsDisabled</code> 属性禁用此组件", "FormatterTitle": "格式化", "FormatterIntro": "通过设置 <code>Formatter</code> 回调方法设置显示值", "ValidateFormTitle": "验证表单中使用", "ValidateFormIntro": "内置于 <code>ValidateForm</code> 中使用", "IsSupportOpacityTitle": "支持透明度", "IsSupportOpacityIntro": "通过设置 <code>IsSupportOpacity=\"true\"</code> 开启透明度支持", "EventOnValueChanged": "颜色改变回调委托方法", "AttributeTemplate": "显示模板", "AttributeFormatter": "显示颜色值格式化回调方法", "AttributeIsSupportOpacity": "是否支持透明度", "AttributeSwatches": "预设候选颜色"}, "BootstrapBlazor.Server.Components.Samples.DateTimePickers": {"Title": "DatePicker 日期选择器", "Description": "用于选择或输入日期", "NormalTitle": "选择日", "NormalIntro": "以「日」为基本单位，基础的日期选择控件", "ShowIconTitle": "是否显示组件图标", "ShowIconIntro": "通过设置 <code>ShowIcon=\"false\"</code> 不显示组件图标节约空间", "ValidateFormTitle": "客户端验证", "ValidateFormIntro": "根据自定义验证规则进行数据有效性检查并自动提示", "IsEditableTitle": "手工录入", "IsEditableIntro": "通过设置 <code>IsEditable=\"true\"</code> 开启手工录入日期功能", "DateTimeOffsetTitle": "点击弹出日期框", "DateTimeOffsetIntro": "以「日」为基本单位，基础的日期选择控件，此示例绑定 <code>DateTimeOffset</code> 数据类型", "BindValueTitle": "数据双向绑定", "BindValueIntro": "日期组件时间改变时文本框内的数值也跟着改变，通过设置 <code>IsEditable=\"true\"</code> 开启手工录入功能", "ViewModeTitle": "带时间的选择器", "ViewModeIntro": "在同一个选择器里选择日期和时间，点击确认按钮后关闭弹窗", "ViewModeTip": "设置 <code>ViewMode</code> 属性值为 <code>DatePickerViewMode.DateTime</code>", "NullValueTitle": "允许空时间", "NullValueIntro": "多用于条件选择", "NullValueTip": "绑定值为可为空类型 <code>DateTime?</code> 时自动出现 <b>清空</b> 按钮", "ShowLabelTitle": "显示标签", "ShowLabelIntro": "作为表单组件时，显示组件前方标签", "ShowLabelTip": "设置 <code>DisplayText</code> 属性值为 <code>选择时间</code>, 前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "DisabledTitle": "禁用", "DisabledIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止输入", "ShowSidebarTitle": "带快捷键侧边栏", "ShowSidebarIntro": "设置 <code>ShowSidebar</code> 属性值为 <code>true</code> 时，组件显示快捷方式侧边栏", "MinValueTitle": "设置值范围", "MinValueIntro": "设置 <code>MinValue</code> 属性值与 <code>MaxValue</code> 限制可选值范围，本例中设置范围为 <b>45</b> 天", "BlockAutoCloseTitle": "自动关闭", "BlockAutoCloseIntro": "设置 <code>AutoClose</code> 属性值，设置选中日期后自动关闭弹出框", "BlockAutoCloseDesc": "<code>AutoClose</code> 默认值为 <code>true</code> 点选日期时自动关闭弹窗，设置值为 <code>false</code> 时，需要点击 <b>确认</b> 按钮时关闭弹窗", "BlockGroupTitle": "组合使用", "BlockGroupIntro": "内置 <code>BootstrapInputGroup</code> 中使用，与 <code>BootstrapInputGroupLabel</code> 组合使用", "Att1": "是否显示前置标签", "Att2": "是否显示快捷侧边栏", "Att3": "前置标签显示文本", "Att4": "日期格式字符串 默认为 yyyy-MM-dd", "Att6": "是否禁用 默认为 false", "Att8": "组件值与 ValueChanged 作为双向绑定的值", "Att9": "获得/设置 组件显示模式 默认为显示年月日模式", "AttrAutoClose": "选中日期后是否自动关闭弹窗", "AttrIsEditable": "是否允许手动录入日期", "Event1": "确认按钮回调委托", "Event2": "组件值改变时回调委托供双向绑定使用", "BlockGroupLabel": "前置标签", "BlockGroupSuffixLabel": "后置标签", "DisplayText": "选择时间", "SubmitText": "保存", "Value": "属性", "Value.Required": "{0}为必填项", "ValidateFormValue": "时间", "DateTimePickerTitle": "时间选择", "DateTimePickerIntro": "通过时钟表盘选择时分秒，获取当前的日期和时间。", "DayTemplateTitle": "自定义日期显示", "DayTemplateIntro": "通过设置 <code>DayTemplate</code> 自定义显示模板，禁用的日期模板为 <code>DayDisabledTemplate</code>", "AttrShowLunar": "是否显示农历", "AttrShowSolarTerm": "是否显示 24 节气", "AttrShowFestivals": "是否显示节日", "AttrShowHolidays": "是否显示法定假日", "Feature": "功能体验区", "FeatureShowLunar": "显示农历", "FeatureShowSolarTerm": "24 节气", "FeatureShowFestivals": "节日", "FeatureShowHolidays": "法定假日", "FeatureIntro": "<b>假日</b>功能依赖组件包 <code>BootstrapBlazor.Holiday</code> <a href=\"holiday\" target=\"_blank\">[传送门]</a>", "FeatureFestivalIntro": "<b>节日</b>功能由组件库内置服务 <code>ICalendarFestivals</code> 提供，内置默认实现提供了 12 个公历节日与 7 个农历节日，可以通过自定义节日服务进行扩展，详细功能介绍请参阅 <b>节日服务</b> 文档 <a href=\"festival\" target=\"_blank\">[传送门]</a>", "DisableOptions": "组合禁用条件", "DisableWeekend": "禁用周末", "DisableToday": "禁用今天", "DisableDayCallbackTitle": "自定义禁用日期", "DisableDayCallbackIntro": "通过设置 <code>OnGetDisabledDaysCallback</code> 自定义哪些日期需要被禁用", "DisableDayCallbackTip": "<p class\"mb-3\"><b>请注意</b></p><ul class=\"ul-demo\"><li>组件赋值为禁用日期时，组件默认仍然显示其值，如果需要将禁用日期显示为 <b>空字符串</b> 请设置 <code>DisplayDisabledDayAsEmpty=\"true\"</code></li><li>本例为稍微复杂逻辑判断禁用日期，禁用日期为组合条件，所以当条件更改时需要调用组件实例方法 <code>ClearDisabledDays</code> 清除内部缓存</li></ul><p class=\"mb-3\">本例中第一个组件由于设置了 <code>DisplayDisabledDayAsEmpty=\"true\"</code> 所以组件值为 <code>DateTime.Today</code> 显示值为 <b>空字符串</b></p><div>本例中第二个组件数据类型为 <b>不可为空类型</b> 由于参数 <code>AutoToday</code> 默认值为 <code>true</code> 所以即使初始化值为 <code>DateTime.MinValue</code> 其显示值为 <code>DateTime.Today</code> 即使当禁用今天时，仍然显示，如果需要显示 <b>空字符串</b> 请设置 <code>DisplayDisabledDayAsEmpty=\"true\"</code></div>", "DisableDayCallbackAllowNullDisplayText": "可为空类型", "DisableDayCallbackNotAllowNullDisplayText": "不可为空类型", "FeatureShowLunarIntro": "<code>ShowLunar</code> 是否显示农历", "FeatureShowSolarTermIntro": "<code>ShowSolarTerm</code> 是否显示 24 节气", "FeatureShowFestivalsIntro": "<code>ShowFestivals</code> 是否显示节日", "FeatureShowHolidaysIntro": "<code>ShowHolidays</code> 是否显示假日", "OnGetDisabledDaysCallbackEvent": "获得自定义禁用日期回调方法", "AttrEnableDisabledDaysCache": "是否启用获得自定义禁用日期缓存", "AttrDisplayDisabledDayAsEmpty": "显示禁用日期为空字符串", "AttrFirstDayOfWeek": "设置每周第一天"}, "BootstrapBlazor.Server.Components.Samples.TimePickers": {"Title": "TimePicker 时间选择器", "Description": "用于选择或输入时刻", "OnConfirmTitle": "选择任意时间点", "OnConfirmIntro": "可以选择任意时间", "HasSecondsTitle": "隐藏秒数", "HasSecondsIntro": "控制是否显示秒数控件", "TimeTitle": "数据双向绑定", "TimeIntro": "点击确认按钮时间选择框值与文本框值一致"}, "BootstrapBlazor.Server.Components.Samples.ClockPickers": {"Title": "ClockPicker 时间选择器", "Description": "通过拖动表针选择时间", "BindValueTitle": "数据双向绑定", "BindValueIntro": "通过设置 <code>IsAutoSwitch=\"false\"</code> 禁止小时、分钟、秒表盘自动切换功能", "AutoSwitchText": "是否自动切换表盘", "HasSecondsTitle": "不设置秒数", "HasSecondsIntro": "通过设置 <code>ShowSecond=\"false\"</code> 不显示秒针表盘", "ShowMinuteTitle": "不设置分钟", "ShowMinuteIntro": "通过设置 <code>ShowMinute=\"false\"</code> 不显示分针表盘", "ShowClockScaleTitle": "显示表盘刻度", "ShowClockScaleIntro": "通过设置 <code>ShowClockScale=\"true\"</code> 显示表盘刻度", "IsAutoSwitchAttr": "是否自动切换 小时、分钟、秒 自动切换", "ShowClockScaleAttr": "是否显示表盘刻度", "ShowMinuteAttr": "是否显示分钟", "ShowSecondAttr": "是否显示秒"}, "BootstrapBlazor.Server.Components.Samples.Editors": {"EditorsTitle": "Editor 富文本框", "EditorsDescription": "将输入的文字转化为 <code>html</code> 代码片段", "EditorsTips": "<code>Editor</code> 组件是对 <a href='https://summernote.org/' target='_blank'><code>Summernote 组件</code></a> 的二次封装，如需使用本地化功能请自行官网下载相对应语言包，自行引用即可，<code>zh-CN</code> <code>en-US</code> 已内置；组件所需 <code>css</code> <code>JavaScript</code> 均按需动态加载，使用者无需设置。由于 <code>summernote</code> 组件依赖 <b>jQuery</b>，所以需要自行引用 <code>jQuery</code> 脚本包。如果编辑内容过多，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "EditorNormalTitle": "基础用法", "EditorNormalIntro": "默认呈现为 <code>div</code> 点击后变为富文本编辑框", "EditorNormalDescription": "通过设置 <code>IsEditor</code> 属性值来控制组件默认是 <code>div</code> 还是 <code>editor</code>", "EditorNormalDiv": "我是一个普通的 <code>div</code> 点击无法编辑", "EditorPlaceholderTitle": "自定义提示信息", "EditorPlaceholderIntro": "通过设置 <code>Placeholder</code> 属性来设置空值时的提示消息", "EditorPlaceholderDescription": "默认提示是 <b>点击后进行编辑</b>", "EditorIsEditorTitle": "默认显示为富文本编辑框", "EditorIsEditorIntro": "通过设置 <code>IsEditor</code> 属性来设置组件直接显示为富文本编辑框", "EditorHeightTitle": "自定义高度", "EditorHeightIntro": "通过设置 <code>Height</code> 属性来设置组件高度", "EditorOnValueChangedTitle": "双向绑定", "EditorOnValueChangedIntro": "实战中通过双向绑定到 <code>Value</code> 后台自动获取到客户端富文本框编辑内容", "EditorOnValueChangedDescription": "通过 <code>bind-Value</code> 对 <code>EditorValue</code> 后台属性进行双向绑定，编辑框内进行编辑后点击 <b>完成</b> 按钮，下方文本框内即可显示编辑后结果", "EditorCustomerToolbarButtonsTitle": "自定义扩展编辑框按钮", "EditorCustomerToolbarButtonsIntro": "通过设置 <code>CustomerPluginItems</code> 属性对编辑框工具栏进行自定义扩展， 通过设置 <code>OnClickPluginItem</code> 回调委托做功能", "EditorCustomerToolbarButtonsDescription": "本例中通过扩展 <code>CustomerPluginItems</code> 属性在工具栏中增加了两个按钮，点击按钮弹出 <code>SweetAlert</code> 模态框，点击模态框确认按钮后文本框中插入一段内容", "EditorToolbarItemsTitle": "自定义工具栏的富文本编辑框", "EditorToolbarItemsIntro": "通过设置 <code>ToolbarItems</code> 属性自定义工具栏内容，目前支持的工具栏值请参见 <a href='https://summernote.org/' target='_blank'>Summernote</a> 官网", "EditorToolbarItemsDescription": "本例中通过设置 <code>ToolbarItems</code> 属性，更改默认可用的工具栏按钮", "EditorSubmitTitle": "提交按钮", "EditorSubmitIntro": "通过设置 <code>ShowSubmit</code> 控制是否显示工具栏最后的 <b>提交</b> 按钮", "EditorSubmitDescription": "<code>ShowSubmit</code> 默认值为 <code>true</code> 显示提交按钮，设计上点击提交按钮后，才触发 <code>OnValueChanged</code> 回调方法此设计大大提高性能节约服务器算力，设置值为 <code>false</code> 后将使用 <code>summernode</code> 库 <code>onChange</code> 触发，适用需要实时获取编辑内容场景", "Att1": "空值时的提示信息", "Att1DefaultValue": "点击后进行编辑", "Att2": "是否直接显示为富文本编辑框", "AttrShowSubmit": "是否显示提交按钮", "Att3": "组件高度", "Att4": "富文本框工具栏工具", "Att5": "自定义按钮", "DoMethodAsyncTitle": "实例方法", "DoMethodAsyncIntro": "使用实例方法从外部操作 <code>Editor</code>，具体的参数参照 <a href='https://summernote.org/deep-dive'>summernote api</a>", "DoMethodAsyncDescription": "本例中通过设置 <code>ToolbarItems</code> 属性，更改默认可用的工具栏按钮", "EditorEmptyPlaceholder": "自定义空值的提示信息", "EditorOnValueChangedLabel": "显示编辑内容：", "EditorOnValueChangedUpdateValue": "更改后的值", "EditorOnValueChangedInitValue": "初始值 Test", "ToolTip1": "这是 plugin1 的提示", "ToolTip2": "这是 plugin2 提示", "Swal1Title": "点击plugin1按钮后弹窗", "Swal1Content": "点击插件按钮后弹窗并确认后才进行下一步处理", "Ret1": "<div class='text-danger'>从plugin1返回的数据</div>", "Swal2Title": "点击 plugin2 按钮后弹窗", "Swal2Content": "点击插件按钮后弹窗并确认后才进行下一步处理", "Ret2": "从plugin2返回的数据", "DoMethodAsyncButton1": "插入一段 Html", "DoMethodAsyncButton2": "将段落修改为 H2", "DoMethodAsyncButton3": "添加一张图片", "DoMethodAsyncButton4": "获得组件内容", "DoMethodAsyncPasteHTML": "这里是外部按钮插入的内容"}, "BootstrapBlazor.Server.Components.Samples.EditorForms": {"Title": "EditorForm 表单组件", "Description": "通过绑定数据模型自动呈现编辑表单", "SubDescription": "<code>EditorForm</code> 组件是一个非常实用的组件，当进行数据编辑时，仅需要将 <code>Model</code> 属性赋值即可。", "EditorFormTips1": "绑定模型默认自动生成全部属性，可以通过设置 <code>AutoGenerateAllItem</code> 更改为不自动生成", "EditorFormTips2": "如不需要编辑列，设置 <code>Editable</code> 即可，默认值为 <code>true</code> 生成编辑组件", "EditorFormTips3": "复杂编辑列，设置 <code>EditTemplate</code> 模板，进行自定义组件进行编辑", "EditorFormTips4": "表单内按钮可以设置多个，设置 <code>Buttons</code> 模板即可", "NormalTitle": "基础用法", "NormalIntro": "通过绑定 <code>TModel</code> 数据模型，自动生成模型各个字段的可编辑表单", "NormalDescription": "直接绑定模型 <code>Model</code>，设置 <b>Education</b> <b>Complete</b> 字段不显示", "ValidateFormTitle": "开启数据验证", "ValidateFormIntro": "通过嵌套 <code>ValidateForm</code> 组件实现数据合规检查功能", "ValidateFormTips1": "组件内置到 <code>ValidateForm</code> 内开启数据合规检查功能，<b>爱好</b> 字段使用 <code>EditTemplate</code> 模板自定义组件呈现数据", "ValidateFormTips2": "通过设置 <code>Readonly</code> 属性，使 <code>生日</code> 字段为只读", "AutoGenerateTitle": "默认不自动生成", "AutoGenerateIntro": "通过设置属性 <code>AutoGenerateAllItem</code> 值为 <code>false</code>，禁止自动生成属性，通过设置 <code>FieldItems</code> 内部集合来控制显示属性", "SkipValidateTitle": "表单组件内的组件绑定与模型无关的字段", "SkipValidateIntro": "通过设置属性 <code>AutoGenerateAllItem</code> 值为 <code>false</code>，禁止自动生成属性，通过设置 <code>FieldItems</code> 内部集合来控制显示属性", "ItemsPerRowTitle": "设置每行显示控件数量", "ItemsPerRowIntro": "通过设置 <code>ItemsPerRow</code> 属性值来控制布局", "ItemsPerRowDescription": "本示例设置 <code>ItemsPerRow=3</code> 每行显示 3 个组件", "AlignmentTitle": "自定义渲染组件类型", "AlignmentIntro": "通过设置 <code>CompnentType</code> 属性值来控制渲染组件类型", "AlignmentTips1": "<code>Inline</code> 布局模式下通过设置 <code>LabelAlign=\"Alignment.Right\"</code> 使表单内标签右对齐", "AlignmentTips2": "<code>Inline</code> 布局模式下通过设置 <code>LabelWidth</code> 值调整标签宽度，默认未设置使用全局样式变量 <code>--bb-row-label-width</code> 值，默认值为 120px", "AlignmentTips3": "<code>Buttons</code> 模板内可嵌套 <code>div</code> 并设置样式 <code>text-end</code> 使按钮右侧对齐", "EditorFormAttributeTitle": "EditorItem 属性", "Att1": "当前绑定数据模型", "Att2": "绑定列模板", "Att3": "按钮模板", "Att4": "是否显示 Label", "Att5": "是否生成所有属性", "Att6": "每行显示组件数量", "Att7": "设置组件布局方式", "Att8": "Inline 布局模式下标签对齐方式", "Att9": "当前绑定数据值", "Att10": "绑定列数据类型", "Att11": "是否允许编辑", "Att12": "是否只读", "Att13": "编辑列前置标签名", "Att14": "列编辑模板", "LabelWidthAttr": "标签宽度", "IsDisplay": "是否显示为 Display 组件", "ShowLabelTooltip": "鼠标悬停标签时显示完整信息", "GroupBoxTitle": "表单示例", "SubButtonText": "提交", "TestName": "张三", "TestAddress": "测试地址", "AutoGenerateDescription": "本例中通过设置 <code>AutoGenerateAllItem</code> 值为 <code>false</code>，关闭自动生成功能，通过手动增加两个 <code>EditorItem</code> 编辑项来呈现表单编辑", "SkipValidateDescription": "在某些情况下表单中有些列的值可能是二级分类等等，需要知道一级分类的信息，这个时候一级分类需要额外的组件来呈现，如果 <code>Select</code>，而这个组件是与当前上下文绑定模型 <code>Model</code> 无关的，这种需求中通过设置 <code>SkipValidate</code> 值为 <code>true</code>，关闭此组件的模型验证功能即可"}, "BootstrapBlazor.Server.Components.Samples.FloatingLabels": {"FloatingLabelsTitle": "FloatingLabel 输入框", "FloatingLabelsSubtitle": "通过鼠标或键盘输入字符", "FloatingLabelNormalTitle": "基础用法", "FloatingLabelNormalIntro": "提供基本的文本录入组件", "FloatingLabelDisplayTextTitle": "单向绑定数据", "FloatingLabelDisplayTextIntro": "显示组件内变量值", "FloatingLabelBindValueTitle": "客户端验证", "FloatingLabelBindValueIntro": "根据自定义验证规则进行数据有效性检查并自动提示", "FloatingLabelBindValueDisplayText": "客户端验证", "FloatingLabelPasswordTitle": "密码框", "FloatingLabelPasswordIntro": "通过设置属性 <code>type</code> 值为 <code>password</code> 使输入文字后以 <code>*</code> 进行屏蔽的密码输入框", "FloatingLabelPasswordDescription": "为了支持更多的文本框属性本组件可以直接写入 <code>type='email'</code> <code>type='number'</code> <code>type='phone'</code> 等 <code>html5</code> 新标准支持的全部属性值，组件未设置 <code>type</code> 值时使用默认的 <code>type='text'</code>", "FloatingLabelPasswordDisplayText": "密码框", "FloatingLabelFormatStringTitle": "泛型绑定", "FloatingLabelFormatStringIntro": "<code>BootstrapInput</code> 组件双向绑定值是泛型的，本例中双向绑定一个 <code>int</code> 类型数值", "FloatingLabelFormatStringDisplayText": "泛型绑定", "FloatingLabelFormatStringDiv1": "绑定数值: {0}", "FloatingLabelDisabledTitle": "禁用", "FloatingLabelDisabledIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止输入", "FloatingLabelDisabledDisplayText": "禁用", "FloatingLabelFormatterTitle": "自定义格式", "FloatingLabelFormatterIntro": "设置 <code>FormatString</code> 属性值为 <code>yyyy-MM-dd</code> 时，组件显示的时间格式为年月日", "FloatingLabelFormatterDiv2": "设置", "FloatingLabelFormatterDescription": "<code>BootstrapInput</code> 组件绑定 <code>byte[]</code> 数组，格式化成 <code>base64</code> 编码字符串示例", "FloatingLabelFormatterDisplayText": "字节数组", "FloatingLabelsTestName": "张三", "FloatingLabelsChildContent": "验证控件", "FloatingLabelsShowLabel": "是否显示前置标签", "FloatingLabelsGroupBox": "是否显示为 GroupBox 样式", "FloatingLabelsDisplayText": "前置标签显示文本", "FloatingLabelsFormatString": "数值格式化字符串", "FloatingLabelsFormatter": "TableHeader 实例", "FloatingLabelsType": "控件类型", "FloatingLabelsIsDisabled": "是否禁用 默认为 fasle", "FloatingLabelGroupBoxTitle": "GroupBox 样式", "FloatingLabelGroupBoxIntro": "设置 <code>IsGroupBox</code> 属性，组件显示为 GroupBox 样式"}, "BootstrapBlazor.Server.Components.Samples.Inputs": {"InputsBaseUsage": "基础用法", "InputsTitle": "Input 输入框", "InputsDescription": "通过鼠标或键盘输入字符", "InputsNormalIntro": "提供基本的文本录入组件", "InputsNormalDescription": "可通过设置 <code>IsAutoFocus</code> 是否自动获取焦点，多个文本框设置自动获取焦点时最后执行的组件将会获得焦点", "InputsColorTitle": "颜色", "InputsColorIntro": "通过设置 <code>Color</code> 更改文本框边框颜色", "InputsKeyboardTitle": "键盘响应", "InputsKeyboardIntro": "使用 <code>OnEnterAsync</code> <code>OnEscAsync</code> 回调委托对 <kbd>Enter</kbd> <kbd>ESC</kbd> 按键进行回调响应", "InputsPlaceholderTitle": "单向绑定数据", "InputsPlaceholderIntro": "显示组件内变量值", "InputsLabelsTitle": "双向绑定数据", "InputsLabelsIntro": "绑定组件内变量，数据自动同步", "InputsLabelsDescription": "<code>BootstrapInput</code> 组件开启双向绑定时，会根据绑定的 <code>Model</code> 属性值去自动获取 <code>Display/DisplayName</code> 标签值并且显示为前置 <code>Label</code>，通过 <code>DisplayText</code> 属性可以自定义显示前置标签，或者通过 <code>ShowLabel</code> 属性关闭前置标签是否显示", "InputsValidateFormTitle": "客户端验证", "InputsValidateFormIntro": "根据自定义验证规则进行数据有效性检查并自动提示", "InputsValidateFormTips1": "使用双向绑定时会自动寻找资源文件中 <code>Key</code> 值为 <code>{FieldName}.PlaceHolder</code> 对应值作为 <code>placeholder</code> 显示，本例中 <code>placeholder</code> 值为资源文件中 <code>Name.PlaceHolder</code> 键对应值 <code>required/不可为空</code>", "InputsPasswordTitle": "密码框", "InputsPasswordIntro": "通过设置属性 <code>type</code> 值为 <code>password</code> 使输入文字后以 <code>*</code> 进行屏蔽的密码输入框", "InputsPasswordDescription": "为了支持更多的文本框属性本组件可以直接写入 <code>type='email'</code> <code>type=number'</code> <code>type='phone'</code> 等 <code>html5</code> 新标准支持的全部属性值，组件未设置 <code>type</code> 值时使用默认的 <code>type='text'</code>", "InputsGenericTitle": "泛型绑定", "InputsGenericIntro": "<code>BootstrapInput</code> 组件双向绑定值是泛型的，本例中双向绑定一个 int 类型数值", "InputsGenericBindValue": "绑定值", "InputsDisabledTitle": "禁用", "InputsDisabledIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止输入", "InputsFormatStringTitle": "自定义格式", "InputsFormatStringIntro": "设置 <code>FormatString</code> 属性值为 <code>yyyy-MM-dd</code> 时，组件显示的时间格式为年月日", "InputsPassword2Title": "密码框", "InputsPassword2Intro": "使用 <code>BootstrapPassword</code> 组件", "InputsTrimTitle": "修剪空白", "InputsTrimIntro": "使用 <code>IsTrim=\"true\"</code> 可在输入内容的时候自动修剪空白", "TrimDescription": "设置参数 <code>IsTrim</code> 值为 <code>true</code> 后，组件内的前后空格将会被裁减", "InputsOnInputTitle": "文本框的值更改时触发", "InputsOnInputIntro": "使用 <code>UseInputEvent=\"true\"</code> 文本框的值更改时触发,用于逐键响应场合", "OnInputDescription": "设置参数 <code>UseInputEvent</code> 值为 <code>true</code> 后，每次按下键盘都会触发 <code>ValueChange</code> 事件", "InputsAtt1": "验证控件", "InputsAtt2": "是否显示前置标签", "InputsAtt3": "前置标签显示文本", "InputsAtt4": "颜色", "InputsAtt5": "数值格式化字符串", "InputsAtt6": "TableHeader 实例", "InputsAtt7": "控件类型", "InputsAtt8": "用户按下 Enter 键回调委托", "InputsAtt9": "用户按下 Esc 键回调委托", "InputsAtt10": "是否禁用", "InputsAtt11": "是否自动获取焦点", "IsSelectAllTextOnFocus": "获得焦点后自动选择输入框内所有字符串", "IsSelectAllTextOnEnter": "Enter 键自动选择输入框内所有字符串", "SelectAllTextAsync": "选择输入框内所有字符串方法", "ValidateRules": "自定义验证集合", "NormalPlaceHolder": "请输入 ...", "IsSelectAllTextOnFocusLabel": "获得焦点时全选", "IsSelectAllTextOnEnterLabel": "回车时全选", "InputsKeyboardTips1": "请按键盘 <kbd>Enter</kbd> 或者 <kbd>Esc</kbd> 进行测试", "InputsKeyboardTips2": "执行 <code>@ref.SelectAllTextAsync()</code> 选择输入框内所有字符串", "PlaceHolder": "请输入 ...", "InputsKeyboardLog": "按键触发 当前文本框值", "InputsPlaceholderSpan": "单向绑定", "InputsLabelsDivider1": "自定义标签", "InputsLabelsTips1": "设置 <code>DisplayText</code> 值为 <b>自定义标签</b>", "InputLabelsText": "自定义标签", "InputsLabelsDivider2": "占位", "InputsLabelsTps2": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>true</code> 时均显示", "InputsLabelsDivider3": "不占位", "InputsLabelsTips3": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>false</code> 时均不显示", "TestName": "张三", "InputsFormatStringSetting": "设置", "InputsFormatStringTips": "<code>BootstrapInput</code> 组件绑定 <code>byte[]</code> 数组，格式化成 <code>base64</code> 编码字符串示例", "UseInputEvent": "是否在文本框输入值时触发", "IsTrim": "是否自动去除空格", "ClearableTitle": "清除", "ClearableIntro": "通过设置 <code>Clearable=\"true\"</code> 参数，使组件获得焦点或者鼠标悬浮时显示一个 <b>清除</b> 小按钮"}, "BootstrapBlazor.Server.Components.Samples.InputNumbers": {"InputNumbersTitle": "InputNumber 组件", "InputNumbersDescription": "仅允许输入标准的数字值，支持自定义范围及其他高级功能", "InputNumbersNormalTitle": "基础用法", "InputNumbersNormalIntro": "<code>Number</code> 数值类型显示文本框，移动端自动弹出数字键盘", "InputNumbersRangeTitle": "区间限制用法", "InputNumbersRangeIntro": "设置 <code>Max</code> <code>Min</code> 来控制数值区间范围 1-10", "InputNumbersNullableTitle": "可为空数据类型", "InputNumbersNullableIntro": "绑定可为空数据类型时，组件允许空字符串，输入非法数据如 <code>1+2+3e</code> 时组件 UI 值为空字符串", "InputNumbersShowButtonTitle": "控制按钮", "InputNumbersShowButtonIntro": "设置 <code>ShowButton</code> 参数来控制是否显示增加或减少的按钮", "InputNumbersShowButtonDescription": "本例设置了最大值 10 最小值 0", "InputNumbersStepTitle": "自定义步长", "InputNumbersStepIntro": "设置 <code>Step</code> 参数来控制增加或减少的步长", "InputNumbersColorTitle": "颜色", "InputNumbersColorIntro": "设置 <code>Color</code> 参数来自定义按钮颜色", "InputNumbersDateTypeTitle": "数据类型", "InputNumbersDateTypeIntro": "本组件采用泛型支持 <code>int short long float double decimal</code> 基础数据类型", "InputNumbersDisabledTitle": "禁用", "InputNumbersDisabledIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止输入", "InputNumbersValidateFormTitle": "显示标签", "InputNumbersValidateFormIntro": "组件双向绑定时会根据条件自动判断是否显示标签文字", "InputNumbersValidateFormDescription": "前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "InputNumbersAtt1": "当前值", "InputNumbersAtt2": "可允许最大值", "InputNumbersAtt3": "可允许最小值", "InputNumbersAtt4": "步长", "InputNumbersAtt5": "是否禁用 默认为 fasle", "InputNumbersAtt6": "是否显示前置标签", "InputNumbersAtt7": "前置标签显示文本", "InputNumbersStep1": "步长默认为 1", "InputNumbersStep10": "步长设置为 10", "InputNumbersStep0.1": "步长设置为 0.1", "InputNumbersColorDescription1": "显示按钮", "InputNumbersColorDescription2": "无按钮", "InputNumbersValidateFormDivider1": "双向绑定显示标签", "InputNumbersValidateFormDivider2": "双向绑定不显示标签", "InputNumbersValidateFormDivider3": "自定义  DisplayText", "InputNumbersValidateFormInputText": "自定义", "InputNumbersUseInputEventTitle": "OnInput 事件", "InputNumbersUseInputEventIntro": "组件使用 OnInput 事件进行数值更新"}, "BootstrapBlazor.Server.Components.Samples.InputGroups": {"InputGroupsTitle": "InputGroup 组件", "InputGroupsDescription": "Input 的前后可以增加与一些标签和按钮，拼成一组。其排版格式紧凑，非常适合单行多列的场景，比如搜索等。", "InputGroupsNormalTitle": "基础用法", "InputGroupsNormalIntro": "在 <code>Input</code> 前增加标签，或者在 <code>Input</code> 后增加 <code>Button</code>", "InputGroupsWidthTitle": "自定义宽度", "InputGroupsWidthIntro": "通过设置 <code>Width</code> 参数，自定义标签宽度", "InputGroupsMultipleTitle": "增加多个组件", "InputGroupsMultipleIntro": "<code>InputGroup</code> 里面可以增加很多组件", "InputGroupsSelectTitle": "下拉框组合", "InputGroupsSelectIntro": "往 <code>InputGroup</code> 里面增加 <code>Select</code>", "InputGroupsValidateFormTitle": "验证表单中", "InputGroupsValidateFormIntro": "内置到 <code>ValidateForm</code> 中使用", "InputGroupsNormalUserName": "用户名", "InputGroupsMultipleDistance": "距离", "InputGroupsCheckboxTitle": "复选框组合", "InputGroupsCheckboxIntro": "往 <code>InputGroup</code> 里面增加 <code>Checkbox</code> 或者 <code>CheckboxList</code>", "InputGroupsRadioTitle": "单选框组合", "InputGroupsRadioIntro": "往 <code>InputGroup</code> 里面增加 <code>RadioList</code>", "InputGroupsSlideButtonTitle": "SlideButton 组合", "InputGroupsSlideButtonIntro": "往 <code>InputGroup</code> 里面增加 <code>SlideButton</code>", "InputGroupsDateTimePickerTitle": "DateTimePicker/Range 组合", "InputGroupsDateTimePickerIntro": "往 <code>InputGroup</code> 里面增加 <code>DateTimePicker</code> 或者 <code>DateTimeRange</code>", "InputGroupsDropdownTitle": "Dropdown 组合", "InputGroupsDropdownIntro": "往 <code>InputGroup</code> 里面增加 <code>Dropdown</code>", "InputGroupsStatusText1": "复选项目", "InputGroupsSwitchTitle": "Switch 组合", "InputGroupsSwitchIntro": "往 <code>InputGroup</code> 里面增加 <code>Switch</code>"}, "BootstrapBlazor.Server.Components.Samples.Markdowns": {"MarkdownsTitle": "Markdown 编辑器", "MarkdownsDescription": "提供 Markdown 语法支持的文本编辑器", "MarkdownsNote": "如果编辑内容过多，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "MarkdownsCss": "CSS 文件", "MarkdownsCssText": "无需设置组件自动动态加载", "MarkdownsJs": "JS 文件", "MarkdownsJsText": "无需设置组件自动动态加载", "MarkdownsLocalizationTipsTitle": "本地化", "MarkdownsLocalizationTips": "组件内置中文，切换当前文化信息即可获得中文", "MarkdownsNormalTitle": "普通用法", "MarkdownsNormalIntro": "默认设置", "MarkdownsNormalDescription": "输入 <code>Markdown</code> 相关代码后，点击下方相关区域显示数据", "MarkdownsAsyncTitle": "异步加载数据", "MarkdownsAsyncIntro": "通过 <code>Webapi</code> 获得 <code>Markdown</code> 显示内容", "MarkdownsAsyncButtonText": "加载", "MarkdownsCommonPropertyTitle": "常用属性", "MarkdownsCommonPropertyIntro": "更改默认参数", "MarkdownsCommonPropertyDescription": "设置 <code>Markdown</code> 编辑器最小高度 <code>300px</code>，默认高度 <code>500px</code>，提示信息这是 <code>Markdown</code>，<code>Tab</code> 方式显示，默认显示所见即所得页面", "MarkdownsCommonPropertyPlaceHolder": "这是 Markdown", "MarkdownsIsViewerTitle": "浏览器模式", "MarkdownsIsViewerIntro": "单纯浏览模式，不可编辑", "MarkdownsIsDarkTitle": "暗黑模式", "MarkdownsIsDarkIntro": "启用暗黑模式", "MarkdownsEnableHighlightTitle": "启用代码高亮插件", "MarkdownsEnableHighlightIntro": "使用 <code>EnableHighlight=true</code> 启用插件，使用```后加代码格式的方式使用高亮，如```js 则使用 js 高亮语法", "MarkdownsBrowserTitle": "外部操作 Markdown", "MarkdownsBrowserIntro": "使用 Api 从外部操作 Editor，具体的Api参照 <a href='https://nhn.github.io/tui.editor/latest/ToastUIEditorCore'>tui.editor api</a>", "MarkdownsBrowserButtonText1": "插入一行文字", "MarkdownsBrowserButtonText2": "插入一张图片", "MarkdownsBrowserButtonText3": "光标移动到最后", "MarkdownsBrowserText": "椰子树", "MarkdownsValidateTitle": "客户端验证", "MarkdownsValidateIntro": "根据自定义验证规则进行数据有效性检查并自动提示", "MarkdownsValidateSubmitText": "提交", "MarkdownString": "测试", "MarkdownsIsViewerDescription": "设置 <code>Markdown</code> 编辑器为纯浏览模式，<code>IsViewer='true'</code>", "Att1": "控件高度", "Att2": "控件最小高度", "Att3": "初始化时显示的界面", "Att4": "预览模式", "Att5": "UI 语言", "Att6": "提示信息", "Att7": "是否为纯浏览模式", "Att8": "是否为暗黑模式", "Att9": "是否启用代码高亮"}, "BootstrapBlazor.Server.Components.Samples.MultiSelects": {"MultiSelectsTitle": "MultiSelect 多项选择器", "MultiSelectsDescription": "当进行多项选项时，使用下拉菜单展示并提供搜索多项选择内容", "MultiSelectColorTitle": "颜色", "MultiSelectColorIntro": "提供各种颜色的多选下拉框", "MultiSelectIsSingleLineTitle": "单行显示", "MultiSelectIsSingleLineIntro": "通过设置 <code>IsSingleLine=\"true\"</code> 使组件始终渲染成一行", "MultiSelectIsSingleLineDescription": "候选项过多时，组件横向布局，鼠标悬浮组件上时显示横向滚动条进行数据滚动", "MultiSelectBindingStringTitle": "双向绑定值字符串", "MultiSelectBindingStringIntro": "绑定一个逗号字符串分割的字符串", "MultiSelectBindingStringDescription": "<div><code>MultiSelect</code> 组件数据源 <code>Items</code> 与 <b>选中值</b> <code>SelectedItemsValue</code> 均支持双向绑定；本例中通过双向绑定 <code>SelectedItemsValue</code> 变量，通过下拉框选择更改其值</div>", "MultiSelectBindingCollectionTitle": "双向绑定值集合", "MultiSelectBindingCollectionIntro": "绑定一个泛型 <code>IEnumerable<T></code> 集合", "MultiSelectBindingCollectionDescription": "本例中通过双向绑定 <code>SelectedArrayValues</code> 集合变量，通过下拉框选择更改其值", "MultiSelectBindingNumberTitle": "双向绑定值数组", "MultiSelectBindingNumberIntro": "绑定一个数组 <code>int[]</code>", "MultiSelectBindingNumberDescription": "本例中通过双向绑定 <code>SelectedIntArrayValues</code> 数组变量，通过下拉框选择更改其值", "MultiSelectBindingEnumCollectionTitle": "双向绑定枚举集合", "MultiSelectBindingEnumCollectionIntro": "绑定一个泛型 <code>IEnumerable<Enum></code> 集合", "MultiSelectBindingEnumCollectionDescription": "本例中通过双向绑定 <code>SelectedEnumValues</code> 集合变量，通过下拉框选择更改其值，<b>枚举</b> 类型时无需设置 <code>Items</code> 参数，额外功能是组件内部会尝试查找资源文件或者 <code>DisplayAttribute</code> 与 <code>DescriptionAttribute</code> 标签尝试进行本地化翻译工作，如本例切换到 <b>中文</b> 时枚举值为 <b>小学</b> 与 <b>中学</b>", "MultiSelectSearchTitle": "搜索功能", "MultiSelectSearchIntro": "通过设置 <code>ShowSearch</code> 值开启搜索功能", "MultiSelectSearchDescription": "本例中设置搜索回调委托方法 <code>OnSearchTextChanged</code> 进行自定义搜索结果，如果未设置时内部使用显示文本进行模糊匹配", "MultiSelectFlagsEnumTitle": "Flags 枚举", "MultiSelectFlagsEnumIntro": "绑定值为 <code>Enum</code> 数据类型时，如果枚举有 <code>Flags</code> 标签时，自动支持多选模式", "MultiSelectGroupTitle": "分组", "MultiSelectGroupIntro": "通过设置 <code>GroupName</code> 将下拉框中的备选项进行分组显示", "MultiSelectDisableTitle": "禁用功能", "MultiSelectDisableIntro": "通过设置 <code>IsDisabled</code> 值设置组件禁用状态", "MultiSelectDisableDescription": "禁用状态时组件无任何响应", "MultiSelectOptionChangeTitle": "选项改变时事件", "MultiSelectOptionChangeIntro": "通过设置 <code>OnSelectedItemsChanged</code> 回调方法获取当前选中数据集合改变事件", "MultiSelectClientValidationTitle": "客户端验证", "MultiSelectClientValidationIntro": "下拉框未选择时，点击提交按钮时拦截。", "MultiSelectClientValidationDescription": "内置到 <code>ValidateForm</code> 组件时，自动开启客户端验证功能，绑定模型拥有 <code>Required</code> 标签", "MultiSelectDisplayLabelTitle": "显示标签", "MultiSelectDisplayLabelIntro": "组件双向绑定时会根据条件自动判断是否显示标签文字", "MultiSelectDisplayLabelDescription": "前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "MultiSelectVeryLongTextTitle": "选项超长文字", "MultiSelectVeryLongTextIntro": "候选项文字特别长", "MultiSelectButtonTitle": "全选与反选按钮", "MultiSelectButtonIntro": "通过参数 <code>ShowToolbar</code> 控制是否显示工具栏，通过参数 <code>ShowDefaultButtons</code> 控制是否显示内置的三个按钮，通过参数 <code>ShowSearch</code> 控制是否显示搜索栏", "MultiSelectMaxMinTitle": "设置选项最大数与最小数", "MultiSelectMaxMinIntro": "通过设置 <code>Max</code> <code>Min</code> 值设置组件可选项数量限制", "MultiSelectExpandButtonTitle": "扩展工具栏按钮", "MultiSelectExpandButtonIntro": "通过设置 <code>ButtonTemplate</code> 自定义工具栏按钮实现自定义功能", "MultiSelectCascadingTitle": "级联绑定", "MultiSelectCascadingIntro": "通过选择第一个下拉框不同选项，第二个下拉框动态填充内容。", "MultiSelectCascadingDescription": "本例中点击第一个下拉框，可以通过异步方法获取第二个多选框的数据源，进行赋值后，调用 <code>StateHasChanged</code> 进行对 <b>多选框</b> 重新渲染", "MultiSelectItemTemplateTitle": "选项模板", "MultiSelectItemTemplateIntro": "通过设置 <code>ItemTemplate</code> 设置下拉框中选项模板，可以自定义样式", "MultiSelectDisplayTemplateTitle": "显示模板", "MultiSelectDisplayTemplateIntro": "通过设置 <code>DisplayTemplate</code> 模板自定义显示样式", "MultiSelectPopoverTitle": "悬浮弹窗", "MultiSelectPopoverIntro": "通过设置 <code>IsPopover</code> 参数，组件使用 <code>popover</code> 渲染 <code>UI</code> 防止由于父容器设置 <code>overflow: hidden;</code> 使弹窗无法显示问题", "MultiSelectsAttribute_ShowLabel": "是否显示前置标签", "MultiSelectsAttribute_ShowCloseButton": "是否显示前置标签关闭按钮", "MultiSelectsAttribute_ShowToolbar": "是否显示功能按钮", "MultiSelectsAttribute_ShowDefaultButtons": "是否显示默认功能按钮", "MultiSelectsAttribute_DisplayText": "前置标签显示文本", "MultiSelectsAttribute_PlaceHolder": "未选择时的占位显示文字", "MultiSelectsAttribute_PlaceHolder_DefaultValue": "点击进行多选 ...", "MultiSelectsAttribute_Class": "样式", "MultiSelectsAttribute_Color": "颜色", "MultiSelectsAttribute_IsDisabled": "是否禁用", "MultiSelectsAttribute_IsSingleLine": "是否单行显示", "MultiSelectsAttribute_Items": "数据集合", "MultiSelectsAttribute_ButtonTemplate": "扩展按钮模板", "MultiSelectsAttribute_ItemTemplate": "选项模板", "MultiSelectsAttribute_IsFixedHeight": "固定组件高度", "MultiSelectsEvent_OnSelectedItemsChanged": "下拉框选项改变时触发此事件", "MultiSelectsEvent_OnSearchTextChanged": "搜索文本发生变化时回调此方法", "MultiSelectAdd": "添加", "MultiSelectDecrease": "减少", "MultiSelectClean": "清空", "MultiSelectClientValidationSubmit": "提交", "MultiSelectDisplayLabelShowLabel": "双向绑定显示标签", "MultiSelectDisplayLabelHideLabel": "双向绑定不显示标签", "MultiSelectDisplayLabelCustomDisplayText": "自定义 DisplayText", "MultiSelectDisplayLabelCustomText": "自定义姓名", "MultiSelectExpandButtonText": "测试", "MultiSelectMaxMinMax": "最多可选择两个选项", "MultiSelectMaxMinMin": "最少选择两个选项", "MultiSelectSearchLog": "搜索文字", "MultiSelectVeryLongTextDisplayText": "超长文字", "MultiSelectOptionChangeLog": "选中项集合", "MultiSelectIsEditableTitle": "可编辑", "MultiSelectIsEditableIntro": "通过设置 <code>IsEditable</code> 参数，使组件可编辑", "MultiSelectIsEditableDescription": "通过设置 <code>EditSubmitKey</code> 参数可以指定通过 <kbd>Enter</kbd> 还是 <kbd>Space</kbd> 进行提交", "MultiSelectVirtualizeTitle": "虚拟滚动", "MultiSelectVirtualizeIntro": "通过设置 <code>IsVirtualize</code> 参数开启组件虚拟功能特性", "MultiSelectVirtualizeDescription": "组件虚拟滚动支持两种形式通过 <code>Items</code> 或者 <code>OnQueryAsync</code> 回调方法提供数据", "MultiSelectsAttribute_ShowSearch": "是否显示搜索框", "MultiSelectsAttribute_IsVirtualize": "是否开启虚拟滚动", "MultiSelectsAttribute_DefaultVirtualizeItemText": "开启虚拟滚动时首次加载 Value 对应的文本字符串用逗号分割", "MultiSelectGenericTitle": "泛型支持", "MultiSelectGenericIntro": "数据源 <code>Items</code> 使用 <code>SelectedItem&lt;TValue&gt;</code> 时即可支持泛型"}, "BootstrapBlazor.Server.Components.Samples.Radios": {"RadiosTitle": "Radio 单选框", "RadiosDescription": "在一组备选项中进行单选", "RadiosNormalTitle": "基础用法", "RadiosNormalIntro": "由于选项默认可见，不宜过多，若选项过多，建议使用 Select 选择器", "RadiosDisableTitle": "禁用单选框", "RadiosDisableIntro": "通过 <code>IsDisabled='true'</code> 单选框不可用状态", "RadiosLabelTitle": "Label 文字", "RadiosLabelIntro": "单选框显示文字", "RadiosLabelText": "按钮组", "RadiosBindingTitle": "双向绑定数据", "RadiosBindingIntro": "绑定组件内变量，数据自动同步，绑定数据类型为 SelectedItem 类型数组", "RadiosVerticalTitle": "竖向排列", "RadiosVerticalIntro": "通过设置 <code>IsVertical</code> 使组件内部竖向排列", "RadiosEnumTitle": "绑定枚举类型", "RadiosEnumIntro": "通过双向绑定 <code>Value</code> 无需设置 <code>Items</code>", "RadiosEnumDescription": "通过设置 <code>IsAutoAddNullItem</code> 自动添加 <b>空值</b> 选项，通过设置 <code>NullItemText</code> 自定义 <b>空值</b> 选项", "RadiosEnumText": "空值", "RadiosColorTitle": "颜色", "RadiosColorIntro": "通过设置 <code>Color</code> 属性改变组件背景色", "RadiosIsButtonTitle": "按钮样式", "RadiosIsButtonIntro": "通过设定 <code>IsButton</code> 值为 <code>True</code> 将候选项更改为按钮样式", "RadiosItemTemplateTitle": "项目模板", "RadiosItemTemplateIntro": "通过设置 <code>ItemTemplate</code> 自定义显示 UI", "RadiosDisplayText": "显示文字", "RadiosNullItemText": "空值显示文字", "RadiosIsDisabled": "是否禁用", "RadiosIsVertical": "是否垂直分布", "RadiosIsAutoAddNullItem": "绑定可为空枚举类型时是否自动添加空值", "RadiosItems": "绑定数据源", "RadiosIsButton": "是否渲染为按钮", "RadiosGroupName": "分组名称", "RadiosOnSelectedChangedEvent": "复选框状态改变时回调此方法", "RadiosLog1": "组件选中值:", "RadiosLog2": "显示值:", "RadiosLog3": "组件 Value 值:", "RadiosItem1": "选项一", "RadiosItem2": "选项二", "RadiosAdd1": "北京", "RadiosAdd2": "上海", "RadioListGenericTitle": "泛型支持", "RadioListGenericIntro": "通过使用 <code>RadioListGeneric</code> 组件配合 <code>SelectedItem&lt;TValue&gt;</code> 开启泛型支持", "RadiosAutoSelectFirstWhenValueIsNullTitle": "自动选择第一候选项", "RadiosAutoSelectFirstWhenValueIsNullIntro": "通过设置 <code>AutoSelectFirstWhenValueIsNull</code> 参数控制 <code>RadioList</code> 候选项选中情况，参数默认值为 <b>true</b>，即如果组件当前值与候选项中值无一致值时，自动选中第一个候选项，设置为 <b>false</b> 后，候选项将全部为未选中状态。", "RadiosAutoSelectFirstWhenValueIsNull": "值未 null 时是否默认选中第一个候选项"}, "BootstrapBlazor.Server.Components.Samples.Rates": {"RatesTitle": "Rate 评分", "RatesDescription": "评分组件", "RatesNormalTitle": "基础用法", "RatesNormalIntro": "<code>Rate</code> 组件通过 1 - 5 颗星表示数值等级，后台可以通过 <code>bind-Value</code> 对数值进行双向绑定通过 <code>Rate</code> 组件更改其值，鼠标滑动更改值，点击星星时确认其值", "RatesDisableTitle": "禁用", "RatesDisableIntro": "通过设置 <code>IsDisable</code> 属性值禁用组件", "RatesReadOnlyTitle": "只读", "RatesReadOnlyIntro": "通过设置 <code>IsReadonly</code> 属性值使组件只读，有颜色不响应点击事件", "RatesIconTitle": "模板", "RatesIconIntro": "通过设置 <code>ItemTemplate</code> 模板，配合自定义样式 <code>custom-rate</code>实现复杂功能", "RatesSwitchOn": "禁用", "RatesSwitchOff": "启用", "RatesValue": "组件值", "RatesIsDisabled": "是否禁用 默认为 fasle", "RatesIsReadonly": "是否只读 默认为 fasle", "RatesEvent1": "值改变时回调委托", "RatesMax": "最大值", "RatesItemTemplate": "Item模板", "RatesLog": "评星:", "RatesCry": "大哭", "RatesTear": "哭", "RatesSmile": "笑", "RatesSurprise": "惊讶", "RatesGrin": "惊喜", "RatesShowValueTitle": "显示评分", "RatesShowValueIntro": "通过设置 <code>ShowValue=\"true\"</code> 使组件显示当前值", "RatesIsWrapTitle": "自动折行", "RatesIsWrapIntro": "通过设置 <code>IsWrap=\"true\"</code> 使组件自动折行，默认不折行", "RatesIsWrap": "是否折行", "RatesShowValue": "是否显示值"}, "BootstrapBlazor.Server.Components.Samples.Selects": {"SelectsTitle": "Select 选择器", "SelectsDescription": "当选项过多时，使用下拉菜单展示并选择内容", "SelectsNormalTitle": "Select 下拉选择框", "SelectsNormalIntro": "提供各种颜色的下拉选择框", "SelectsNormalDescription": "本例中，第一个下拉框没有进行 <code>Value</code> 双向绑定，所以选择不同选项时仅自己变化，其余下拉框共用同一数据源 <code>Items</code> 并且双向绑定 <code>Value</code> 值，选择不同选项时一同变化", "SelectsDisableTitle": "Select 禁用下拉框", "SelectsDisableIntro": "选择器不可用状态", "SelectsDisableOption": "下拉框内选项 <b>禁用</b> 示例", "SelectsBindingTitle": "Select 双向绑定", "SelectsBindingIntro": "通过 <code>Select</code> 组件绑定 <code>Model.Name</code> 属性，改变下拉框选项时，文本框内的数值随之改变。", "SelectsClearableTitle": "可清空单选", "SelectsClearableIntro": "包含清空按钮，可将选择器清空为初始状态", "SelectsClearableDesc": "不可为空整形设置 <code>IsClearable</code> 无效，其默认值为 <b>0</b>", "SelectsBindingSelectedItemTitle": "Select 双向绑定 SelectItem", "SelectsBindingSelectedItemIntro": "通过 <code>Select</code> 组件绑定 <code>SelectItem</code> 属性，改变下拉框选项时，文本框内的数值随之改变。", "SelectsCascadingTitle": "Select 级联绑定", "SelectsCascadingIntro": "通过选择第一个下拉框不同选项，第二个下拉框动态填充内容。", "SelectsCascadingButtonText1": "弹窗中级联示例", "SelectsClientValidationTitle": "Select 客户端验证", "SelectsClientValidationIntro": "下拉框未选择时，点击提交按钮时拦截。", "SelectsOption1": "请选择 ...", "SelectsOption2": "北京", "SelectsOption3": "上海", "SelectsOption4": "广州", "SelectsClientValidationButtonText2": "提交", "SelectsGroupTitle": "分组", "SelectsGroupIntro": "备选项进行分组展示", "SelectsGuidTitle": "绑定泛型为 Guid 结构", "SelectsGuidIntro": "组件绑定值为 Guid 结构体示例", "SelectsDisplayLabelTitle": "显示标签", "SelectsDisplayLabelIntro": "组件双向绑定时会根据条件自动判断是否显示标签文字", "SelectsDisplayLabelDescription": "前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "SelectsDisplayLabelDivider1": "双向绑定显示标签", "SelectsDisplayLabelDivider2": "双向绑定不显示标签", "SelectsDisplayLabelDivider3": "自定义 DisplayText", "SelectsDisplayLabelSelectText": "自定义城市", "SelectsStaticTitle": "静态数据", "SelectsStaticIntro": "直接在 <code>Select</code> 组件内部进行硬编码书写，适用于静态数据下拉框", "SelectsEnumTitle": "枚举数据", "SelectsEnumIntro": "<code>Select</code> 组件绑定枚举类型示例", "SelectsEnumDescription1": "当绑定值为可为空枚举类型时，组件内部自动通过 <code>PlaceHolder</code> 值添加首选项，未设置 <code>PlaceHolder</code> 值时，使用资源文件中的 <b>请选择 ...</b> 作为首选项，本示例绑定 <code>EnumEducation</code> 枚举类型", "SelectsEnumDescription2": "绑定值为枚举类型时，设置 <code>PlaceHolder</code> 无效", "SelectsEnumSelectText1": "可为空", "SelectsEnumSelectText2": "不为空", "SelectsEnumSelectText3": "使用枚举整形值作为集合", "SelectsNullableTitle": "绑定可为空类型", "SelectsNullableIntro": "<code>Select</code> 组件绑定 <code>int?</code> 类型示例", "SelectsNullableDescription": "选中第一个选项时，绑定值 <code>SelectedIntItem</code> 为 <code>null</code>", "SelectsNullableBooleanTitle": "绑定可为空布尔类型", "SelectsNullableBooleanIntro": "<code>Select</code> 组件绑定 <code>bool?</code> 类型示例", "SelectsNullableBooleanDescription1": "可为空布尔类型多用于条件搜索框中", "SelectsNullableBooleanDescription2": "选中第一个选项时，绑定值 <code>SelectedIntItem</code> 为 <code>null</code>", "SelectsCustomTemplateTitle": "自定义选项模板", "SelectsCustomTemplateIntro": "通过设置 <code>ItemTemplate</code> 可以自定义选项渲染样式", "SelectsShowSearchTitle": "带搜索框的下拉框", "SelectsShowSearchIntro": "通过设置 <code>ShowSearch</code> 属性控制是否显示搜索框，默认为 <b>false</b> 不显示搜索框，可以通过设置 <code>IsAutoClearSearchTextWhenCollapsed</code> 参数控制下拉框收起后是否自动清空搜索框内文字，默认值为 <b>false</b> 不清空", "SelectsConfirmSelectTitle": "带确认的下拉框", "SelectsConfirmSelectIntro": "通过设置 <code>OnBeforeSelectedItemChange</code> 委托或者设置 <code>ShowSwal</code> 参数值为 <code>true</code>，阻止当前值的改变。", "SelectConfifrmSelectDesc1": "设置 <code>OnBeforeSelectedItemChange</code> 回调方法，在回调方法内自己弹窗确认是否更改值，返回 <code>true</code> 时更改，否则不更改", "SelectConfifrmSelectDesc2": "设置 <code>ShowSwal=\"true\"</code> 然后通过设置 <code>SwalTitle</code> <code>SwalContent</code> 参数值使用内置弹窗进行确认即可，在回调方法内自己弹窗确认是否更改值", "SelectsTimeZoneTitle": "时区下拉框", "SelectsTimeZoneIntro": "下拉框展现时区数据", "SwalTitle": "下拉框值变更", "SwalContent": "您确认要改变选项值吗？", "SwalFooter": "点击确认改变选项值，选择取消后值不变", "SelectsShowLabel": "是否显示前置标签", "SelectsShowSearch": "是否显示搜索框", "SelectsIsAutoClearSearchTextWhenCollapsed": "下拉框收起时是否自动清空搜索栏", "SelectsDisplayText": "前置标签显示文本", "SelectsClass": "样式", "SelectsColor": "颜色", "SelectsIsDisabled": "是否禁用", "SelectsItems": "数据集合", "SelectItems": "静态数据模板", "SelectsItemTemplate": "数据选项模板", "SelectsChildContent": "数据模板", "SelectsCategory": "对话框图标", "SelectsContent": "对话框内容", "SelectsContentDefaultValue": "确定改变当前值吗？", "SelectsDisableItemChangedWhenFirstRender": "禁止首次加载时触发 OnSelectedItemChanged 回调方法", "SelectsOnSelectedItemChanged": "下拉框选项改变时触发此事件", "SelectsOnBeforeSelectedItemChange": "下拉框选项改变前触发此事件", "SelectsPlaceHolder": "未选择", "SelectsDisplayTemplateTitle": "显示模板", "SelectsDisplayTemplateIntro": "自定义选项显示 <code>UI</code>", "SelectsPopoverTitle": "悬浮弹窗", "SelectsPopoverIntro": "通过设置 <code>IsPopover</code> 参数，组件使用 <code>popover</code> 渲染 <code>UI</code> 防止由于父容器设置 <code>overflow: hidden;</code> 使弹窗无法显示问题", "SelectsIsEditableTitle": "可编辑", "SelectsIsEditableIntro": "通过设置 <code>IsEditable=\"true\"</code> 使组件可录入", "SelectsIsEditableDesc": "开启可编辑功能后，输入值如果候选项中没有时，可以通过 <code>TextConvertToValueCallback</code> 回调方法返回新值，可以通过 <code>OnInputChangedCallback</code> 回调对 <code>Items</code> 数据源进行更新，防止页面刷新后输入值丢失", "SelectsVirtualizeTitle": "虚拟滚动", "SelectsVirtualizeIntro": "通过设置 <code>IsVirtualize</code> 参数开启组件虚拟功能特性", "SelectsVirtualizeDescription": "组件虚拟滚动支持两种形式通过 <code>Items</code> 或者 <code>OnQueryAsync</code> 回调方法提供数据", "SelectsGenericTitle": "泛型支持", "SelectsGenericIntro": "数据源 <code>Items</code> 使用 <code>SelectedItem&lt;TValue&gt;</code> 时即可支持泛型", "SelectsGenericDesc": "<p>请参考 <a href=\"https://github.com/dotnetcore/BootstrapBlazor/issues/4497?wt.mc_id=DT-MVP-5004174\" target=\"_blank\">设计思路</a> 理解此功能。本例中通过选择下拉框选项，得到的值为 <code>Foo</code> 实例，右侧文本框内显示值为 <code>Foo</code> 属性 <code>Address</code> 值</p><p>本例中设置 <code>IsEditable=\"true\"</code> 以及 <code>TextConvertToValueCallback</code> 参数，录入原数据源中不存在的 <code>Foo</code> 时，在 <code></code> 回调方法中添加新 <code>Foo</code> 实例到数据源中</p>", "SelectsOnInputChangedCallback": "编辑模式下输入文本转换为对应 Value 回调方法", "TextConvertToValueCallback": "编辑模式下输入文本变化时回调方法", "SelectsIsEditable": "是否可编辑", "SelectsIsVirtualize": "是否开启虚拟滚动", "SelectsDefaultVirtualizeItemText": "开启虚拟滚动时首次加载 Value 对应的文本字符串用逗号分割", "SelectsShowSwal": "是否显示 Swal 确认弹窗"}, "BootstrapBlazor.Server.Components.Samples.Sliders": {"SlidersTitle": "Slider 滑块", "SlidersDescription": "通过拖动滑块在一个固定区间内进行选择", "SlidersNormalTitle": "基础用法", "SlidersNormalIntro": "在拖动滑块时，改变当前值", "SlidersRangeTitle": "Range 标签", "SlidersIsDisabledTitle": "禁用", "SlidersIsDisabledIntro": "通过设置 <code>IsDisabled=\"true\" 禁用组件", "SlidersRangeIntro": "通过设置绑定值标签 <code>RangeAttribute</code> 自动生成 <code>min</code> <code>max</code>", "SlidersIsDisabled": "是否禁用", "SlidersValue": "组件当前值", "SlidersValueChanged": "ValueChanged 回调方法"}, "BootstrapBlazor.Server.Components.Samples.Switches": {"SwitchesTitle": "Switch 开关", "SwitchesDescription": "提供最普通的开关应用", "SwitchesNormalTitle": "基础用法", "SwitchesNormalIntro": "点击按钮切换状态", "SwitchesNormalDescription": "点击第一个开关有值输出日志", "SwitchesOnText": "开启", "SwitchesOffText": "关闭", "SwitchesDisableTitle": "禁用状态", "SwitchesDisableIntro": "通过设置 <code>IsDisabled</code> 属性控制组件不可用状态", "SwitchesColorTitle": "开关颜色", "SwitchesColorIntro": "通过设置 <code>OnColor</code> <code>OffColor</code> 属性值，设置开关状态颜色", "SwitchesBindingTitle": "双向绑定", "SwitchesBindingIntro": "绑定组件内变量，数据自动同步", "SwitchesBindingDescription1": "<code>Switch</code> 组件开启双向绑定时，会根据绑定的 <code>Model</code> 属性值去自动获取 <code>DisplayName</code> 标签值并且显示为前置 <code>Label</code>，通过 <code>DisplayText</code> 属性可以自定义显示前置标签，或者通过 <code>ShowLabel</code> 属性关闭前置标签是否显示", "SwitchesBindingDescription2": "前置标签显式规则与 <code>BootstrapInput</code> 组件一致 <a href='input'>[传送门]</a>", "SwitchesBindingDisplayText1": "双向绑定示例", "SwitchesBindingDivText1": "绑定数值", "SwitchesBindingDividerText1": "自定义标签", "SwitchesBindingTips1": "设置 <code>DisplayText</code> 值为 <b>自定义标签</b>", "SwitchesBindingDisplayText2": "自定义标签", "SwitchesBindingDividerText2": "占位", "SwitchesBindingTips2": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>true</code> 时均显示", "SwitchesBindingDividerText3": "不占位", "SwitchesBindingTips3": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>false</code> 时均不显示", "SwitchesBindingDisplayText3": "不显示", "SwitchesInnerTextTitle": "显示内置文字", "SwitchesInnerTextIntro": "通过设置 <code>ShowInnerText</code> 属性控制组件显示内置文字", "SwitchesInnerTextDescription": "通过设置 <code>OnInnerText</code> <code>OffInnerText</code> 属性更改内置文字，默认情况下建议使用一个汉字，可自定义组件宽度来增加内置文字数量", "SwitchesInnerTextLabelText1": "默认文字：", "SwitchesInnerTextLabelText2": "自定义文字：", "SwitchesInnerTextOnInnerText": "是", "SwitchesInnerTextOffInnerText": "否", "SwitchesNullableTitle": "可为空类型的开关", "SwitchesNullableIntro": "通过设置 <code>DefaultValueWhenNull</code> 属性控制 <code>Null</code> 值的默认值，未设置时为 <code>false</code>", "SwitchesAttributeClass": "样式", "SwitchesAttributeHeight": "控件高度", "SwitchesAttributeIsDisabled": "是否禁用", "SwitchesAttributeOffColor": "关颜色设置", "SwitchesAttributeOnColor": "开颜色设置", "SwitchesAttributeOnTextAttr": "组件 On 时显示文本", "SwitchesAttributeOffTextAttr": "组件 Off 时显示文本", "SwitchesAttributeOnInnerTextAttr": "组件 On 时内置显示文本", "SwitchesAttributeOffInnerTextAttr": "组件 Off 时内置显示文本", "SwitchesAttributeOnInnerTextDefaultValue": "开", "SwitchesAttributeOffInnerTextDefaultValue": "关", "SwitchesAttributeShowInnerText": "是否显示内置显示文本", "SwitchesAttributeWidth": "组件宽度", "SwitchesAttributeValue": "获取值", "SwitchesAttributeShowLabel": "是否显示前置标签", "SwitchesAttributeDisplayText": "前置标签显示文本", "SwitchesAttributeOnValueChanged": "值发生改变时回调委托方法", "SwitchesEventValueChanged": "获取选择改变的值"}, "BootstrapBlazor.Server.Components.Samples.TextAreas": {"TextAreaPlaceHolder": "请输入 ...", "TextAreaTitle": "Textarea 多行文本框", "TextAreaSubTitle": "用于录入大量文字", "TextAreaNormalTitle": "基础用法", "TextAreaNormalIntro": "可接受大量文字", "TextAreaLabel": "基础用法", "TextAreaDisableTitle": "禁用", "TextAreaDisableIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止输入", "TextAreaBindWayBindValue": "绑定值", "TextAreaReadOnlyTitle": "只读", "TextAreaReadOnlyIntro": "设置 <code>readonly</code> 属性时，组件禁止输入", "TextAreaHeightTitle": "高度", "TextAreaHeightIntro": "设置 <code>rows</code> 属性时，组件初始化显示固定行数高度", "TextAreaBindWayTitle": "双向绑定", "TextAreaBindWayIntro": "绑定组件内变量，数据自动同步", "TextAreaShowLabel": "是否显示前置标签", "TextAreaDisplayText": "前置标签显示文本", "TextAreaIsDisabled": "是否禁用 默认为 fasle", "TextAreaScrollTitle": "内容滚动", "TextAreaScrollIntro": "滚动到底部/顶部/指定位置", "TextAreaMockChat": "模拟聊天 ", "TextAreaMockChatRun": "开始模拟聊天", "TextAreaMockChatStop": "停止模拟聊天", "TextAreaScrollToBottom": "滚动到底部", "TextAreaScrollToTop": "滚动到顶部", "TextAreaScrollTo": "滚动+20", "TextAreaAutoScroll": "自动滚屏", "TextAreaUseShiftEnterTitle": "Shift Enter", "TextAreaUseShiftEnterIntro": "通过设置 <code>UseShiftEnter=\"true\"</code> 开始使用 <kbd>Shift</kbd> + <kbd>Enter</kbd> 进行换行操作，适用于对话框类应用", "TextAreaUseShiftEnterPlaceHolder": "请输入一些文字，Enter 发送 Shift + Enter 换行", "TextAreaUseShiftEnter": "是否使用 Shift + Enter 代替原回车按键行为", "TextAreaKeyEventTitle": "Enter/Esc 按键事件", "TextAreaKeyEventIntro": "通过设置 <code>OnEnterAsync</code> <code>OnEscAsync</code> 开始 <kbd>Enter</kbd> <kbd>Esc</kbd> 按键回调事件", "TextAreaKeyEventPlaceHolder": "按下 Enter/Esc 键触发事件"}, "BootstrapBlazor.Server.Components.Samples.Toggles": {"TogglesOnText": "开启", "TogglesOffText": "关闭", "TogglesTitle": "Toggle 开关", "TogglesSubTitle": "提供最普通的开关应用，值为 <code>true</code> <code>false</code>", "TogglesNormalTitle": "基础用法", "TogglesNormalIntro": "点击按钮切换状态", "BasicUsageP": "点击第一个开关有值输出日志", "TogglesDisableTitle": "禁用状态", "TogglesDisableIntro": "开关不可用状态。", "TogglesBindToWayTitle": "双向绑定数据", "TogglesBindToWayIntro": "绑定组件内变量，数据自动同步", "BindToWayP1": "<code>Switch</code> 组件开启双向绑定时，会根据绑定的 <code>Model</code> 属性值去自动获取 <code>DisplayName</code> 标签值并且显示为前置 <code>Label</code>，通过 <code>DisplayText</code> 属性可以自定义显示前置标签，或者通过 <code>ShowLabel</code> 属性关闭前置标签是否显示", "BindToWayDisplayText1": "双向绑定示例", "BindToWayDiv": "绑定数值:", "CustomLabelText": "自定义标签", "BindToWayP2": "设置 <code>DisplayText</code> 值为 <b>自定义标签</b>", "BindToWayP3": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>true</code> 时均显示", "OccupantsText": "占位", "NotOccupantsText": "不占位", "BindToWayP4": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>false</code> 时均不显示", "NotDisplayText": "不显示", "Color": "颜色", "IsDisabled": "是否禁用", "OffTextAttr": "组件 Off 时显示文本", "OffTextDefaultValue": "收缩", "OnTextAttr": "组件 On 时显示文本", "OnTextDefaultValue": "展开", "Width": "组件宽度", "Value": "获取值", "ShowLabel": "是否显示前置标签", "DisplayText": "前置标签显示文本", "OnValueChanged": "值发生改变时回调委托方法", "ValueChanged": "获取选择改变的值", "DisplayName": "绑定标签"}, "BootstrapBlazor.Server.Components.Samples.Transfers": {"LeftPanelText": "左侧列表", "LeftButtonText": "到左边", "RightPanelText": "右侧列表", "RightButtonText": "到右边", "PlaceHolder": "请输入", "TransfersTitle": "Transfer 穿梭框", "TransferNormalTitle": "基础用法", "TransferNormalIntro": "可搜索", "TransferCustomerTitle": "可自定义", "TransferCustomerIntro": "可以对列表标题文案、按钮文案、数据项的渲染函数、列表底部的勾选状态文案、列表底部的内容区等进行自定义。", "TransferSearchTitle": "可搜索", "TransferSearchIntro": "在数据很多的情况下，可以对数据进行搜索和过滤。", "TransferBindTitle": "双向绑定", "TransferBindIntro": "组件数据发生变化时，双向绑定 <code>bind-Value</code> 值同步变化", "TransferBindDescription": "<code>Transfer</code> 组件为泛型组件，支持三种绑定类型 <code>string</code> <code>IEnumerable&lt;string&gt;</code> <code>IEnumerable&lt;SelectedItem&gt;</code> 本例中双向绑定 <code>SelectedValue</code> 类型为 <code>IEnumerable&lt;SelectedItem&gt;</code>", "BindToWayButtonText1": "增加", "TransferValidateTitle": "客户端验证", "TransferValidateIntro": "组件数据发生变化时，双向绑定 <code>bind-Value</code> 值同步变化", "ClientValidationP1": "<code>Transfer</code> 组件内置于 <code>ValidateForm</code> 组件即开启客户端验证，本例中组件双向绑定 <code>Model.Hobby</code> 类型为 <code>IEnumerable&lt;string&gt;</code>", "ClientValidationButtonText": "提交", "TransferDisableTitle": "禁用", "TransferDisableIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止输入", "TransferItemClassTitle": "设置 Item 样式", "TransferItemClassIntro": "通过设置 <code>OnSetItemClass</code> 回调方法根据 <code>SelectedItem</code> 值设置选项样式", "Items": "组件绑定数据项集合", "TransferMinMaxTitle": "最大最小值约束", "TransferMinMaxIntro": "通过设置 <code>Min</code> <code>Max</code> 参数来限制选项个数", "LeftButtonTextAttr": "左侧按钮显示文本", "LeftPanelTextAttr": "左侧面板 Header 显示文本", "LeftPanelDefaultValue": "列表 1", "RightButtonTextAttr": "右侧按钮显示文本", "RightPanelTextAttr": "右侧面板 Header 显示文本", "RightPanelTextDefaultValue": "列表 2", "ShowSearch": "是否显示搜索框", "LeftPanelSearchPlaceHolderString": "左侧面板中的搜索框 placeholder 字符串", "RightPanelSearchPlaceHolderString": "右侧面板中的搜索框 placeholder 字符串", "IsDisabled": "是否禁用", "OnSelectedItemsChanged": "组件绑定数据项集合选项变化时回调方法", "OnSetItemClass": "设置 Item 样式回调方法", "Data": "数据", "Backup": "备选", "LeftHeaderTemplate": "左侧数据 Header 模板", "LeftItemTemplate": "左侧数据项模板", "RightHeaderTemplate": "右侧数据 Header 模板", "RightItemTemplate": "右侧数据项模板"}, "BootstrapBlazor.Server.Components.Samples.UploadAvatars": {"UploadsTitle": "AvatarUpload 头像上传组件", "UploadsSubTitle": "通过点击上传文件，通常用作上传预览一个或者一组类似头像的图片", "UploadsNote": "如果上传文件过大，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "AvatarUploadTitle": "基本用法", "AvatarUploadIntro": "通过设置 <code>IsMultiple</code> 控制是否允许多文件上传，通过设置 <code>IsCircle</code> 控制是否为圆角，通过设置 <code>BorderRadius</code> 控制圆角曲率。组件通过设置 <code>OnChange</code> 回调函数处理用户上传头像，如果未提供此回调时，将使用内置方法尝试读取上传文件生成 <code>base64</code> 格式预览数据", "AvatarUploadTips3": "设置 <code>IsSingle</code> 时，仅可以上传一张图片或者文件", "AvatarUploadTips5": "相关文档：<a href='https://www.runoob.com/tags/att-input-accept.html' target='_blank'>[Accept 属性详解]</a> <a href='https://www.iana.org/assignments/media-types/media-types.xhtml' target='_blank'>[Media Types 详细列表]</a>", "AvatarUploadTips6": "通过 <code>DefaultFileList</code> 属性设置预览地址 <code>PrevUrl</code> 即可", "AvatarUploadTips7": "验证表单内使用头像框示例", "AvatarUploadButtonText": "提交", "AvatarUploadValidateTitle": "ValidateForm", "AvatarUploadValidateIntro": "放置到 <code>ValidateForm</code> 内集成自动数据验证功能，详情可以参考 <code>ValidateForm</code> 组件，本例中上传文件扩展名仅限制为 <code>.png, .jpg, .jpeg</code>，上传其他格式时会有错误提示，文件大小限制为 <code>5M</code> 超过时也会有错误提示显示", "AvatarUploadAcceptTitle": "Accept", "AvatarUploadAcceptIntro": "组件提供了 <code>Accept</code> 属性用于设置上传文件过滤功能，本例中圆形头像框接受 GIF 和 JPEG 两种图像，设置 <code>Accept='image/gif, image/jpeg'</code>，如果不限制图像的格式，可以写为：<code>Accept='image/*'</code>，该属性并不安全还是应该是使用 <b>服务器端验证</b> 进行文件格式验证", "UploadsWidth": "预览框宽度", "UploadsHeight": "预览框高度", "UploadsIsCircle": "是否为圆形头像模式", "UploadsBorderRadius": "预览框圆角曲率", "UploadsValidateFormTitle": "表单应用", "UploadsValidateFormValidContent": "数据合规，保存成功", "UploadsValidateFormInValidContent": "数据不合规，请更正后再提交表单", "UploadsFormatError": "文件格式不正确", "UploadsAvatarMsg": "头像上传"}, "BootstrapBlazor.Server.Components.Samples.UploadInputs": {"UploadsTitle": "InputUpload 上传组件", "UploadsSubTitle": "通过点击浏览按钮弹出选择文件框选择一个或者多个文件进行上传", "UploadsNote": "如果上传文件过大，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "UploadNormalTitle": "基础用法", "UploadNormalIntro": "可以通过设置 <code>ShowDeleteButton=\"true\"</code> 显示 <b>删除</b> 按钮", "UploadNormalLabelPhoto": "选择一个或者多个文件", "UploadFormSettingsTitle": "表单应用", "UploadFormSettingsIntro": "放置到 <code>ValidateForm</code> 内集成自动数据验证功能，详情可以参考 <code>ValidateForm</code> 组件", "UploadFormSettingsLi1": "使用 <code>ValidateForm</code> 表单组件，通过设置模型属性的 <code>FileValidation</code> 标签设置自定义验证，支持文件 <b>扩展名</b> <b>大小</b> 验证，本例中设置扩展名为 <code>.png .jpg .jpeg</code>，文件大小限制为 <code>5M</code>", "UploadFormSettingsLi2": "选择文件后并未开始上传文件，点击 <code>提交</code> 按钮数据验证合法后，再 <code>OnSubmit</code> 回调委托中进行上传文件操作，注意 <b>Picture</b> 属性类型为 <code>IBrowserFile</code>", "UploadFormSettingsButtonText": "提交", "UploadsIsDirectory": "是否上传整个目录", "UploadsIsMultiple": "是否允许多文件上传", "UploadsShowProgress": "是否显示上传进度", "UploadsDefaultFileList": "已上传文件集合", "UploadsShowDeleteButton": "是否显示删除按钮", "UploadsIsDisabled": "是否禁用", "UploadsPlaceHolder": "占位字符串", "UploadsBrowserButtonClass": "上传按钮样式", "UploadsBrowserButtonIcon": "浏览按钮图标", "UploadsBrowserButtonText": "浏览按钮显示文字", "UploadsDeleteButtonClass": "删除按钮样式", "UploadsDeleteButtonIcon": "删除按钮图标", "UploadsDeleteButtonText": "删除按钮文字", "UploadsDeleteButtonTextDefaultValue": "删除", "UploadsAccept": "上传接收的文件格式", "UploadsOnDelete": "点击删除按钮时回调此方法", "UploadsOnChange": "点击浏览按钮时回调此方法"}, "BootstrapBlazor.Server.Components.Samples.UploadButtons": {"UploadsTitle": "ButtonUpload 按钮上传组件", "UploadsSubTitle": "通过点击按钮弹出选择文件框选择一个或者多个文件，通常用作上传文件附件", "UploadsNote": "如果上传文件过大，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "ButtonUploadTitle": "基础用法", "ButtonUploadIntro": "通过设置 <code>ShowUploadFileList=\"true\"</code> 可以显示上传文件列表，设置 <code>ShowDeleteButton=\"true\"</code> 显示 <b>删除</b> 按钮"}, "BootstrapBlazor.Server.Components.Samples.UploadCards": {"UploadsTitle": "CardUpload 卡片上传组件", "UploadsSubTitle": "通过点击按钮弹出选择文件框选择一个或者多个文件，呈现为卡片式带预览模式", "UploadsNote": "如果上传文件过大，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "ButtonUploadTitle": "基础用法", "ButtonUploadIntro": "使用 <code>DefaultFileList</code> 设置已上传的内容", "UploadPreCardStyleTitle": "预览卡片式", "UploadPreCardStyleIntro": "<code>CardUpload</code> 组件，呈现为卡片式带预览模式", "UploadFileIconTitle": "文件图标", "UploadFileIconIntro": "不同文件格式显示的图标不同", "UploadFileIconTemplateTitle": "自定义文件图标", "UploadFileIconTemplateIntro": "通过设置 <code>IconTemplate</code> 参数，使用 <code>FileIcon</code> 组件可以对文件图标进行进一步自定义 <a href=\"/file-icon\">[FileIcon 示例]</a>", "UploadBase64Title": "Base64 格式文件", "UploadBase64Intro": "通过设置 <code>UploadFile</code> 实例的 <code>PrevUrl</code> 参数值使用 <code>data:image/xxx;base64,XXXXX</code> 格式图片内容字符串作为预览文件路径", "UploadsFileTitle": "文件上传", "UploadsFileError": "文件大于 5M 请重新选择文件上传", "UploadsSuccess": "文件保存成功", "UploadsSaveFileError": "文件保存失败", "UploadsWasmError": "wasm 模式请调用 api 进行保存"}, "BootstrapBlazor.Server.Components.Samples.UploadDrops": {"UploadsTitle": "DropUpload 拖拽上传组件", "UploadsSubTitle": "通过点击组件或者拖拽或者粘贴上传一个或者多个文件", "UploadsNote": "如果上传文件过大，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "DropUploadTitle": "基础用法", "DropUploadIntro": "通过 <code>OnChange</code> 回调处理所有上传文件", "DropUploadFooterText": "文件大小不超过 5Mb", "UploadsBodyTemplate": "Body 模板", "UploadsIconTemplate": "图标模板", "UploadsTextTemplate": "文字模板", "UploadsUploadIcon": "图标", "UploadsUploadText": "上传文字", "UploadsShowFooter": "是否显示 Footer", "UploadsFooterTemplate": "Footer 字符串模板", "UploadsFooterText": "Footer 字符串信息"}, "BootstrapBlazor.Server.Components.Samples.ValidateForms": {"ChangeButtonText": "更改组件", "ResetButtonText": "重置组件", "ValidateFormsSubmitButtonText": "提交表单", "ValidateFormsTitle": "ValidateForm 表单组件", "ValidateFormsSubTitle": "可供数据合规检查的表单组件", "ValidateFormNormalTitle": "基础用法", "ValidateFormNormalIntro": "放置到 <code>ValidateForm</code> 中的组件提交时自动进行数据合规检查", "ValidateFormNormalBasicUsageDescription": "组件说明：", "ValidateFormNormalLi1": "<code>ValidateForm</code> 组件支持异步设置 <code>Model</code> 值", "ValidateFormNormalLi2": "表单事件为 <code>OnValidSubmit</code> <code>OnInvalidSubmit</code>", "ValidateFormNormalLi3": "<code>Model</code> 参数为必填项不允许为空", "ValidateFormNormalLi4": "表单内可以放置多个按钮，通过设置 <code>ButtonType='ButtonType.Submit'</code> 参数是否提交表单", "ValidateFormNormalLi5": "客户端验证机制支持模型的 <code>Required</code> 标签，通过设置 <code>ErrorMessage</code> 参数设置提示信息，未设置时使用默认的英文提示信息", "ValidateFormNormalLi6": "表单默认检查表单内绑定字段值是否合法，如需要检查模型所有字段时可设置 <code>ValidateAllProperties</code> 属性值为 <code>true</code>", "ValidateFormNormalLi7": "通过设置提交按钮 <code>Button</code> 属性 <code>IsAsync</code> 值，设置异步提交表单", "ValidateFormNormalLi8": "表单内组件通常用法都是使用双向绑定技术对 <code>Model</code> 的属性值进行双向绑定，当其值改变时会导致所在组件 <code>StateHasChanged</code> 方法被调用，即其所在组件或者页面进行刷新重新渲染", "ValidateFormNormalLi9": "表单内组件控件的值修改后 <code>OnFieldChanged</code> 方法被调用", "ValidateFormNormalFormLabelWidth": "组件前置标签默认宽度为 <code>120px</code> 六个汉字，如需要更多汉字请在项目样式文件中更改样式变量 <code>--bb-row-label-width</code> 即可，或者设置表单显示标签在组件上方", "ValidateFormNormalBasicUsageDescription2": "注意事项：", "ValidateFormInnerComponentTitle": "内置组件", "ValidateFormInnerComponentIntro": "放置支持表单组件到 <code>ValidateForm</code> 中", "ValidateFormInnerComponentInnerComponentLabel": "支持表单验证的组件示例", "ValidateFormInnerComponentInnerComponentDescription1": "示例说明", "ValidateFormInnerComponentInnerComponentLi1": "姓名为字符串类型", "ValidateFormInnerComponentInnerComponentLi2": "年龄为整数类型", "ValidateFormInnerComponentInnerComponentLi3": "生日为时间类型", "ValidateFormInnerComponentInnerComponentLi4": "教育为枚举类型", "ValidateFormInnerComponentInnerComponentLi5": "爱好为集合类型", "ValidateFormInnerComponentInnerComponentDescription2": "本例中通过设置 <b>提交按钮</b> 属性 <code>IsAsync</code> 来异步提交表单，点击提交后禁用自身，异步操作完毕后恢复", "ValidateFormCustomDisplayErrorTitle": "自定义显示错误信息", "ValidateFormCustomDisplayErrorIntro": "通过调用 <code>SetError</code> 方法设置自定义错误信息", "ValidateFormCustomDisplayErrorDescription": "<b>应用场景</b><p>客户端验证通过后进行数据库保存操作，如果出现其他问题，后仍然可以进行表单自定义错误提示，本例中数据验证合法后，点击 <code>提交表单</code> 按钮后，姓名字段会显示，<code>数据库中已存在</code> 这样的自定义提示信息</p>", "ValidateFormValidatorAllFieldTitle": "验证所有字段值", "ValidateFormValidatorAllFieldIntro": "此组件默认检查表单内模型绑定字段值，如需要检查模型所有字段值时设置 <code>ValidateAllProperties</code> 值", "ValidateFormValidatorAllFieldDescription": " 本例中未放置 <code>Address</code>，由于设置 <code>ValidateAllProperties</code> 参数值为 <code>true</code>，所以 <code>Address</code> 字段仍然被检查，即使表单内所有数据均合法后，提交数据时仍然触发 <code>OnInvalidSubmit</code> 回调委托", "ValidateFormComplexValidationTitle": "复杂类型支持", "ValidateFormComplexValidationIntro": "支持任意复杂类型的绑定与验证", "ValidateFormComplexValidationDescription": "本示例中第二个绑定的是一个超级复杂类型 <code>ComplexModel.Dummy.Dummy2.Name</code> 清空值后，点击 <b>提交表单</b> 会对数据进行验证。第二个文本框验证合规后，通过调用 <code>SetError</code> 再次显示错误提示", "ValidateFormComplexValidationPre": "ComplexForm.SetError('Dummy.Dummy2.Name','数据库中已存在');", "ValidateFormDynamicFormTitle": "动态调整表单内组件", "ValidateFormDynamicFormIntro": "通过代码更改表单内组件，验证表单仍然可以正确的进行验证", "ValidateFormDynamicFormDescription": "点击 <b>更改组件</b> 按钮后 <code>地址</code> 组件变更为 <code>数量</code> 组件，<b>重置组件</b> 按钮恢复，<code>姓名</code> <code>地址</code>组件为必填项，<code>数量</code> 组件有默认值 <code>0</code> 所以可以通过数据检查", "ValidateFormInnerFormLabelTitle": "表单内设置组件标签右对齐", "ValidateFormInnerFormLabelIntro": "通过样式统一设置全站或者特定表单内标签对齐方式", "ValidateFormInnerFormLabelDescription": "地址文本框增加了 <b>邮件地址</b> 验证规则", "ValidateFormCustomValidationFormTitle": "自定义验证表单", "ValidateFormCustomValidationFormIntro": "通过设置 <code>Rules</code> 添加自定义验证规则", "CustomValidationFormP1": "地址文本框增加了 <b>邮件地址</b> 验证规则，点击提交时验证", "CustomValidationFormComment1": "增加邮箱验证规则", "CustomValidationFormComment2": "Razor 文件中使用", "ValidateFormValidateTitle": "代码调用验证方法", "ValidateFormValidateIntro": "通过代码中调用 <code>ValidateForm</code> 实例方法 <code>Validate</code> 触发表单认证", "ValidateButtonText": "保存", "ValidateFormValidateDescription": "按钮类型为普通按钮不会触发表单 <code>submit</code> 行为，为了提高性能，示例中使用 <code>OnClickWithoutRender</code> 方法减少一次 <code>UI</code> 渲染", "Model": "表单组件绑定的数据模型，必填属性", "ValidateAllProperties": "是否检查所有字段", "ShowRequiredMark": "表单内必填项是否显示 * 标记", "ChildContent": "子组件模板实例", "OnValidSubmit": "表单提交时数据合规检查通过时的回调委托", "OnInvalidSubmit": "表单提交时数据合规检查未通过时的回调委托", "SetError": "设置验证失败方法", "Validate": "表单认证方法", "OnInvalidSubmitLog": "OnInvalidSubmit 回调委托: 验证未通过", "OnValidSubmitLog": "OnValidSubmit 回调委托: 验证通过", "OnValidSubmitStartingLog": "OnValidSubmit 回调委托: Starting ...", "OnValidSubmitDoneLog": "OnValidSubmit 回调委托: Done!", "OnInvalidSubmitCallBackLog": "OnInvalidSubmit 回调委托", "OnValidSubmitCallBackLog": "OnValidSubmit 回调委托", "DatabaseExistLog": "数据库中已存在", "LongDisplayText": "我是特别长的显示标签", "LongDisplayDescription": "本例中通过设置 <code>form-inline</code> 样式内的 <code>BootstrapInput</code> 组件 <code>ShowLabelTooltip</code> 为 <code>true</code> 使鼠标悬停在被裁剪的 <code>label</code> 时显示完整信息", "ShowLabelTooltip": "鼠标悬停标签时显示完整信息", "DisableAutoSubmitFormByEnter": "是否禁用表单自动提交功能", "ValidateFormDisableAutoSubmitFormByEnterTitle": "禁用自动提交功能", "ValidateFormDisableAutoSubmitFormByEnterIntro": "当焦点在表单内输入框时，按 <kbd>Enter</kbd> 默认是自动提交表单的，通过设置 <code>DisableAutoSubmitFormByEnter=\"false\"</code> 禁止自动提交", "DisableAutoSubmitFormByEnterDesc": "<code>form</code> 表单是 <code>web</code> 一个重要的元素，如果表单内有 <code>input</code> 元素当此元素获得焦点时，按下 <kbd>Enter</kbd> 会自动提交表单，这是表单元素的特性，并不是我们组件代码逻辑，如果不需要这个功能时通过设置 <code>DisableAutoSubmitFormByEnter=\"true\"</code> 禁用此功能，此例中文本框内按回车不会 <b>自动提交</b> 并且进行数据合规性检查，需要点击 <b>提交表单</b> 按钮提交表单", "ValidateFormIValidatableObjectTitle": "IValidatableObject 接口类型", "ValidateFormIValidatableObjectIntro": "<code>IValidatableObject</code> 接口提供了更加灵活的自定义校验，非常适合进行非常复杂的数据校验，例如多属性组合起来进行校验等场景。", "ValidateFormIValidatableObjectDescription": "本示例使用 <code>IValidatableObject</code> 实现校验 <code>联系电话1</code> 和 <code>联系电话2</code> 不能相同，以及 <code>名称</code> 不能为空，本例中并未设置 <code>名称</code> 为必填项，但是由于模型对 <code>IValidatableObject</code> 接口的实现中进行了校验，所以在提交数据时仍然触发 <code>OnInvalidSubmit</code> 回调委托", "ValidateFormIValidateCollectionTitle": "IValidateCollection 接口类型", "ValidateFormIValidateCollectionIntro": "<code>IValidateCollection</code> 接口提供了更加灵活的自定义 <code>联动</code> 校验，非常适合进行非常复杂的数据校验，例如多属性组合起来进行校验等场景。", "ValidateFormIValidateCollectionDescription": "本示例使用 <code>IValidateCollection</code> 实现校验 <code>联系电话1</code> 和 <code>联系电话2</code> 不能相同校验，更改任意单元格使电话号码相同时两个文本框均进行提示", "ValidateMetadataTypeTitle": "MetadataType IValidateCollection", "ValidateMetadataTypeIntro": "模型通过 <code>[MetadataType(typeof(MockModelMetadataType))]</code> 指定元数据类型 <code>MockModelMetadataType</code> 进行模型验证，如果指定模型继承 <code>IValidateCollection</code> 接口时，调用其 <code>Validate</code> 方法进行数据合规性检查", "LabelWidth": "标签宽度"}, "BootstrapBlazor.Server.Components.Samples.Ajaxs": {"AjaxTitle": "Ajax调用", "AjaxDescribe": "用于直接在浏览器使用 fetch 方法与服务器交互，目前只支持输入输出皆为 json，返回值为 json 字符串，可以自行转换处理。", "NormalTitle": "基础用法", "NormalIntro": "模拟登录", "NormalB": "特别注意：", "NormalDiv": "这里只是进行了登录模拟，并没有真正的调用 <code>HttpContext.SignInAsync</code>，真实使用时需要在登录完成后对页面进行刷新，否则无法真正的登录成功。", "GoToTitle": "页面跳转", "GoToIntro": "用 <code>Js</code> 实现页面跳转，解决了 <code>Blazor</code> 页面作为 <b>SPA</b> 跳转时不会真正刷新页面的问题", "InvokeAsync": "执行 fetch 方法", "GoTo": "执行 goto 方法", "NormalButtonText1": "登录成功", "NormalButtonText2": "登录失败", "GoToButtonText1": "跳转到文档首页", "GoToButtonText2": "跳转到自己（刷新页面）"}, "BootstrapBlazor.Server.Components.Samples.Avatars": {"Title": "Avatar 头像", "SubTitle": "用图标、图片或者字符的形式展示用户或事物信息。", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "通过 <code>IsCircle</code> 和 <code>Size</code> 设置头像的形状和大小。", "IconTitle": "展示类型", "IconIntro": "支持三种类型：图标、图片和字符", "BorderTitle": "边框", "BorderIntro": "通过设置 <code>IsBorder</code> 是否显示头像框边框，此模式下图片加载失败时边框为 <code>border-danger</code> 样式，加载成功时边框为 <code>border-success</code>;其余模式下边框为<code>border-info</code>", "CircletTitle": "异步加载", "CircleIntro": "适用于图片地址由 <code>webapi</code> 等接口异步获取的场景", "Size": "头像框大小", "IsBorder": "是否显示边框", "IsCircle": "是否为圆形", "IsIcon": "是否为图标", "IsText": "是否为显示为文字", "Icon": "头像框显示图标", "Text": "头像框显示文字", "Url": "Image 头像路径地址", "GetUrlAsync": "获取 Image 头像路径地址异步回调委托", "BasicUsageDivider": "分割线", "BorderDiv1": "第一幅圆角正方形头像框加载正确，所以边框为 <b class='text-info'>蓝色</b>", "BorderDiv2": "第二幅圆形头像加载图片路径错误，所以边框为 <b class='text-danger'>红色</b>，图片显示为默认图标"}, "BootstrapBlazor.Server.Components.Samples.Badges": {"Title": "Badge 徽章组件", "SubTitle": "出现在按钮、图标旁的数字或状态标记", "BasicusageTitle": "基础用法", "BasicusageIntro": "提供各种颜色的徽章小挂件组件", "PillTitle": "胶囊徽章", "PillIntro": "通过属性 <code>IsPill='true'</code> 设置圆角的徽章挂件", "ButtonTitle": "按钮内徽章", "ButtonIntro": "按钮内部徽章", "ChildContent": "内容", "Class": "样式", "Color": "颜色", "IsPill": "胶囊样式", "ButtonSpan": "主要按钮"}, "BootstrapBlazor.Server.Components.Samples.ShieldBadges": {"Title": "ShieldBadge 徽章组件", "SubTitle": "带图标标签文本的徽章组件高仿 Shields Badge", "ShieldBadgeNormalTitle": "基础用法", "ShieldBadgeNormalIntro": "通过 <code>Icon</code> 设置图标，通过 <code>Label</code> 设置左侧文本，通过 <code>Text</code> 设置右侧文本"}, "BootstrapBlazor.Server.Components.Samples.BarcodeReaders": {"Title": "BarcodeReader 条码扫描", "SubTitle": "本组件通过调用摄像头对条码进行扫描，获取到条码内容条码/QR码", "Attention": "特别注意：", "Li1": "站点要启用 <code>https</code>，这是浏览器厂商要求的", "Li2": "移动端 iOS 系统必须使用 <code>Safari</code> 浏览器，切换前/后摄像头要点一下关闭功能按钮", "Li3": "安卓手机大概率需要原生系统浏览器，<code>Chrome</code> 是必定可以的，某些浏览器可能不兼容摄像头", "Li4": "条码识别率与手机像素，条码大小，手机执行效率有关", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "通过摄像头进行扫码识别", "Step": "操作步骤：", "BasicUsageLi1": "点击开始打开摄像头", "BasicUsageLi2": "对准条码进行扫描", "BasicUsageLi3": "点击关闭按钮关闭摄像头", "ImageTitle": "基础用法", "ImageIntro": "通过静态图片进行扫码识别", "ImageLi1": "点击扫码弹出选择文件框", "ImageLi2": "选取包含条码图片", "ImageLi3": "开始识别条码", "ButtonScanText": "扫描按钮文字", "ButtonScanTextDefaultValue": "扫描", "ButtonStopText": "关闭按钮文字", "ButtonStopTextDefaultValue": "关闭", "AutoStopText": "自动关闭按钮文字", "AutoStopTextDefaultValue": "自动关闭", "DeviceLabel": "设备列表前置标签文字", "DeviceLabelDefaultValue": "摄像头", "InitDevicesString": "初始化设备列表文字", "InitDevicesStringDefaultValue": "正在识别摄像头", "NotFoundDevicesString": "未找到视频相关设备文字", "NotFoundDevicesStringDefaultValue": "未找到视频相关设备", "AutoStart": "组件初始化时是否自动开启摄像头", "AutoStop": "扫描到条码后是否自动停止", "ScanType": "扫描方式摄像头或者图片", "OnInit": "初始化摄像头回调方法", "OnResult": "扫描到条码回调方法", "OnStart": "打开摄像头回调方法", "OnClose": "关闭摄像头回调方法", "OnError": "发生错误回调方法", "OnDeviceChanged": "设备切换时回调方法", "InitLog": "初始化摄像头完成", "ScanCodeLog": "扫描到条码", "ErrorLog": "发生错误", "OpenCameraLog": "打开摄像头", "CloseCameraLog": "关闭摄像头"}, "BootstrapBlazor.Server.Components.Samples.Blocks": {"Title": "Block 条件块", "SubTitle": "根据参数条件决定是否显示块内容，通常与权限授权配合使用", "BasicUsageTitle": "普通用法", "BasicUsageIntro": "通过设置 <code>OnQueryCondition</code> 回调方法返回值判断是否显示组件内容", "TemplateTitle": "模板", "TemplateIntro": "通过设置 <code>Authorized</code> <code>NotAuthorized</code> 可以分别设置符合条件与不符合条件时显示的内容", "AuthorizateTitle": "通过权限判断", "AuthorizateIntro": "本例模拟用户登录来控制 <code>Block</code> 内容是否显示，使用比 <code>AuthorizeView</code> 简单", "UsersTitle": "预设置用户", "UsersIntro": "通过设置 <code>Users</code> 控制显示内容，当前登录用户属于预设用户时显示", "RolesTitle": "预设置角色", "RolesIntro": "通过设置 <code>Roles</code> 控制显示内容，当前登录用户属于预设角色时显示", "Tips": "通过设置不同的 <code>Name</code> 当作资源，结合授权系统对此资源授权可以实现对网页中任意元素进行权限控制", "OnQueryCondition": "是否显示此 Block", "ChildContent": "Block 块内显示内容", "Authorized": "Block 块内符合条件显示内容", "NotAuthorized": "Block 块内不符合条件显示内容", "IsShow": "显示", "IsHide": "隐藏", "Content": "我是组件内容", "Login": "登入", "Logout": "登出", "AuthorizateDiv1": "我是 <code>CondtionBlock</code> 组件内容，当前登录用户：<code>{0}</code>", "AuthorizateDiv2": "我是 <code>AuthorizeView</code> 组件内容，当前登录用户：<code>{0}</code>", "AuthorizateDiv3": "我是 <code>AuthorizeView</code> 组件内容，当前未登录", "TemplateDiv1": "我是 <b>符合</b> 条件显示的组件内容", "TemplateDiv2": "我是 <b>不符合</b> 条件显示的组件内容", "UsersDiv1": "当前用户 <code>{0}</code> 允许看到此内容", "RolesDiv1": "当前角色 <code>User</code> 允许看到此内容"}, "BootstrapBlazor.Server.Components.Samples.Cards": {"Title": "Card 卡片", "SubTitle": "将信息聚合在卡片容器中展示", "BasicUsageTitle": "Card 卡片组件", "BasicUsageIntro": "Card简单示例", "FooterTitle": "Header <PERSON>", "FooterIntro": "通过添加 HeaderTemplate 与 FooterTemplate 元素即可展示相关内容", "IsCenterTitle": "内容居中", "IsCenterIntro": "通过设置，<code>IsCenter=true</code> 使内容居中", "ColorTitle": "带有边框颜色的卡片", "ColorIntro": "通过设置，<code>Color</code> 使border和Body具有相应的颜色", "CollapsibleIntro": "通过设置 <code>IsCollapsible</code> 使 <code>Body</code> 可以伸缩", "CollapsibleIntroDesc": "通过设置 <code>Collapsed</code> 使 <code>Body</code> 默认收缩 参数默认值 <code>false</code> 展开", "CollapsibleHeaderTemplateTitle": "Header 模板", "CollapsibleHeaderTemplateIntro": "通过设置 <code>HeaderTemplate</code> 自定义 <code>CardHeader</code> 内容", "ShadowTitle": "带有阴影的卡片", "ShadowIntro": "通过设置 <code>IsShadow</code> 开启阴影特效", "BodyTemplate": "Body 模板", "FooterTemplate": "Footer 模板", "HeaderTemplate": "Header 模板", "Class": "样式", "Color": "设置卡片边框颜色", "IsCenter": "是否居中", "IsCollapsible": "是否可以收缩", "Collapsed": "默认是否收缩", "IsShadow": "是否开启阴影特效", "Compare": "对比设置", "CollapsibleTitle": "可以收缩展开的卡片", "CollapsibleBody": "点击 Header 收缩/展开", "CollapsibleHeaderTemplate": "这里是模板", "ShadowBody": "阴影特效示例", "HeaderPaddingY": "Header 上下内边距"}, "BootstrapBlazor.Server.Components.Samples.Calendars": {"Title": "Calendar 日历框", "SubTitle": "按照日历形式展示数据的容器。当数据是日期或按照日期划分时，例如日程、课表、价格日历等，农历等。目前支持年/月切换。", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "基础的日历显示。", "BindTitle": "数据双向绑定", "BindIntro": "日历框选择时间时数据自动更新文本框", "ViewModeTitle": "按周展示", "ViewModeIntro": "通过设置属性 <code>CalendarViewMode.Week</code>", "CellTemplateTitle": "单元格模板", "CellTemplateIntro": "通过设置属性 <code>CellTemplate</code> 自定义单元格模板", "HeaderTemplateTitle": "头部模板", "HeaderTemplateIntro": "通过设置属性 <code>HeaderTemplate</code> 自定义头部模板", "HeaderTemplateDesc": "通过设置 <code>BodyTemplate</code> 配合 <code>HeaderTemplate</code> 来自定义呈现 UI，本例中月视图中前后均增加了附加列，星期试图中增加了时间线", "AppTitle": "实战应用", "AppIntro": "课堂表", "AppText": "目前按周内组件暂时为统一使用 <code>ChildContext</code> 来进行渲染，所有单元格内的数据相关操作组件均未进行封装，稍后完善", "Siesta": "午休", "Value": "组件值", "ChildContent": "子组件", "ValueChanged": "值改变时回调委托", "CellTemplate": "单元格模板", "CellTemplateDemoTitle": "实战工时系统示例", "CellTemplateDemoIntro": "点击单元格弹窗编辑工时，关闭弹窗后重新汇总统计数据", "CellTemplateDemoSummary": "当月工时统计", "None": "无", "Chinese": "语文", "Math": "数学", "English": "英语", "Study": "自习", "FirstDayOfWeek": "设置每周第一天"}, "BootstrapBlazor.Server.Components.Samples.Cameras": {"Title": "Camera 摄像头拍照组件", "SubTitle": "本组件通过调用摄像头进行拍照操作", "Attention": "特别注意：", "Li1": "站点要启用 <code>https</code>，这是浏览器厂商要求的", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "通过摄像头进行拍照", "BasicUsageStep": "操作步骤：", "BasicUsageLi1": "点击开始打开摄像头", "BasicUsageLi2": "点击拍照按钮", "BasicUsageLi3": "点击关闭按钮关闭摄像头", "ShowPreview": "是否显示 照片预览", "AutoStart": "是否直接开启摄像头", "OnInit": "初始化摄像头回调方法", "OnStart": "开始拍照回调方法", "OnClose": "关闭拍照回调方法", "OnCapture": "拍照成功回调方法", "VideoWidth": "视频窗口宽度", "VideoHeight": "视频窗口高度", "CaptureJpeg": "拍照格式为 Jpeg", "Quality": "拍照图像质量", "DeviceLabel": "摄像头", "InitDevicesString": "正在识别摄像头", "PlayText": "开启", "StopText": "关闭", "PreviewText": "预览", "SaveText": "保存", "NotFoundDevicesString": "未找到视频相关设备"}, "BootstrapBlazor.Server.Components.Samples.Captchas": {"Title": "Capt<PERSON>s 滑块验证码", "SubTitle": "通过拖动滑块进行人机识别", "BasicUsageTitle": "基础功能", "BasicUsageIntro": "进行简单的人机识别", "ImageTitle": "指定图床路径与名称", "ImageIntro": "通过 <code>ImagesPath</code> 设置图床路径，通过 <code>ImagesName</code> 设置图片名称，后台通过计算拼接随机图片全路径名称", "ImageCallbackTitle": "指定图床委托方法", "ImageCallbackIntro": "通过 <code>GetImageName</code> 设置自定义方法拼接随机图片全路径名称", "ImagesPath": "图床路径", "ImagesName": "滑块背景图文件名称", "HeaderText": "组件 Header 显示文字", "HeaderTextDefaultValue": "请完成安全验证", "BarText": "拖动滑块显示文字", "BarTextDefaultValue": "向右滑动填充拼图", "FailedText": "背景图加载失败显示文字", "FailedTextDefaultValue": "加载失败", "LoadText": "背景图加载时显示文字", "LoadTextDefaultValue": "正在加载 ...", "TryText": "拼图失败滑块显示文字", "TryTextDefaultValue": "再试一次", "Offset": "拼图对齐偏移量", "Width": "拼图宽度", "Height": "拼图高度", "OnValid": "滑块验证码进行验证结果判断后回调此方法", "GetImageName": "自定义获取背景图文件名称方法"}, "BootstrapBlazor.Server.Components.Samples.Carousels": {"Title": "Carousel 走马灯", "SubTitle": "在有限空间内，循环播放同一类型的图片、文字等内容", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "适用广泛的基础用法，通过设置 <code>Items</code> 属性值对组件进行图片的绑定，值为图片路径数组", "ShowControlsTitle": "控制按钮", "ShowControlsIntro": "通过设置 <code>ShowControls</code> 属性，设置是否显示控制按钮 默认是 <code>true</code>", "ShowIndicatorsTitle": "指示器", "ShowIndicatorsIntro": "通过设置 <code>ShowIndicators</code> 属性，设置是否显示指示标识 默认是 <code>true</code>", "FadeTitle": "淡入淡出", "FadeIntro": "通过设置 <code>IsFade</code> 属性，图片切换时采用淡入淡出效果", "CaptionTitle": "标题", "CaptionIntro": "通过设置 <code>CarouselItem</code> 的 <code>Caption</code> 属性，开启标题功能", "IntervalTitle": "切换间隔", "IntervalIntro": "通过设置 <code>CarouselItem</code> 的 <code>Interval</code> 属性，可以单独设置幻灯片单独切换时间间隔，间隔默认值 5000 毫秒", "TouchSwipingTitle": "禁用手势滑动", "TouchSwipingIntro": "通过设置 <code>DisableTouchSwiping</code> 属性，禁用移动端手势滑动功能", "CaptionTemplateTitle": "标题模板", "CaptionTemplateIntro": "通过设置 <code>CarouselItem</code> 的 <code>CaptionTemplate</code> 属性，自定义标题内容", "CaptionClassTitle": "标题样式", "CaptionClassIntro": "通过设置 <code>CarouselItem</code> 的 <code>CaptionClass</code> 属性，自定义标题部分样式", "CaptionClassP1": "本例设置 <code>d-none d-md-block</code> 使小屏幕下不显示 <code>Caption</code>", "OnClickTitle": "点击图片回调事件", "OnClickIntro": "通过设置 <code>OnClick</code> 属性后，点击 <code>Image</code> 后触发 <code>OnClick</code> 回调委托", "ChildContentTitle": "子组件", "ChildContentIntro": "使用 <code>ChildContent</code> 渲染自定义组件", "Images": "Images 集合", "IsFade": "是否淡入淡出", "Width": "设置图片宽度", "OnClick": "点击图片回调委托", "HoverPause": "鼠标悬停时是否暂停播放", "PlayMode": "自动播放模式"}, "BootstrapBlazor.Server.Components.Samples.Client": {"Title": "获取客户端连接信息", "SubTitle": "多用于系统日志跟踪", "BasicUsageTitle": "普通用法", "BasicUsageIntro": "注入服务显示客户端信息", "BasicUsageP1": "用法介绍", "BasicUsageP2": "1. <b>Startup.cs</b> 文件中使用 <code>UseBootstrapBlazor</code> 中间件进行客户端信息收集", "BasicUsageTips": "<code>app.UseBootstrapBlazor</code> 中间件位于程序集 <code>BootstrapBlazor.Middleware</code>，请自行引用此包才能正常使用", "BasicUsageP3": "2. 组件中使用注入服务 <code>WebClientService</code> 调用 <code>GetClientInfo</code> 方法", "BasicUsageP4": "3. 开启 IP 地理位置定位功能", "LocatorsProviderOptions": "全局配置定位器选项 <code>WebClientOptions</code> 默认 <code>false</code> 没有启用 IP 地址定位功能，请在配置文件中或者代码中更改为 <code>true</code>", "LocatorsProviderDesc1": "更新 <code>appsetting.json</code> 项目配置文件", "LocatorsProviderDesc2": "或者使用代码开启", "LocatorsProviderDesc3": "或者通过配置开启本功能", "GroupBoxTitle": "您的连接信息", "IpLocatorFactoryDesc": "本服务已内置 IP 地理位置定位功能，详细配置与文档请参考", "Id": "连接 ID", "RequestUrl": "请求地址", "Ip": "IP 地址", "City": "城市", "OS": "操作系统", "Browser": "浏览器", "Engine": "浏览器引擎", "Device": "设备", "Language": "语言"}, "BootstrapBlazor.Server.Components.Samples.Circles": {"Title": "Circle 进度环", "SubTitle": "图表类组件。一般有两种用途：", "CircleTips1": "显示某项任务进度的百分比", "CircleTips2": "统计某些指标的占比。", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "通过设置 <code>Value</code> 属性设定圆形进度", "ColorTitle": "颜色", "ColorIntro": "通过设置 <code>Color</code> 属性设定圆形进度条颜色", "StrokeWidthTitle": "进度条宽度", "StrokeWidthIntro": "通过设置 <code>StrokeWidth</code> 属性设定圆形进度条宽度", "ChildContentTitle": "自定义显示内容", "ChildContentIntro": "通过自定义子组件自定义显示内容", "Width": "组件宽度", "StrokeWidth": "进度条宽度", "Value": "当前进度", "Color": "进度条颜色", "ShowProgress": "是否显示进度条信息", "ChildContent": "子组件", "IncreaseSpan": "增加", "DecreaseSpan": "减少", "ChildContentP1": "消费人群规模", "ChildContentSpan": "总占人数"}, "BootstrapBlazor.Server.Components.Samples.Collapses": {"Title": "Collapse 折叠面板", "SubTitle": "通过折叠面板收纳内容区域", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "可同时展开多个面板，面板之间不影响", "AccordionTitle": "手风琴效果", "AccordionIntro": "每次只能展开一个面板", "IconTitle": "图标效果", "IconIntro": "面板标题前置图标", "ColorTitle": "子项标题颜色", "ColorIntro": "每个面板设置不同颜色", "ItemsTitle": "动态更新", "ItemsIntro": "通过条件逻辑设置 <code>CollapseItems</code> 模板", "CollapseItems": "内容", "IsAccordion": "是否手风琴效果", "OnCollapseChanged": "项目收起展开状态改变回调方法", "Consistency": "一致性 Consistency", "ConsistencyItem1": "与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；", "ConsistencyItem2": "在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。", "Feedback": "反馈 Feedback", "FeedbackItem1": "控制反馈：通过界面样式和交互动效让用户可以清晰的感知自己的操作；", "FeedbackItem2": "页面反馈：操作后，通过页面元素的变化清晰地展现当前状态。", "Efficiency": "效率 Efficiency", "EfficiencyItem1": "简化流程：设计简洁直观的操作流程；", "EfficiencyItem2": "清晰明确：语言表达清晰且表意明确，让用户快速理解进而作出决策；", "EfficiencyItem3": "帮助用户识别：界面简单直白，让用户快速识别而非回忆，减少用户记忆负担。", "Controllability": "可控 Controllability", "ControllabilityItem1": "用户决策：根据场景可给予用户操作建议或安全提示，但不能代替用户进行决策；", "ControllabilityItem2": "结果可控：用户可以自由的进行操作，包括撤销、回退和终止当前操作等。", "ButtonText": "切换", "HeaderTemplateTitle": "Header 模板", "HeaderTemplateIntro": "通过设置 <code>HeaderTemplate</code> 自定义 <b>Header</b> 显示内容", "CollapseItemAttributeText": "文本文字", "CollapseItemAttributeIcon": "图标字符串", "CollapseItemAttributeTitleColor": "标题颜色", "CollapseItemAttributeClass": "样式名称", "CollapseItemAttributeHeaderClass": "Header CSS 样式名称", "CollapseItemAttributeHeaderTemplate": "Header 模板", "CollapseItemAttributeIsCollapsed": "当前状态是否收缩"}, "BootstrapBlazor.Server.Components.Samples.DateTimeRanges": {"Title": "DateTimeRange 日期时间段选择器", "Description": "在同一个选择器里选择一段日期", "NormalTitle": "基本功能", "NormalIntro": "以「日」为基本单位，选择一段时间", "BindValueTitle": "数据双向绑定", "BindValueIntro": "点击确认按钮时间选择框值与文本框值一致，通过设置 <code>AutoClose=\"true\"</code> 实现自动关闭功能，通过设置 <code>ShowSelectedValue=\"true\"</code> 直接显示选中值", "MaxMinValueTitle": "最大值和最小值", "MaxMinValueIntro": "设置时间的取值范围", "DisabledTitle": "禁用", "DisabledIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止输入", "SidebarTitle": "带快捷键侧边栏", "SidebarIntro": "设置 <code>ShowSidebar</code> 属性值为 <code>true</code> 时，组件显示快捷方式侧边栏", "SidebarTip": "通过设置  <code>ShowSidebar</code> 参数开启显示侧边栏快捷选项功能，通过设置 <code>SidebarItems</code> 参数集合替换组件内置的默认快捷项", "TodayTitle": "显示今天按钮", "TodayIntro": "设置 <code>ShowToday</code> 属性值为 <code>true</code> 时，组件下方显示今天快捷按钮", "TodayTip": "点击 <code>Today</code> 今天按钮时，时间范围为 <code>yyyy-MM-dd 00:00:00 到 yyyy-MM-dd 23:59:59</code>", "SingleViewTitle": "单选模式", "SingleViewTip": "通过设置 <code>RenderMode=\"Single\"</code> 允许独立选择开始时间与结束时间", "ValidateFormTitle": "表单中使用", "ValidateFormIntro": "将组件内置到 <code>ValidateForm</code> 中使用", "AutoCloseTitle": "自动关闭", "AutoCloseIntro": "点击侧边栏快捷方式自动关闭弹窗", "Submit": "提交", "Limit": "时间范围", "ViewModeTitle": "显示模式", "ViewModeIntro": "通过设置 <code>ViewMode=\"DatePickerViewMode.Year\"</code> 使组件视图为年视图，设置 <code>ViewMode=\"DatePickerViewMode.Month\"</code> 使组件视图为年视图", "Feature": "功能体验区", "FeatureShowLunar": "显示农历", "FeatureShowSolarTerm": "24 节气", "FeatureShowFestivals": "节日", "FeatureShowHolidays": "法定假日"}, "BootstrapBlazor.Server.Components.Samples.Ips": {"IpNormalTitle": "基础用法", "IpNormalIntro": "分段录入并显示 <code>ip</code> 地址，例如：<code>***********</code>"}, "BootstrapBlazor.Server.Components.Samples.Displays": {"Title": "Display 显示组件", "SubTitle": "显示静态文本数据", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "仅显示", "BindWayTitle": "双向绑定数据", "BindWayIntro": "通过双向绑定可以自动获取资源文件中的显示标签", "BindWayP1": "<code>Display</code> 组件开启双向绑定时，会根据绑定的 <code>Model</code> 属性值去自动获取 <code>Display/DisplayName</code> 标签值并且显示为前置 <code>Label</code>，通过 <code>DisplayText</code> 属性可以自定义显示前置标签，或者通过 <code>ShowLabel</code> 属性关闭前置标签是否显示", "DataTypeTitle": "泛型绑定", "DataTypeIntro": "<code>Display</code> 组件内置对 <code>枚举</code> <code>集合</code> <code>数组</code> 进行处理，如不符合条件时，请自定义格式化或者回调委托方法", "EditorFormTitle": "表单内使用", "EditorFormIntro": "<code>Display</code> 组件在表单组件 <code>EditorForm</code> 中使用，多用于明细页，不可编辑模式", "FormatStringTitle": "自定义格式", "FormatStringIntro": "设置 <code>FormatString</code> 属性值为 <code>yyyy-MM-dd</code> 时，组件显示的时间格式为年月日", "LookupTitle": "Lookup", "LookupIntro": "设置 <code>Lookup</code> 值为 <code>IEnumerable&lt;SelectedItem&gt;</code> 集合或者通过设置 <code>LookupServiceKey</code>，组件将通过此数据集，进行通过 <code>Value</code> 显示 <code>Text</code> 翻译工作", "LookupP1": "本例中组件 <code>Value='@IntValue'</code> 设置 <code>Lookup='@IntValueSource'</code> 组件将 <code>Value</code> 值对应的 <code>Text</code> 显示出来", "ShowLabel": "是否显示前置标签", "DisplayText": "前置标签显示文本", "FormatString": "数值格式化字符串", "Formatter": "TableHeader 实例", "TypeResolver": "类型解析回调方法 TValue 为 Array 实例时内部使用", "BasicUsage": "基础用法", "BindWayCustomLabel": "自定义标签", "BindWayP2": "设置 <code>DisplayText</code> 值为 <b>自定义标签</b>", "BindWayOccupants": "占位", "BindWayP3": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>true</code> 时均显示", "BindWayNotOccupants": "不占位", "BindWayP4": "无论是否设置 <code>DisplayText</code> 值，当 <code>ShowLabel</code> 为 <code>false</code> 时均不显示", "Integer": "整型", "Enum": "枚举", "Collection": "集合", "Arr": "数组", "FormatStringSettingText": "设置", "FormatStringP": "<code>Display</code> 组件绑定 <code>byte[]</code> 数组，格式化成 <code>base64</code> 编码字符串示例", "ShowTooltipTitle": "显示 Tooltip", "ShowTooltipIntro": "组件包裹 <code>Tooltip</code> 或者 <code>ShowTooltip=\"true\"</code> 后内置 <code>BootstrapInputGroup</code> 组件使用"}, "BootstrapBlazor.Server.Components.Samples.DropdownWidgets": {"Title": "DropdownWidget 下拉挂件", "SubTitle": "多用于头部信息汇总展示", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "直接在页面上通过 <code>ChildContent</code> 编写挂件内容", "AttributeTitle": "DropdownWidgetItem 组件", "Icon": "挂件图标", "BadgeColor": "徽章颜色", "HeaderColor": "Header 颜色", "BadgeNumber": "徽章显示数量", "ShowArrow": "是否显示小箭头", "MenuAlignment": "菜单对齐方式", "HeaderTemplate": "Header 模板", "BodyTemplate": "Body 模板", "FooterTemplate": "Footer 模板", "BasicUsageMessage": "您有 4 个未读消息", "BasicUsageViewMessage": "查看所有消息", "BasicUsageNotify": "您有 10 个未读通知", "BasicUsageViewNotify": "查看所有通知", "BasicUsageTasks": "您有 3 个任务", "BasicUsageViewTasks": "查看所有任务", "OnItemCloseAsync": "关闭菜单项回调方法"}, "BootstrapBlazor.Server.Components.Samples.Empties": {"Title": "Empty 空状态", "SubTitle": "空状态时的展示占位图", "NormalTitle": "基础用法", "NormalIntro": "添加 <code>Empty</code> 标签即可", "ImageTitle": "设置空状态图片路径", "ImageIntro": "设置 <code>Image</code> 属性即可", "TemplateTitle": "自定义空状态模板", "TemplateIntro": "内套 <code>Template</code> 标签渲染自定义组件", "Image": "自定义图片路径", "Text": "自定义描述信息", "TextDefaultValue": "暂无描述", "Width": "自定义图片宽度", "Height": "自定义图片高度", "Template": "自定义模板", "ChildContent": "子组件", "ImageText": "暂无数据", "TemplateText": "自定义空状态模板", "TemplateIButtonText": "返回上一页"}, "BootstrapBlazor.Server.Components.Samples.GroupBoxes": {"Title": "GroupBox 集合组件", "SubTitle": "模拟 WinForm 的 GroupBox 组件", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "将自己的组件放到 <code>GroupBox</code> 内部即可", "AttTitle": "设置组件标题", "GroupTitle": "表单示例", "GroupP1": "我是 <code>GroupBox</code> 示例", "GroupP2": "更多示例请参考 <code>EditorForms</code> 表单示例 <a href='editor-form' target='_blank'>[传送门]</a>"}, "BootstrapBlazor.Server.Components.Samples.LinkButtons": {"LinkButtonsComTitle": "LinkButton 链接按钮", "LinkButtonsSubTitle": "提供 A 标签按钮", "LinkButtonTextTitle": "链接按钮", "LinkButtonTextIntro": "通过 <code>Text</code> 值设置按钮显示文本", "LinkButtonText": "链接按钮", "LinkButtonUrlTitle": "导航地址", "LinkButtonUrlIntro": "通过 <code>Url</code> 值设置导航地址", "LinkButtonTitleTitle": "提示信息", "LinkButtonTitleIntro": "通过 <code>Title</code> 值设置链接按钮 <code>Tooltip</code>", "LinkButtonTitleDetail": "鼠标悬停到按钮上时显示 <code>Tooltip</code>", "LinkButtonImageTitle": "图片", "LinkButtonImageIntro": "通过 <code>ImageUrl</code> 值设置图片地址", "LinkButtonChildTitle": "模板", "LinkButtonChildIntro": "通过 <code>ChildContent</code> 模板自定义显示内容", "LinkButtonIconTitle": "图标按钮", "LinkButtonIconIntro": "通过设置 <code>Icon</code> 显示图标", "LinkButtonOnClickTitle": "点击事件", "LinkButtonOnClickIntro": "通过 <code>OnClick</code> 回调方法响应点击事件", "LinkButtonColorTitle": "颜色", "LinkButtonColorIntro": "通过设置 <code>Color</code> 值，更改按钮文字颜色", "LinkButtonIsDisabledTitle": "禁用", "LinkButtonIsDisabledIntro": "通过设置 <code>IsDisabled</code> 值使按钮失效", "LinkButtonVerticalTitle": "垂直布局", "LinkButtonVerticalIntro": "通过设置 <code>IsVertical</code> 值使按钮图标与文字垂直布局", "Text": "显示文本", "Url": "Url", "Title": "Tooltip 显示文字", "ImageUrl": "显示图片地址", "TooltipPlacement": "Tooltip 显示位置", "ChildContent": "子组件", "OnClick": "点击事件回调方法", "Icon": "按钮 Icon", "LinkButtonTitleTooltip": "我是提示信息条", "LinkButtonIsDisabledText": "禁用", "LinkButtonColorText": "颜色按钮", "LinkButtonVerticalText": "测试按钮"}, "BootstrapBlazor.Server.Components.Samples.ListViews": {"ListViewsTitle": "ListView 列表视图", "ListViewsSubTitle": "提供规则排列控件", "ProductListText": "产品列表", "BasicUsageTitle": "普通用法", "BasicUsageIntro": "适用于大量重复的数据实现规则排列", "BasicUsageP1": "点击图片时触发 <code>OnListViewItemClick</code> 事件", "PaginationTitle": "分页显示", "PaginationIntro": "设置 <code>Pageable</code> 显示分页组件", "GroupTitle": "分组显示", "GroupIntro": "设置 <code>GroupName</code> 数据进行分组显示，通过设置 <code>GroupOrderCallback</code> 参数细化分组排序规则", "CollapseTitle": "分组折叠", "CollapseIntro": "设置 <code>Collapsable=\"true\"</code> 使分组信息可折叠", "Collapsible": "是否可折叠", "IsAccordionTitle": "分组手风琴", "IsAccordionIntro": "设置 <code>IsAccordion=\"true\"</code> 使分组信息折叠手风琴效果", "Items": "组件数据源", "Pageable": "是否分页", "HeaderTemplate": "ListView Header 模板", "BodyTemplate": "ListView Body 模板", "FooterTemplate": "ListView Footer 模板", "Collapse": "分组数据折叠", "IsAccordion": "分组数据手风琴效果", "OnQueryAsync": "异步查询回调方法", "OnListViewItemClick": "ListView元素点击时回调委托", "QueryAsync": "手工查询数据方法", "CollapsedGroupCallback": "组件分组项是否收缩回调委托方法", "GroupOrderCallback": "组件分组项排序回调委托方法", "GroupItemOrderCallback": "组件分组项内项目集合排序回调方法", "GroupHeaderTextCallback": "分组标题显示文本回调方法"}, "BootstrapBlazor.Server.Components.Samples.Locators": {"LocatorsTitle": "获取 IP 地理位置", "LocatorsSubTitle": "多用于系统日志跟踪与分析", "LocatorsNormalTitle": "普通用法", "LocatorsNormalIntro": "注入服务显示客户端地理位置信息", "LocatorsNormalDescription": "用法介绍", "LocatorsNormalInjectIPLocator": "组件中使用注入服务 <code>IpLocatorFactory</code> 调用 <code>Create</code> 方法创建定位服务 <code>IIpLocatorProvider</code> 实例", "LocatorsNormalTipsTitle": "某些地理位置查询接口返回字符集可能是其他字符集如 <code>gbk</code>，程序会报错；", "LocatorsNormalTips1": "解决办法：", "LocatorsNormalTips2": "<code>Startup</code> 文件中 <code>ConfigureServices</code> 方法内增加下面这句话即可解决", "LocatorsNormalExtendDescription": "扩展自定义地理位置查询接口", "LocatorsNormalExtend1": "1. 实现自定义定位器", "LocatorsNormalExtend2": "2. 配置自定义定位器", "LocatorsNormalExtend3": "3. 通过定位器定位", "LocatorsNormalCustomerLocator": "通过 <code>AddSingleton</code> 方法将自定义定位器 <code>CustomerLocatorProvider</code> 添加到服务容器中", "LocatorsNormalIpTitle": "IP 测试数据", "LocatorsNormalTips3": "山东省 中国联通", "LocatorsNormalTips4": "安徽省合肥市蜀山区 中国电信", "LocatorsNormalInputText": "Ip 地址", "LocatorsNormalDisplayText": "地理位置", "LocatorsNormalButtonText": "定位", "LocatorsProviderDesc": "<p>组件库内置两个免费在线地理位置定位器分别为 <code>BaiduIpLocatorProvider</code> <code>BaiduIpLocatorProviderV2</code><p><p>组件库内置一个收费在线地理位置定位器 <code>BootstrapBlazor.JuHeIpLocatorProvider</code> <a href=\"https://www.nuget.org/packages/BootstrapBlazor.JuHeIpLocatorProvider\" target=\"_blank\">Nuget 包</a> <a href=\"https://juhe.cn\" target=\"_blank\">官网地址</a></p><p>组件库内置一个免费离线地理位置定位器 <code>BootstrapBlazor.IP2Region</code> <a href=\"https://www.nuget.org/packages/BootstrapBlazor.IP2Region\" target=\"_blank\">Nuget 包</a></p>"}, "BootstrapBlazor.Server.Components.Samples.Print": {"PrintsTitle": "Print 打印按钮", "PrintsSubTitle": "用于打印文档或者局部视图", "PrintsTips1": "设置 <code style='margin-inline-start: 14px;'>PreviewUrl</code> 时，单开一个新网页进行打印预览，点击此页面的打印按钮进行网页打印", "PrintsTips2": "不设置 <code>PreviewUrl</code> 时，如果是 <code>Dialog</code> 组件中的打印按钮，则打印弹窗内容", "PrintsButtonText": "打印", "PrintButtonTitle": "普通用法", "PrintButtonIntro": "通过点击打印按钮将页面进行打印", "PrintsButtonDescription": "点击下方打印按钮后，弹出新页面进行打印预览，确认无误后点击打印预览页面中的打印按钮进行打印机选择进行打印操作", "PrintDialogTitle": "打印弹窗", "PrintDialogIntro": "通过设置弹窗组件 <code>ShowPrint</code> 开启打印功能", "PrintDialogP": "本例中弹窗内容为自定义组件 <code>{0}</code> 弹窗自身按钮所在 <code>Footer</code> 被隐藏，所以弹窗内置的 <code>Print</code> 按钮无法显示，设置 <code>@nameof(DialogOption.ShowPrintButtonInHeader)</code> 使 <b>打印</b> 按钮显示在标题栏中", "PrintServiceTitle": "打印服务", "PrintServiceIntro": "通过设置要打印的内容组件，直接调用 <b>打印服务</b> 进行打印作业，打印服务 <code>PrintService</code> 服务文档 <a href=\"print-service\" target=\"_blank\">[传送门]</a>", "PrintsDialogTitle": "数据查询窗口"}, "BootstrapBlazor.Server.Components.Samples.Tags": {"TagsTitle": "Tag 标签", "TagsSubTitle": "用于标记和选择。", "Tag1": "标签一", "Tag2": "标签二", "Tag3": "标签三", "Tag4": "标签四", "Tag5": "标签五", "Tag6": "标签六", "Tag7": "标签七", "TagsNormalTitle": "基础用法", "TagsNormalIntro": "页面中的非浮层元素，不会自动消失。", "TagsCloseButtonTitle": "关闭按钮", "TagsCloseButtonIntro": "提供关闭按钮的标签", "TagsIconTitle": "带 Icon", "TagsIconIntro": "表示某种状态时提升可读性。", "TagsChildContent": "内容", "TagsClass": "样式", "TagsColor": "颜色", "TagsIcon": "图标", "TagsShowDismiss": "关闭按钮", "TagsOnDismiss": "关闭标签回调方法"}, "BootstrapBlazor.Server.Components.Samples.Timelines": {"TimelinesTitle": "Timeline 时间线", "TimelinesSubTitle": "可视化地呈现时间流信息", "TimelinesNormalTitle": "基础用法", "TimelinesNormalIntro": "<cdoe>Timeline</code> 可拆分成多个按照时间戳正序或倒序排列的 <cdoe>activity</code>，时间戳是其区分于其他控件的重要特征，使⽤时注意与 <cdoe>Steps</code> 步骤条等区分。", "TimelinesNormalDescription": "排序：", "TimelinesCustomNodeStyleTitle": "⾃定义节点样式", "TimelinesCustomNodeStyleIntro": "可根据实际场景⾃定义节点颜⾊，或直接使⽤图标。", "TimelinesLeftTitle": "左侧展现", "TimelinesLeftIntro": "内容在时间轴左侧轮流出现", "TimelinesLeftDescription": "通过设置 <code>IsLeft</code> 属性来控制内容出现在时间线左侧", "TimelinesAlternateTitle": "交替展现", "TimelinesAlternateIntro": "内容在时间轴两侧轮流出现", "TimelinesAlternateDescription": "通过设置 <code>IsAlternate</code> 属性来控制时间线左右交替展现", "TimelinesDisplayCustomComponentTitle": "展现自定义组件", "TimelinesDisplayCustomComponentIntro": "时间轴上展现自定义组件", "TimelinesDisplayCustomComponentDescription": "通过设置 <code>TimelineItem</code> 的 <code>CustomerComponent</code> 来控制时间线展现的自定义组件", "TimelinesSelectedItem1": "正序", "TimelinesSelectedItem2": "倒序", "TimelineItemContent1": "创建时间", "TimelineItemContent2": "通过审核", "TimelineItemContent3": "活动按期开始", "TimelineItemContent4": "默认样式的节点", "TimelineItemContent5": "支持自定义颜色", "TimelineItemContent6": "支持使用图标", "TimelinesDescription1": "实时输出", "TimelinesDescription2": "计数器", "TimelinesDescription3": "天气预报信息", "TimelinesItems": "数据集合", "TimelinesIsReverse": "是否倒序显示", "TimelinesIsLeft": "是否左侧展现内容", "TimelinesIsAlternate": "是否交替展现内容", "TimelinesColor": "节点颜色", "TimelinesContent": "内容正文", "TimelinesIcon": "节点图标", "TimelinesDescription": "节点描述文字", "TimelinesComponent": "节点自定义组件", "TimelinesAttributeTitle": "TimelineItem 属性"}, "BootstrapBlazor.Server.Components.Samples.Searches": {"SearchesTitle": "Search 搜索框", "SearchesSubTitle": "用于数据搜索", "SearchesPlaceHolder": "搜索示例", "SearchesNormalTitle": "基础用法", "SearchesNormalIntro": "输入部分数据进行搜索", "SearchesNormalDescription": "请输入 <code>1234</code> 获取智能提示，通过设置 <code>IsAutoFocus='true'</code> 开启自动获得焦点功能", "SearchesDisplayButtonTitle": "显示清空按钮", "SearchesDisplayButtonIntro": "通过设置 <code>ShowClearButton</code> 参数控制是否显示清空按钮", "SearchesKeyboardsTitle": "键盘输入即时搜索", "SearchesKeyboardsIntro": "通过设置 <code>IsTriggerSearchByInput</code> 参数控制是否实时进行搜索操作，组件默认输入时即进行搜索，可通过 <code>IsTriggerSearchByInput=\"false\"</code> 关闭", "SearchesValidateFormTitle": "验证表单内使用", "SearchesValidateFormIntro": "内置于 <code>ValidateForm</code> 使用，输入中文时不会多次触发搜索功能", "SearchesNoDataTip": "自动完成数据无匹配项时提示信息", "SearchesNoDataTipDefaultValue": "无匹配数据", "SearchesButtonLoadingIcon": "正在搜索按钮图标", "SearchesClearButtonIcon": "清空按钮颜色", "SearchesClearButtonText": "清空按钮文本", "SearchesClearButtonColor": "清空按钮颜色", "SearchesButtonColor": "搜索按钮颜色", "SearchesIsAutoFocus": "是否自动获得焦点", "SearchesIsAutoClearAfterSearch": "点击搜索后是否自动清空搜索框", "SearchesIsTriggerSearchByInput": "搜索模式是否为输入即触发,默认点击搜索按钮触发", "SearchesShowClearButton": "是否显示清除按钮", "SearchesOnSearch": "点击搜索时回调此委托", "SearchesOnClear": "点击清空时回调此委托", "SearchesItemTemplateTitle": "模板", "SearchesItemTemplateIntro": "通过设置 <code>ItemTemplate</code> 配合泛型数据可以做出自己想要的任何效果，本例中通过搜索任意关键字，后台调用任意第三方搜索结果并且进行展示，选中搜索项后通过 <code>OnSelectedItemChanged</code> 回调方法可以自行处理", "SearchesShowPrefixIconTitle": "显示前缀图标", "SearchesShowPrefixIconIntro": "通过设置 <code>ShowPrefixIcon</code> 参数控制是否显示前缀图标", "SearchesShowPrefixIconDescription": "可以通过 <code>PrefixIconTemplate</code> 自定义前缀，本例中通过前缀模板使用 <code>Svg</code> 图标", "SearchesButtonTemplateTitle": "按钮模板", "SearchesButtonTemplateIntro": "通过设置 <code>ButtonTemplate</code> 自定义组件显示的按钮", "SearchesButtonTemplateDesc": "通过设置 <code>PrefixButtonTemplate</code> 自定义组件前置显示的按钮", "SearchesButtonTemplateDesc2": "在自定义模板中可以通过关联参数调用 <code>Search</code> 组件内部的 <code>OnClear</code> <code>OnSearch</code> 方法", "SearchesIsClearableTitle": "IsClearable", "SearchesIsClearableIntro": "通过设置 <code>IsClearable=\"true\"</code> 显示清空小图标", "SearchesIsClearable": "是否显示清空小按钮", "SearchesClearIcon": "清空图标", "SearchesPrefixButtonTemplate": "前置按钮模板", "SearchesButtonTemplate": "按钮模板", "SearchesIconTemplateTitle": "图标模板", "SearchesIconTemplateIntro": "通过设置 <code>IconTemplate</code> 自定义组件显示的图标", "SearchesIconTemplateDesc": "搜索组件上下文 <code>SearchContext&lt;string&gt;</code> 提供了组件内部的 <code>OnClear</code> <code>OnSearch</code> 方法"}, "BootstrapBlazor.Server.Components.Samples.Titles": {"Title": "Title 网站标题", "SubTitle": "用于设置网页页面标题", "Tips": "<p><code>NET6.0</code> 可使用微软 <code>PageTitle</code> 组件，用于设置网页标题，本组建额外提供了以服务的形式设置当前网页标题</p>", "BasicUsageTitle": "基础用法", "BasicUsageIntro": "网页中添加 <code>Title</code> 组件并设置 <code>Text</code> 属性即可", "BasicUsageTips": "<code>Title</code> 组件设置 <code>Text</code> 后为普通组件使用，未设置 <code>Text</code> 属性时供服务调用", "BasicUsageP": "<b>设置 <code>Text</code> 属性</b>", "BasicUsageTitleText": "我是标题", "BasicUsageP1": "测试网页", "AdvanceTitle": "高级用法", "AdvanceIntro": "网页中无需添加 <code>Title</code> 组件，调用注入服务 <code>TitleService</code>", "AdvanceB": "特别注意：", "AdvanceP": "使用此种方法时请在 <code>App</code> 或者 <code>MainLayout</code> 或者 <code>Page</code> 中添加 <b>未设置 <code>Text</code> 属性</b> 的 <code>Title</code> 组件", "FuncationParmeter": "我是标题", "StaticTitle": "静态方法", "StaticIntro": "直接调用 <code>TitleService</code> 静态方法", "StaticB": "特别注意：", "StaticDiv": "使用此种方法时请在 <code>App</code> 或者 <code>MainLayout</code> 或者 <code>Page</code> 中添加 <b>未设置 <code>Text</code> 属性</b> 的 <code>Title</code> 组件"}, "BootstrapBlazor.Server.Components.Components.DemoBlock": {"Title": "未设置", "TooltipText": "已拷贝到剪贴板"}, "BootstrapBlazor.Server.Components.Components.Tips": {"Title": "小提示"}, "BootstrapBlazor.Server.Components.Components.AttributeTable": {"Title": "Attributes 属性"}, "BootstrapBlazor.Server.Components.Components.MethodTable": {"Title": "Methods 方法"}, "BootstrapBlazor.Server.Components.Common.AttributeItem": {"Name": "参数", "Description": "说明", "Type": "类型", "ValueList": "可选值", "DefaultValue": "默认值"}, "BootstrapBlazor.Server.Components.Common.EventItem": {"Name": "参数", "Description": "说明", "Type": "类型"}, "BootstrapBlazor.Server.Components.Common.MethodItem": {"Title": "Methods 方法", "Name": "参数", "Description": "说明", "Type": "类型", "Parameters": "参数", "ReturnValue": "返回值"}, "BootstrapBlazor.Server.Components.Layout.ComponentLayout": {"Title": "基于 Bootstrap 和 Blazor 的企业级组件库", "Example": "示例", "Video": "相关视频", "ViewRazorCode": "查看 Razor 代码", "ViewC#Code": "查看 C# 代码", "ViewExample": "查看示例", "IconTheme": "图标主题", "Group": "交流群"}, "BootstrapBlazor.Server.Components.Samples.Alerts": {"Title": "<PERSON><PERSON> 警告", "SubTitle": "用于页面中展示重要的提示信息。", "BaseUsageText": "基础用法", "IntroText1": "页面中的非浮层元素，不会自动消失。", "CloseButtonUsageText": "关闭按钮", "IntroText2": "提供关闭按钮的警告框", "WithIconUsageText": "带 Icon", "IntroText3": "表示某种状态时提升可读性。", "ShowBarUsageText": "显示左侧 Bar", "IntroText4": "作为 <code>Tip</code> 使用", "ShowBorderTitle": "边框效果", "ShowBorderIntro": "设置 <code>ShowBorder=\"true\"</code> 开启边框效果", "ShowShadowTitle": "阴影效果", "ShowShadowIntro": "设置 <code>ShowShadow=\"true\"</code> 开启阴影效果", "AlertPrimaryText": "主要的警告框", "AlertSecondaryText": "次要的警告框", "AlertSuccessText": "成功的警告框", "AlertDangerText": "危险的警告框", "AlertWarningText": "警告的警告框", "AlertInfoText": "信息的警告框", "AlertDarkText": "黑暗的警告框"}, "BootstrapBlazor.Server.Components.Samples.RibbonTabs": {"RibbonTabsTitle": "RibbonTab 选项卡", "RibbonTabsDescription": "Office 菜单选项卡", "RibbonTabsNormalTitle": "基本用法", "RibbonTabsNormalIntro": "通过设置 <code>Items</code> 初始化选项卡", "RibbonTabsFloatTitle": "可悬浮", "RibbonTabsFloatIntro": "通过设置 <code>ShowFloatButton</code> 使选项卡右侧显示收缩按钮，不占用主窗体空间", "RibbonTabsFloatContent": "我是正文内容，收起菜单后我会向上移动", "RibbonTabsRightButtonsTemplateTitle": "右上角按钮模板", "RibbonTabsRightButtonsTemplateIntro": "通过设置 <code>RightButtonsTemplate</code> 可以在选项卡右上角增加一些快捷按钮", "RibbonTabsRightButtonsTemplateContent": "文档", "RibbonTabsShowFloatButtonAttr": "是否显示悬浮小箭头", "RibbonTabsOnFloatChanged": "组件是否悬浮状态改变时回调方法", "RibbonTabsRibbonArrowUpIcon": "选项卡向上箭头图标", "RibbonTabsRibbonArrowDownIcon": "选项卡向下箭头图标", "RibbonTabsRibbonArrowPinIcon": "选项卡可固定图标", "RibbonTabsShowFloatButton": "是否显示悬浮小箭头", "RibbonTabsItems": "数据源", "RibbonTabsOnItemClickAsync": "点击命令按钮回调方法", "OnMenuClickAsyncAttr": "点击一级菜单回调方法", "RibbonTabsRightButtonsTemplate": "右侧按钮模板", "RibbonTabsItemsText1": "文件", "RibbonTabsItems1": "常规操作", "RibbonTabsItems2": "常规操作", "RibbonTabsItems3": "常规操作", "RibbonTabsItems4": "打开", "RibbonTabsItems5": "保存", "RibbonTabsItems6": "另存为", "RibbonTabsItemsText2": "编辑", "RibbonTabsItems7": "打开", "RibbonTabsItems8": "保存", "RibbonTabsItems9": "另存为", "RibbonTabsItems10": "常规操作", "RibbonTabsItems11": "常规操作", "RibbonTabsItems12": "常规操作", "ItemsGroupName1": "操作组一", "ItemsGroupName2": "操作组三", "RibbonTabsHeaderClickTitle": "Header 点击回调", "RibbonTabsHeaderClickIntro": "通过设置 <code>OnHeaderClickAsync</code> 回调方法处理点击 <code>Header</code> 部分逻辑", "RibbonTabsAnchorTitle": "锚点支持", "RibbonTabsAnchorIntro": "通过设置 <code>IsSupportAnchor</code> 开启是否支持锚点功能", "RibbonTabsAnchorDesc": "通过设置 <code>EncodeAnchorCallback</code> 自定义哈希编码规则，通过设置 <code>DecodeAnchorCallback</code> 自定义哈希解码规则，通过这两个方法支持自定义多级哈希，本例中特意使用组合 <code>Hash</code> 使页面滚动到下方"}, "BootstrapBlazor.Server.Components.Samples.Logouts": {"LogoutsTitle": "Logout 登出组件", "LogoutsDescription": "用于网站登出操作", "LogoutsNormalTitle": "普通用法", "LogoutsNormalIntro": "直接使用内置模板使用", "LogoutsShowUserNameTitle": "仅显示头像", "LogoutsShowUserNameIntro": "设置 <code>ShowUserName</code> 值为 <code>false</code>", "LogoutsChildContentTitle": "自定义显示模板", "LogoutsChildContentIntro": "设置 <code>ChildContent</code> 或者在组件内部直接写其他组件即可", "LogoutsChildContentCustomDisplay": "自定义显示内容", "LogoutsHeaderTemplateTitle": "自定义登录信息模板", "LogoutsHeaderTemplateIntro": "设置 <code>HeaderTemplate</code>", "LogoutsHeaderTemplateUser1": "总经理", "LogoutsHeaderTemplateUser2": "Admin", "LogoutsLinkTemplateTitle": "自定义链接模板", "LogoutsLinkTemplateIntro": "设置 <code>LinkTemplate</code>", "LogoutsLinkTemplatePersonalCenter": "个人中心", "LogoutsLinkTemplateSetup": "设置"}, "BootstrapBlazor.Server.Components.Samples.AutoRedirects": {"Title": "AutoRedirect 自动跳转组件", "Description": "通过设置的地址，当页面无鼠标或者键盘动作时自动跳转到指定页面", "NormalTitle": "普通用法", "NormalIntro": "通过设置 <code>Interval</code> 间隔，当无鼠标或者键盘动作时自动跳转到 <code>RedirectUrl</code> 设置的地址"}, "BootstrapBlazor.Server.Components.Samples.AnchorLinks": {"AnchorLinkTitle": "AnchorLink 锚点链接", "AnchorLinkDescribe1": "应用于标题带", "AnchorLinkDescribe2": "的锚点链接，点击拷贝方便分享", "AnchorLinkDemoTitle": "普通用法", "AnchorLinkDemoIntroduction": "放置标签后点击拷贝锚点链接到粘贴板", "AnchorLinkTips1": "锚点链接组件属性", "AnchorLinkTips2": "为必填项，不填写时不提供拷贝锚点链接功能", "AnchorLinkTips3": "组件锚点图标可通过", "AnchorLinkTips4": "参数进行自定义默认为", "AnchorLinkTips5": "参数 <code>TooltipText</code> 用于设置拷贝地址后提示信息 默认 <code>null</code> 不弹出提示信息", "AttrId": "Id", "AttrIcon": "图标", "AttrText": "文本", "AttrTooltipText": "提示栏文本", "AnchorLinkText": "我是一个可以点击的锚点链接"}, "BootstrapBlazor.Server.Components.Samples.QRCodes": {"QRCodesTitle": "QRCode 二维码", "QRCodesSubTitle": "用于二维码生成", "QRCodesNormalTitle": "基础用法", "QRCodesNormalIntro": "点击生成按钮，生成特定的 <code>QRCode</code>", "QRCodesContentTitle": "直接生成", "QRCodesContentIntro": "通过 <code>Content</code> 参数指定二维码内容", "QRCodesWidthTitle": "大小", "QRCodesWidthIntro": "通过 <code>Width</code> 参数指定二维码宽度，高度与宽度一致", "QRCodesColorTitle": "颜色", "QRCodesColorIntro": "通过 <code>DarkColor</code> 参数指定二维码黑色部分颜色，<code>LightColor</code> 参数指定二维码白色部分颜色", "SuccessText": "二维码生成成功", "ClearText": "二维码清除成功", "OnGenerated": "二维码生成后回调委托", "OnCleared": "二维码清除后回调委托", "Width": "大小", "ClearButtonText": "清除按钮文字", "ClearButtonIcon": "清除按钮图标", "GenerateButtonText": "生成按钮文字", "GenerateButtonIcon": "生成按钮图标", "ShowButtons": "是否显示按钮", "DarkColor": "黑色部分替代颜色", "LightColor": "白色部分替代颜色", "QRCodesPlaceHolderValue": "请输入内容", "QRCodesClearButtonTextValue": "清除", "QRCodesGenerateButtonTextValue": "生成"}, "BootstrapBlazor.Server.Components.Samples.Anchors": {"Title": "<PERSON><PERSON> 锚点", "SubTitle": "用于跳转到页面指定位置", "BaseUsageText": "基础用法", "IntroText1": "需要展现当前页面上可供跳转的锚点链接，以及快速在锚点之间跳转", "IntroText2": "点击下面 <code><PERSON><PERSON></code> 项目，页面滚动到相对应的章节", "ContentText1": "<p>与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。</p><p>控制反馈：通过界面样式和交互动效让用户可以清晰的感知自己的操作；</p><p>页面反馈：操作后，通过页面元素的变化清晰地展现当前状态。</p><p>简化流程：设计简洁直观的操作流程；</p><p>清晰明确：语言表达清晰且表意明确，让用户快速理解进而作出决策；</p><p>帮助用户识别：界面简单直白，让用户快速识别而非回忆，减少用户记忆负担。</p>", "DescTarget": "锚点目标 Id", "DescIsAnimation": "是否开启动画效果", "DescContainer": "滚动条所在元素 Id", "DescOffset": "偏移量用于调整间隙使用", "DescChildContent": "内容"}, "BootstrapBlazor.Server.Components.Samples.Breadcrumbs": {"Title": "Breadcrumb 面包屑", "Describe": "显示当前页面的路径，快速返回之前的任意页面。", "Block1Title": "基础用法", "Block1Intro": "适用广泛的基础用法。"}, "BootstrapBlazor.Server.Data.Foo": {"Name": "姓名", "DateTime": "日期", "Address": "地址", "Address.PlaceHolder": "不可为空", "Count": "数量", "Count.PlaceHolder": "不可为空", "Complete": "是/否", "Education": "学历", "Hobby": "爱好", "ReadonlyColumn": "只读列", "Name.Required": "{0}是必填项", "Address.Required": "{0}是必填项", "Education.Required": "{0}是必选项", "Hobby.Required": "请选择一种{0}", "Name.PlaceHolder": "不可为空", "Hobbies": "游泳,登山,打球,下棋", "Foo.Name": "张三 {0}", "Foo.Address": "上海市普陀区金沙江路 {0} 弄", "Foo.Address2": "地球、中国、上海市普陀区金沙江路 {0} 弄 这里是超长单元格示例", "Foo.BindValue": "绑定值", "True": "通过", "False": "未通过", "NullItemText": "未设置"}, "BootstrapBlazor.Server.Components.Samples.ValidateForms.ComplexFoo": {"Name": "姓名", "Name.Required": "{0} 值是必填项"}, "BootstrapBlazor.Server.Components.Samples.ValidateForms.Dummy2": {"Name": "姓名", "Name.Required": "Dummy2 {0} 值是必填项"}, "BootstrapBlazor.Server.Data.EnumEducation": {"PlaceHolder": "请选择 ...", "Primary": "小学", "Middle": "中学"}, "BootstrapBlazor.Server.Components.Components.ThemeChooser": {"Title": "点击切换主题", "HeaderText": "请选择主题"}, "BootstrapBlazor.Server.Components.Samples.Uploads.Person": {"Name": "姓名", "Name.Required": "{0}不能为空", "Picture": "上传文件", "Picture.Required": "上传文件不能为空"}, "BootstrapBlazor.Server.Components.Components.CultureChooser": {"Label": "语言："}, "BootstrapBlazor.Server.Components.Components.GlobalSearch": {"SearchPlaceHolder": "搜索", "SearchingText": "正在搜索 ...", "SearchResultText": "匹配到 {0} 条记录，耗时 {1} 毫秒", "LogoText": "Powered by BootstrapBlazor", "SearchResultPlaceHolder": "键入要搜索的内容", "EmptySearchResultPlaceHolder": "无匹配搜索", "EnterKeyText": "选择", "ArrowKeyText": "导航", "EscKeyText": "关闭"}, "BootstrapBlazor.Server.Components.Components.Header": {"DownloadText": "Download", "HomeText": "首页", "IntroductionText": "文档", "TutorialsText": "实战", "FullScreenTooltipText": "点击切换全屏模式"}, "BootstrapBlazor.Server.Components.Layout.BaseLayout": {"SiteTitle": "Bootstrap Blazor - 组件库", "FlowText": "工作流", "InstallAppText": "安装小程序", "InstallText": "安装", "CancelText": "取消", "Title": "点击查看更新日志", "ChatTooltip": "Azure OpenAI", "LightMode": "明亮模式", "DarkMode": "暗黑模式"}, "BootstrapBlazor.Server.Components.Layout.NavMenu": {"GetStarted": "快速上手", "Introduction": "简介", "Install": "类库安装", "ProjectTemplate": "项目模板", "Globalization": "全球化", "Localization": "本地化", "WebAppBlazor": "WebApp 模式", "ServerBlazor": "服务器端模式", "ClientBlazor": "客户端模式", "MauiBlazor": "MAUI Blazor 模式", "ZIndex": "组件层次", "Breakpoints": "断点阈值", "Theme": "组件主题", "Labels": "表单标签", "GlobalException": "全局异常", "GlobalOption": "全局配置", "LayoutPage": "后台模拟器", "FAIcon": "FontAwesome Icons", "Components": "组件总览", "LayoutComponents": "布局组件", "Client": "客户信息服务 Client", "Divider": "分割线 Divider", "DragDrop": "拖拽组件 DragDrop", "Layout": "布局组件 Layout", "FullScreen": "全屏组件 FullScreen", "Footer": "页脚组件 Footer", "Row": "行组件 Row", "Scroll": "滚动条 Scroll", "Skeleton": "骨架屏 Skeleton", "Split": "分割面板 Split", "Responsive": "断点通知 Responsive", "NavigationComponents": "导航组件", "Anchor": "锚点 Anchor", "AnchorLink": "锚点链接 AnchorLink", "AutoRedirect": "自动跳转 AutoRedirect", "Breadcrumb": "面包屑 Breadcrumb", "Dropdown": "下拉菜单 Dropdown", "GoTop": "跳转组件 GoTop", "Logout": "登出组件 Logout", "Menu": "菜单 Menu", "Navigation": "导航栏 Nav", "Pagination": "分页 Pagination", "Steps": "步骤条 Step", "Tab": "标签页 Tab", "NotificationComponents": "通知组件", "Alert": "警告框 Alert", "Console": "控制台 Console", "Dialog": "对话框 Dialog", "Dispatch": "消息分发 Dispatch", "Drawer": "抽屉 Drawer", "EditDialog": "编辑弹窗 EditDialog", "FloatingLabel": "悬浮标签 FloatingLabel", "Message": "消息框 Message", "Modal": "模态框 Modal", "Light": "指示灯 Light", "PopConfirm": "确认框 PopConfirmButton", "Progress": "进度条 Progress", "SearchDialog": "搜索弹窗 SearchDialog", "SwitchButton": "状态切换按钮 SwitchButton", "Spinner": "旋转图标 Spinner", "SweetAlert": "模态弹框 <PERSON><PERSON><PERSON><PERSON>", "Timer": "计时器 Timer", "Toast": "轻量弹窗 Toast", "FormsComponents": "表单组件", "AutoComplete": "自动完成 AutoComplete", "AutoFill": "自动填充 AutoFill", "Button": "按钮 But<PERSON>", "Block": "条件块 Block", "Cascader": "级联选择 Cascader", "Checkbox": "多选框 Checkbox", "CheckboxList": "多选框组 CheckboxList", "ColorPicker": "颜色拾取器 ColorPicker", "DateTimePicker": "时间框 DateTimePicker", "DateTimeRange": "时间范围框 DateTimeRange", "Editor": "富文本框 Editor", "EditorForm": "表单编辑框 EditorForm", "EyeDropper": "取色服务 EyeDropperService", "Input": "输入框 Input", "InputNumber": "数字框 InputNumber", "InputGroup": "输入组 InputGroup", "Ip": "IP 地址 IpAddress", "Markdown": "富文本框 Markdown", "CherryMarkdown": "腾讯文本框 Markdown", "MultiSelect": "多项选择器 MultiSelect", "Radio": "单选框 Radio", "Rate": "评分 Rate", "Select": "选择器 Select", "SelectObject": "任意选择器 SelectObject", "SelectTable": "表格选择器 SelectTable", "SelectTree": "树状选择器 SelectTree", "Slider": "滑块 Slider", "Switch": "开关 Switch", "Textarea": "多行文本框 Textarea", "Toggle": "开关 Toggle", "Transfer": "穿梭框 Transfer", "Upload": "上传组件 Upload", "ValidateForm": "验证表单 ValidateForm", "DataComponents": "数据组件", "Ajax": "数据传输 Ajax", "Avatar": "头像框 Avatar", "Badge": "徽章 Badge", "BarcodeReader": "条码扫描 BarcodeReader", "Card": "卡片 Card", "Calendar": "日历框 Calendar", "Camera": "摄像头 Camera", "Captcha": "验证码 Captcha", "Carousel": "走马灯 Carousel", "Circle": "进度环 Circle", "Collapse": "折叠 Collapse", "Display": "数据显示 Display", "CountUp": "计数器 CountUp", "DropdownWidget": "挂件 DropdownWidget", "GroupBox": "集合 GroupBox", "Handwritten": "手写组件 HandWritten", "LinkButton": "链接按钮 LinkButton", "ListView": "列表组件 ListView", "ListGroup": "列表框 ListGroup", "Locator": "位置定位 IpLocatorFactory", "Icon": "图标 Icon", "IFrame": "内嵌框架 IFrame", "ImageViewer": "图片 ImageViewer", "FileIcon": "文件图标 FileIcon", "Popover": "弹出窗 Popover", "Print": "打印按钮 Print", "QRCode": "二维码 QRCode", "Repeater": "重复组件 Repeater", "Search": "搜索框 Search", "Tag": "标签 Tag", "Timeline": "时间线 Timeline", "Title": "网站标题 TitleService", "Download": "文件下载 Download", "Tooltip": "工具条 Tooltip", "Reconnector": "重连组件 Reconnector", "Tree": "树形组件 Tree", "TreeView": "树形组件 TreeView", "TableComponents": "表格组件", "Table": "表格组件", "TableBase": "基本功能", "TableRow": "行设置", "TableColumn": "列设置", "TableColumnDrag": "列拖动", "TableColumnResizing": "列宽设置", "TableColumnList": "列显示/隐藏", "TableColumnAlign": "列对齐", "TableColumnCopy": "列复制", "TableColumnTemplate": "列模板", "TableCell": "单元格", "TableDetail": "明细行", "TableDynamic": "动态表格", "TableDynamicObject": "动态对象", "TableSearch": "搜索功能", "TableFilter": "筛选和排序", "TableFixHeader": "表头固定", "TableHeaderGroup": "表头分组", "TableFixColumn": "列固定", "TablePage": "分页功能", "TableToolbar": "工具栏", "TableEdit": "表单维护", "TableTracking": "跟踪模式", "TableDynamicExcel": "Excel-DataTable", "TableExcel": "Excel-Items", "TableExport": "导出功能", "TableSelection": "行选中", "TableAutoRefresh": "自动刷新", "TableFooter": "统计合并", "TableDialog": "弹窗联动", "TableWrap": "折行演示", "TableTree": "树形数据", "TableLoading": "数据加载", "TableLookup": "外键数据源", "TableVirtualization": "虚拟滚动", "TableAttribute": "特性标签", "Topology": "人机交互图 HMI", "MenuAccordion": "手风琴效果", "MenuExpandAll": "全部展开", "Empty": "空状态 Empty", "Charts": "图表 Chart", "ChartSummary": "简介", "ChartLine": "折线图 Line", "ChartBar": "柱状图 Bar", "ChartPie": "饼图 Pie", "ChartDoughnut": "圆环图 Doughnut", "ChartBubble": "气泡图 Bubble", "Transition": "过渡效果 Transition", "Geolocation": "地理定位组件 GeoLocationService", "Notification": "浏览器通知 NotificationService", "SignaturePad": "手写签名 SignaturePad", "Speech": "语音识别 Speech", "SpeechComponents": "语音组件", "SpeechIntro": "简介", "Recognizer": "语音识别 Recognizer", "Synthesizer": "语音合成 Synthesizer", "SpeechWave": "语音波形图 SpeechWave", "WebSpeech": "Web Speech Api", "OnScreenKeyboard": "屏幕键盘 OnScreenKeyboard", "RibbonTab": "选项卡菜单 RibbonTab", "PulseButton": "心跳按钮 PulseButton", "Bluetooth": "蓝牙服务 IBluetoothService", "PdfReader": "PDF阅读器 PDF Reader", "PdfViewer": "PDF阅读器 PDF Viewer", "VideoPlayer": "视频播放器 VideoPlayer", "FileViewer": "文件预览器 FileViewer", "FlipClock": "卡片翻转时钟 FlipClock", "DriverJs": "高亮向导组件 DriverJs", "BaiduOcr": "文字识别服务 IBaiduOcr", "AzureOpenAI": "AI 聊天服务 AzureOpenAI", "HtmlRenderer": "Html 转换器  HtmlRenderer", "Html2Image": "Html 转 Image IHtml2Image", "Html2Pdf": "Html 转 Pdf IHtml2Pdf", "Mask": "遮罩服务 MaskService", "ContextMenu": "右键菜单 ContextMenu", "ClockPicker": "时间选择器 ClockPicker", "TimePicker": "时间选择器 TimePicker", "DockViewComponents": "可停靠布局 DockView", "DockViewComponents2": "可停靠布局 DockViewV2", "DockViewIndex": "DockView 简介", "DockViewColumn": "DockView 列布局", "DockViewRow": "DockView 行布局", "DockViewStack": "DockView 堆栈布局", "DockViewNest": "DockView 嵌套布局", "DockViewComplex": "DockView 组合布局", "DockViewVisible": "DockView 可见性切换", "DockViewLock": "DockView 布局锁定", "DockViewLayout": "DockView 布局自定义", "DockViewTitle": "DockView 设置标题", "DockViewV2Index": "DockViewV2 简介", "DockViewV2Column": "DockViewV2 列布局", "DockViewV2Row": "DockViewV2 行布局", "DockViewV2Group": "DockViewV2 组布局", "DockViewV2Nest": "DockViewV2 嵌套布局", "DockViewV2Complex": "DockViewV2 组合布局", "DockViewV2Visible": "DockViewV2 可见性切换", "DockViewV2Lock": "DockViewV2 布局锁定", "DockViewV2Layout": "DockViewV2 布局自定义", "DockViewV2Title": "DockViewV2 设置标题", "OtherComponents": "其他组件", "MouseFollowerIntro": "鼠标跟随", "Live2DDisplayIntro": "Live2D 插件", "SlideButton": "快捷菜单 SlideButton", "DialButton": "拨号菜单 DialButton", "CountButton": "倒计时按钮 CountButton", "Splitting": "动画组件 Splitting", "QueryBuilder": "条件生成器 QueryBuilder", "WebSerial": "串口服务 ISerialService", "MindMap": "思维导图 MindMap", "Mermaid": "图表工具 Mermaid", "Marquee": "文字滚动 Marquee", "Stack": "堆叠布局 Stack", "Segmented": "分段控制器 Segmented", "Utility": "实用工具", "JSExtension": "JSRuntime 扩展", "Clipboard": "剪切板服务 Clipboard", "CodeEditor": "代码编辑器 CodeEditor", "Gantt": "甘特图 Gantt", "ImageCropper": "图像裁剪 ImageCropper", "Services": "内置服务", "AzureTranslator": "翻译服务 AzureTranslator", "BarcodeGenerator": "条码生成器 BarcodeGenerator", "ZipArchive": "压缩归档服务 IZipArchiveService", "BrowserFinger": "浏览器指纹 BrowserFingerService", "SvgEditor": "Svg编辑器 SvgEditor", "Waterfall": "瀑布流 Waterfall", "Holiday": "假日服务 ICalendarHoliday", "Festival": "节日服务 ICalendarFestival", "Lookup": "外键数据源服务 ILookupService", "DialogService": "弹窗服务 DialogService", "OnlineText": "在线统计 Online", "PrintService": "打印服务 PrintService", "ConnectionService": "在线连接服务 ConnectionService", "ExportPdfButton": "导出 Pdf 按钮 ExportPdfButton", "ThemeProvider": "主题服务 IThemeProvider", "IconPark": "字节跳动图标 IconPark", "IntersectionObserver": "交叉观察者 IntersectionObserver", "AntDesignIcon": "蚂蚁图标 AntDesignIcon", "Icons": "内置图标", "BootstrapIcon": "Bootstrap Icons", "MaterialIcon": "Material Icons", "FluentSystemIcon": "Fluent Icons", "ElementIcon": "饿了么图标 ElementIcon", "DrawerService": "抽屉服务 DrawerService", "SortableList": "拖拽组件 SortableList", "WinBox": "窗口 WinBox", "Player": "播放器 Player", "RDKit": "分子式组件 RDKit", "SmilesDrawer": "分子式组件 SmilesDrawer", "Affix": "固钉组件 Affix", "Watermark": "水印组件 Watermark", "OctIcon": "Oct Icons", "UniverIcon": "Univer Icons", "Typed": "打字机效果 Typed", "UniverSheet": "表格组件 UniverSheet", "ShieldBadge": "徽章组件 ShieldBadge", "OtpInput": "验证码输入框 OtpInput", "TotpService": "时间密码验证服务 ITotpService", "VideoDevice": "视频设备服务 IVideoDevice", "AudioDevice": "音频设备服务 IAudioDevice", "FullScreenButton": "全屏按钮 FullScreenButton", "Meet": "视频会议组件 Meet", "InputUpload": "上传组件 InputUpload", "ButtonUpload": "按钮上传组件 ButtonUpload", "AvatarUpload": "头像上传组件 AvatarUpload", "CardUpload": "卡片上传组件 CardUpload", "DropUpload": "拖动上传组件 DropUpload", "Vditor": "富文本框 Vditor Markdown", "TcpSocketFactory": "套接字服务 ITcpSocketFactory", "OfficeViewer": "Office 文档预览组件", "SocketComponents": "Socket 服务", "SocketAutoReceive": "自动接收数据", "SocketManualReceive": "手动接收数据", "DataPackageAdapter": "数据适配器", "SocketAutoConnect": "自动重连", "SocketDataEntity": "通讯数据转实体类", "NetworkMonitor": "网络状态 NetworkMonitor"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesHeader": {"TablesHeaderTitle": "表头分组功能", "TablesHeaderDescription": "数据结构比较复杂的时候，可使用多级表头来展现数据的层次关系", "TablesHeaderNormalTitle": "基础用法", "TablesHeaderNormalIntro": "设置 <code>MultiHeaderTemplate</code> 模板即可", "TablesHeaderNormalTips1": "通过设置 <code>ShowMultiFilterHeader</code> 值，来控制是否显示过滤行头", "TablesHeaderNormalTips2": "注意细节，首列单元格合并后导致最后一行表头第一列无单元格，所以需要自行设置样式 <code>border-bottom</code> 与其他单元格一致", "TablesHeaderNormal_Time": "时间", "TablesHeaderNormal_Info": "个人信息", "TablesHeaderNormal_Name": "个人姓名", "TablesHeaderNormal_Address": "个人地址"}, "BootstrapBlazor.Server.Components.Samples.SelectTrees": {"SelectTreesTitle": "树状选择器 SelectTree", "SelectTreesDescription": "下拉框内呈现树状数据结构，供选择", "SelectTreesNormalTitle": "普通用法", "SelectTreesNormalIntro": "选中节点", "SelectTreesDisableTitle": "禁用", "SelectTreesDisableIntro": "设置 <code>IsDisabled</code> 使组件禁用", "SelectTreesBindingTitle": "双向绑定", "SelectTreesBindingIntro": "绑定组件内变量，数据自动同步", "SelectTreesClientValidationTitle": "客户端验证", "SelectTreesClientValidationIntro": "组件内置 <code>ValidateForm</code> 可设置验证规则", "SelectTreesEditTitle": "可输入", "SelectTreesEditIntro": "通过设置 <code>IsEditable=\"true\"</code> 可设置下拉框选择后文本框可输入", "SelectTreesIsPopoverTitle": "悬浮弹窗", "SelectTreesIsPopoverIntro": "通过设置 <code>IsPopover</code> 参数，组件使用 <code>popover</code> 渲染 <code>UI</code> 防止由于父容器设置 <code>overflow: hidden;</code> 使弹窗无法显示问题", "SelectTreesClientValidationButtonText": "提交"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesDetailRow": {"TablesDetailRowTitle": "显示明细行功能", "TablesDetailRowDesc": "用于展示父子关系表数据", "DetailRowTemplateTitle": "简单应用", "DetailRowTemplateIntro": "通过设置 <code>DetailRowTemplate</code> 模板设置明细行内容", "DetailRowTemplateP": "明细行内显示绑定行的另外一个字段 <code>学历</code> 以普通文字形式呈现。支持双击展开明细行的功能，双击回调仅 <b>PC</b> 端支持，请在<b>PC</b> 端测试。", "DetailRowTemplateP1": "通过 <code>IsDetails</code> 参数可以实现动态切换是否显示明细行功能", "DetailRowTemplate2Title": "嵌套 Table 组件应用", "DetailRowTemplate2Intro": "通过设置 <code>DetailRowTemplate</code> 模板设置明细行为子表数据", "DetailRowTemplate2P": "明细行内嵌套另外一个 <code>Table</code> 组件，由于每行都要关联子表数据，出于性能的考虑，此功能采用 <code>懒加载</code> 模式，即点击展开按钮后，再对嵌套 <code>Table</code> 进行数据填充，通过 <code>ShowDetailRow</code> 回调委托可以控制每一行是否显示明细行，本例中通过 <code>Complete</code> 属性来控制是否显示明细行，可通过翻页来测试本功能", "HeightTitle": "固定表头开启明细行功能", "HeightIntro": "通过设置 <code>Height</code> 固定表头后模板设置明细行内容", "HeightP": "本例中固定表头后，再开启明细行功能", "DetailRowTemplate3Title": "明细行中使用 Tab 组件", "DetailRowTemplate3Intro": "通过设置 <code>DetailRowTemplate</code> 模板设置明细行内容", "DetailRowTemplate3P": "本例中明细行内使用 <code>Tab</code> 组件再次将数据分割成两个 <code>TabItem</code> 内容，进行再次数据拆分演示", "DynamicTitle": "动态数据明细行", "DynamicIntro": "数据源为 <code>DataTable</code>", "EducationText": "学历:", "DetailTextTrue": "关闭明细行", "DetailTextFalse": "开启明细行", "TabItemText": "关联数据", "DetailRowTemplateIsAccordion": "手风琴效果"}, "BootstrapBlazor.Server.Components.Samples.Table.Tables": {"ButtonAddColumnText": "增加列", "ButtonRemoveColumnText": "移除列", "TableBaseTitle": "Table 表格", "TableBaseDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "TableBaseExplain1": "<code>Table</code> 组件已经支持移动端适配，当屏幕小于 <code>RenderModeResponsiveWidth</code> 设定值时，组件渲染成卡片式方便查看数据，其默认值为 <code>768</code>", "TableBaseExplain2": "<code>Table</code> 组件有一个 <code>RenderMode</code> 属性，其默认值为 <code>Auto</code> 其他值定义如下", "TableBaseTips1": "<code>Auto</code>: 当屏幕小于 768px 时使用 <code>CardView</code> 模式，否则使用 <code>Table</code> 模式", "TableBaseTips2": "<code>Table</code>: 表格渲染模式，使用 <code>table</code> 元素进行数据渲染，适合宽屏幕下查看数据", "TableBaseTips3": "<code>CardView</code>：卡片式渲染模式，使用 <code>div</code> 元素进行数据渲染，适合窄屏幕下查看数据", "TableBaseTips4": "<code>TableColumn</code> 必须使用 <code>@bind-Field</code> 绑定了模型属性，模型属性为复杂类型时，必须初始化此属性；想要绑定只读属性时，先绑定其他可写属性后利用模板显示只读属性", "TableBaseNormalTitle": "基础表格", "TableBaseNormalIntro": "基础的表格展示用法。", "TableBaseNormalDescription": "点击按钮时更新数据源 <code>Items</code> 组件 <code>Table</code> 显示数据自动更新", "TableBaseStripedTitle": "带斑马纹表格", "TableBaseStripedIntro": "使用带斑马纹的表格，可以更容易区分出不同行的数据。设置 <code>IsStriped=true</code> 即可", "TableBaseBorderedTitle": "带边框表格", "TableBaseBorderedIntro": "通过设置 <code>IsBordered</code> 属性，增加表格表框效果", "TableBaseSizeTitle": "紧凑型表格", "TableBaseSizeIntro": "通过设置 <code>TableSize</code> 属性，设定表格内间隙变小适合大数据展示", "TableBaseSizeDescription": "<code>TableSize</code> 为表格大小枚举类型，默认值为 <code>Normal</code>，紧奏型值为 <code>Compact</code>", "TableBaseHeaderStyleTitle": "表头样式", "TableBaseHeaderStyleIntro": "通过设置 <code>HeaderStyle</code> 属性", "TableBaseHeaderStyleDescription": "<code>HeaderStyle</code> 为表格表头样式，默认值为 <code>None</code>", "AttributeTitle": "Table 属性", "TableSizeAttr": "表格大小", "HeaderStyleAttr": "表格 Header 样式", "HeaderTextWrap": "表头是否折行", "HeightAttr": "固定表头", "PageItemsAttr": "IsPagination=true 设置每页显示数据数量", "AutoRefreshIntervalAttr": "自动刷新时间间隔", "ExtendButtonColumnWidthAttr": "行操作按钮列宽度", "RenderModeResponsiveWidthAttr": "组件布局模式自动切换阈值", "IndentSizeAttr": "树状数据缩进宽度（像素px）", "ItemsAttr": "数据集合", "PageItemsSourceAttr": "IsPagination=true 设置每页显示数据数量的外部数据源", "EditModeAttr": "设置编辑行数据模式", "MultiHeaderTemplateAttr": "表头分组模板", "TableFooterAttr": "Table Footer 模板", "TableToolbarTemplateAttr": "自定义按钮模板", "EditTemplateAttr": "编辑弹窗模板", "ExportButtonDropdownTemplateAttr": "导出按钮下拉框模板", "ShowAdvancedSearchAttr": "是否显示高级搜索", "SearchTemplateAttr": "高级搜索模板", "BeforeRowButtonTemplateAttr": "Table 行按钮模板 放置到按钮前", "RowButtonTemplateAttr": "Table 行按钮模板 默认放置到按钮后", "DetailRowTemplateAttr": "Table 明细行模板", "IsAutoCollapsedToolbarButtonAttr": "小屏时是否自动收缩工具栏按钮", "IsBorderedAttr": "边框", "IsPaginationAttr": "显示分页", "IsStripedAttr": "斑马纹", "IsRenderedAttr": "组件是否渲染完毕", "IsMultipleSelectAttr": "是否为多选模式，为 true 时第一列自动为复选框列", "IsAutoRefreshAttr": "是否自动刷新表格", "IsTreeAttr": "是否为树形数据", "IsDetailsAttr": "是否为明细行表格，未设置时使用 DetailRowTemplate 进行逻辑判断", "IsHideFooterWhenNoDataAttr": "无数据时是否显示 Footer", "IsKeepSelectedRowsAttr": "翻页后是否保持选中行数据", "IsKeepSelectedRowAfterAddAttr": "新建数据后是否保持选中行", "ClickToSelectAttr": "点击行即选中本行", "ShowCheckboxTextAttr": "显示文字的选择列", "ShowFooterAttr": "是否显示表脚", "ShowFilterHeaderAttr": "是否显示过滤行", "ShowMultiFilterHeaderAttr": "是否显示多级表头的过滤行", "ShowSearchAttr": "显示搜索栏", "ShowSearchTextAttr": "显示搜索文本框", "ShowSearchTextTooltipAttr": "是否显示搜索框文本提示栏", "ShowResetButtonAttr": "显示清空搜索按钮", "ShowSearchButtonAttr": "显示搜索按钮", "SearchModeAttr": "搜索栏渲染方式", "CollapsedTopSearchAttr": "是否收缩顶部搜索框", "ShowToolbarAttr": "显示 Too<PERSON>bar", "ShowLineNoAttr": "显示 行号", "ShowDefaultButtonsAttr": "显示默认按钮 增加编辑删除", "ShowAddButtonAttr": "显示增加按钮", "ShowEditButtonAttr": "显示编辑按钮", "ShowEditButtonCallbackAttr": "显示行内编辑按钮，未设置时使用 ShowEditButton 值", "ShowDeleteButtonAttr": "显示删除按钮", "ShowDeleteButtonCallbackAttr": "显示行内删除按钮未设置时使用 ShowEditButton 值", "ShowExtendButtonsAttr": "显示行操作按钮", "ShowExtendEditButtonAttr": "是否显示行编辑操作按钮", "ShowExtendEditButtonCallbackAttr": "是否显示行内编辑操作按钮回调方法", "ShowExtendDeleteButtonAttr": "是否显示行删除操作按钮", "ShowExtendDeleteButtonCallbackAttr": "是否显示行内删除操作按钮回调方法", "DisableExtendEditButtonAttr": "是否禁用行内编辑操作按钮", "DisableExtendEditButtonCallbackAttr": "是否禁用行内编辑操作按钮回调方法", "DisableExtendDeleteButtonAttr": "是否禁用行内删除操作按钮", "DisableExtendDeleteButtonCallbackAttr": "是否禁用行内删除操作按钮回调方法", "ShowUnsetGroupItemsOnTopAttr": "未分组编辑项是否在开头渲染", "ShowSkeletonAttr": "首次加载时是否显示骨架屏", "ShowLoadingInFirstRenderAttr": "首次加载时是否显示加载动画", "ShowColumnListAttr": "是否显示列显示/隐藏控制按钮", "OnColumnVisibleChangedAttr": "改变列是否显示时触发此回调", "ShowEmptyAttr": "是否显示无数据提示", "ShowToastAfterSaveOrDeleteModelAttr": "保存/删除失败后是否显示 Toast 提示框", "ShowToastBeforeExport": "导出数据前是否弹出 Toast 提示框", "ShowToastAfterExport": "导出数据后是否弹出 Toast 提示框", "BeforeExportCallback": "导出数据前回调方法", "AfterExportCallback": "导出数据后回调方法", "TreeIconAttr": "树形数据行小箭头", "ScrollingDialogContentAttr": "编辑弹窗框是否为内部出现滚动条", "FixedExtendButtonsColumnAttr": "是否固定扩展按钮列", "OnQueryAsyncAttr": "异步查询回调方法", "OnAddAsyncAttr": "新建按钮回调方法", "OnColumnCreatingAttr": "列创建时回调委托方法", "ColumnOrderCallbackAttr": "列排序回调委托方法", "OnDoubleClickCellCallbackAttr": "设置单元格双击事件", "OnDeleteAsyncAttr": "删除按钮异步回调方法", "OnEditAsyncAttr": "编辑按钮异步回调方法", "OnSaveAsyncAttr": "保存按钮异步回调方法", "OnResetSearchAsyncAttr": "重置搜索按钮异步回调方法", "OnClickRowCallbackAttr": "点击行回调委托方法", "OnAfterSaveAsyncAttr": "保存数据后异步回调方法", "OnAfterDeleteAsyncAttr": "删除数据后异步回调方法", "OnAfterModifyAsyncAttr": "保存或者删除数据后异步回调方法", "OnAfterRenderCallbackAttr": "表格渲染完毕后回调方法", "OnTreeExpandAttr": "树形数据节点展开式回调委托方法", "OnDoubleClickRowCallbackAttr": "双击行回调委托方法", "SortIconAttr": "排序默认图标", "SortIconAscAttr": "排序升序图标", "SortIconDescAttr": "排序降序图标", "EditDialogSaveButtonTextAttr": "编辑弹窗中保存按钮文字", "EditDialogIsDraggableAttr": "编辑弹窗是否可拖拽", "EditDialogShowMaximizeButtonAttr": "编辑弹窗是否显示最大化按钮", "EditDialogSizeAttr": "编辑弹窗大小", "SearchDialogIsDraggableAttr": "搜索弹窗是否可拖拽", "SearchDialogShowMaximizeButtonAttr": "搜索弹窗是否显示最大化按钮", "SearchDialogSizeAttr": "搜索弹窗大小", "AddModalTitleAttr": "新建数据弹窗 Title", "EditModalTitleAttr": "编辑数据弹窗 Title", "UnsetTextAttr": "未设置排序时 tooltip 显示文字", "UnsetTextValue": "点击升序", "SortAscTextAttr": "升序排序时 tooltip 显示文字", "SortAscTextValue": "点击降序", "SortDescTextAttr": "降序排序时 tooltip 显示文字", "SortDescTextValue": "取消排序", "RenderModeAttr": "Table 组件布局模式设置", "EmptyTextAttr": "EmptyText", "EmptyImageAttr": "无数据时显示图片链接地址", "EmptyTemplateAttr": "无数据时显示模板", "EditDialogItemsPerRowAttr": "每行显示组件数量", "EditDialogRowTypeAttr": "设置组件布局方式", "EditDialogLabelAlignAttr": "Inline 布局模式下标签对齐方式", "AddAsyncMethod": "手工添加数据方法", "EditAsyncMethod": "手工编辑数据方法", "QueryAsyncMethod": "手工查询数据方法", "TextWrapAttr": "是否允许换行", "AutoGenerateColumnsAttr": "是否自动生成列", "CssClassAttr": "自定义单元格样式", "EditableAttr": "是否生成编辑组件", "EditTemplateColumnAttr": "模板", "FilterableAttr": "是否可过滤数据", "FilterTemplateAttr": "过滤模板", "FilterAttr": "列过滤器", "HeaderTemplateAttr": "表头模板", "IsReadonlyWhenAddAttr": "新建时此列只读", "IsReadonlyWhenEditAttr": "编辑时此列只读", "LookupAttr": "字典数据源", "LookupStringComparisonAttr": "字典数据源比较规则", "LookupServiceKeyAttr": "LookupService 服务获取 Lookup 数据集合键值", "LookupServiceDataAttr": "LookupService 服务获取 Lookup 数据集合自定义数据", "ReadonlyAttr": "编辑时是否只读模式", "SearchTemplateColumnAttr": "模板", "ShowTipsAttr": "显示单元格 Tooltips", "SearchableAttr": "是否参与搜索", "SortableAttr": "是否排序", "DefaultSortAttr": "是否为默认排序列", "DefaultSortOrderAttr": "默认排序规则", "ShowAdvancedSortAttr": "是否显示高级排序按钮", "TextAttr": "表头显示文字", "TextEllipsisAttr": "是否文本超出时省略", "TemplateAttr": "模板", "VisibleAttr": "是否显示此列", "IsVisibleWhenAddAttr": "新建时此列是否显示", "IsVisibleWhenEditAttr": "编辑时此列是否显示", "WidthAttr": "列宽度（像素px）", "FixedAttr": "是否固定本列", "GroupNameAttr": "当前属性分组", "GroupOrderAttr": "当前属性分组顺序", "ShownWithBreakPointAttr": "显示节点阈值", "FormatStringAttr": "格式化字符串", "FormatterAttr": "列格式化回调委托", "AlignAttr": "文字对齐方式", "OrderAttr": "顺序号", "OnCellRenderAttr": "单元格回调方法", "IsMarkupStringAttr": "是否为 MarkupString", "ShowGotoNavigatorAttr": "是否显示 Goto 跳转导航", "GotoTemplateAttr": "Goto 导航自定义组件", "GotoNavigatorLabelTextAttr": "Goto 导航标签文字", "ShowPageInfoAttr": "是否显示分页详细信息", "PageInfoTemplateAttr": "分页详细信息自定义组件", "PageInfoTextAttr": "分页信息显示文字", "AllowDragOrderAttr": "是否允许拖动列标题调整表格列顺序", "AutoScrollLastSelectedRowToViewAttr": "选中行最后一个是否自动滚动到可视视窗内", "TableBaseNormalRefreshText": "刷新", "TableBaseHeaderStyleMode": "表头模式", "IsPopoverToolbarDropdownButtonAttr": "工具栏下拉框按钮是否浮动"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesColumn": {"TablesColumnTitle": "Table 表格", "TablesColumnDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "ColumnTextTitle": "自定义列名", "ColumnTextIntro": "通过设置 <code>Text</code> 增加列头显示名称", "ColumnTextDesc": "表格组件 <code>TableColumns</code> 模板中的字段采用的是根据绑定模型的 <code>DisplayName</code> 标签值来自动显示的，如果要自定义显示名称请设置 <code>Text</code> 属性", "SelectTitle": "带选择列表格", "SelectIntro": "通过设置 <code>IsMultipleSelect=\"true\"</code> 增加表格第一列为选择列，设置 <code>ShowRowCheckboxCallback</code> 属性确定行是否显示选择框，本例中通过 <code>Complete</code> 属性来控制是否显示行的选择框", "ShowCheckboxTitle": "带显示文字的选择列表格", "ShowCheckboxIntro": "通过设置 <code>ShowCheckboxText=true</code> 表格第一列显示文字为 <b>选择</b>", "DisabledTitle": "选择框列", "DisabledIntro": "<code>RowTemplate</code> 内部组件 <code>TableCell</code> 设置 <code>Checkbox</code> 并设置相关数据绑定即可，本示例中通过数据绑定将选择框组件与值进行绑定", "FormatterTitle": "自定义列数据格式", "FormatterIntro": "列绑定时通过指定 <code>FormatString</code> 或者 <code>Formatter</code> 回调委托来实现单元格数值格式化", "FormatterP1": "本例中列 <code>DateTime</code> 值根据 <code>FormatString</code> 将值格式化为 <code>yyyy-MM-dd</code> 年月日格式", "FormatterP2": "本例中列 <code>Count</code> 值根据 <code>Formatter</code> 将值格式化为 <code>0.00</code> 保留两位小数格式", "AlignTitle": "列数据对齐方式", "AlignIntro": "列绑定时通过指定 <code>Align</code> 属性设置对齐方式", "AlignP1": "本例中列 <code>DateTime</code> 列设置为居中对齐 <code>Alignment.Center</code>", "AlignP2": "本例中列 <code>Count</code> 列设置为右侧对齐 <code>Alignment.Right</code>", "ShowCopyColumnTitle": "允许拷贝列数据", "ShowCopyColumnIntro": "通过设置 <code>ShowCopyColumn</code> 设置表格列允许拷贝整列数据", "ShowCopyColumnDesc": "可以通过设置 <code>ShowCopyColumnTooltip</code> <code>CopyColumnTooltipText</code> <code>CopyColumnCopiedTooltipText</code> 等设置微调拷贝列数据图标的 <code>Tooltip</code> 相关参数", "ShowColumnToolboxTitle": "列工具栏", "ShowColumnToolboxIntro": "通过设置列 <code>ToolboxTemplate</code> 参数，开启列工具栏按钮", "BindComplexObjectTitle": "绑定复杂类型和表达式", "BindComplexObjectIntro": "当绑定复杂类型时，无论绑定的对象集合是否为空，都要求 <code>TItem</code> 提供无参构造函数，否则要求通过 <code>CreateItemCallback</code> 提供构造回调；当绑定复杂表达式时，要求绑定时该表达式不得引发 <code>NullReferenceException</code> 异常，如果业务逻辑无法避免此问题，建议使用 <code>Template</code> 进行处理", "BindComplexObjectP1": "本例中，复杂类型 <code>ComplexFoo</code> 不具备无参构造函数，需通过 <code>CreateItemCallback</code> 提供 <code>ComplexFoo</code> 的构造回调", "BindComplexObjectP2": "本例中，我们希望将复杂表达式 <code>context.Company.Name</code> 绑定到列 <code>CompanyName</code> 上，但是业务逻辑无法确保绑定时属性 <code>Company</code> 的值不为 <code>null</code> 。因此可以先绑定简单表达式，再通过列模板 <code>Template</code> 进行处理", "BindComplexObjectButtonText": "设置公司", "ComplexFooDateTime": "日期", "ComplexFooName": "姓名", "ComplexFooAddress": "地址", "ComplexFooAge": "年龄", "ComplexFooCompany": "公司", "OnColumnCreatingTitle": "设置当前列属性", "OnColumnCreatingIntro": "通过指定 <code>OnColumnCreating</code>回调，对列集合进行数据二次更改", "OnColumnCreatingP1": "通过 <code>OnColumnCreating</code> 回调方法中的参数既可以对现有列进行扩展：", "OnColumnCreatingP2": "也可以根据自己的业务逻辑实现一些特殊功能；本例中通过回调函数对 <code>Name</code> 列进行了 <code>只读</code> 设置", "OnColumnCreatingLi1": "根据业务逻辑移除一些列的显示", "OnColumnCreatingLi2": "根据业务逻辑对特定列进行组件渲染，对 <code>ComponentType</code> <code>ComponentParameters</code> 进行赋值操作", "AdvanceTitle": "实战示例", "AdvanceIntro": "通过显示模板 <code>Template</code> 对各列进行自定义渲染", "CustomColText1": "自定义列名1", "CustomColText2": "自定义列名2", "CustomColText3": "自定义列名3", "ColumnIgnoreTitle": "列忽略", "ColumnIgnoreIntro": "通过设置 <code>Ignore</code> 参数控制列是否渲染，此参数与 <code>Visible</code> 不同，设置 <code>Visible=\"false\"</code> 后不显示列可通过 <code>ShowColumnList=\"true\"</code> 在列表中勾选并显示", "ColumnIgnoreButtonText": "忽略/不忽略", "ColumnOrderTitle": "列顺序", "ColumnOrderIntro": "通过设置 <code>TableColumn</code> 的 <code>Order</code> 参数进行设置顺序"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesColumnDrag": {"TablesColumnTitle": "Table 表格", "TablesColumnDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "AllowDragOrderTitle": "允许拖动列标题调整表格列顺序", "AllowDragOrderIntro": "通过指定 <code>AllowDragColumn</code> 设置表格列允许拖动列标题调整表格列顺序", "AllowDragOrderDesc": "<p>在列标题上按下鼠标拖动到其他列标题位置可将该列调整至目标列之前，但 <code>Table</code> 组件内置的列如明细行列、行号列、选择列、操作列等不可被调整</p><p>本示例通过设置 <code>ClientTableName</code> 参数开启了本地化存储，拖动调整顺序后，可刷新页面，列顺序是保持上次状态的</p><p>通过设置 <code>ColumnOrderCallback</code> 回调方法可以实现服务器端列顺序持久化</p>"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesColumnResizing": {"TablesColumnTitle": "Table 表格", "TablesColumnDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "WidthTitle": "自定义各列宽度", "WidthIntro": "通过设置 <code>TableColumn</code> <code>Width</code> 属性，来控制列宽度，行内按钮操作列宽度由 <code>ExtendButtonColumnWidth</code> 属性控制", "WidthP1": "本例中继续上一个例子，实现了自定义四个功能按钮，并且扩展到行内，点击各个按钮时均有相对应的回调委托方法，<code>TableToolbarButton</code> 采用的是 <code>Delegate</code> 方式完成数据交换，点击工具栏按钮时设置 <code>OnClick</code> 委托方法即可获取表内选中的行数据集合", "WidthP2": "<code>RowButtonTemplate</code> 按钮模板默认将自定义按钮放置到内置按钮后面，如需要自定义按钮放置到内置按钮前请使用 <code>BeforeRowButtonTemplate</code> 模板", "WidthP3": "通过设置按钮 <code>IsShow</code> 参数来控制是否显示按钮", "AllowResizingTitle": "允许列调整", "AllowResizingIntro": "通过指定 <code>AllowResizing</code> 设置表格列允许调整宽度", "AllowResizingDesc": "<b>注意：</b> <code>Table</code> 父容器有有效宽度值时 <code>Table</code> 才会出现滚动条，可通过设置 <code>ClientTableName</code> 参数开启本地化存储列宽功能，即通过拖拽后列宽下次打开时会保持，通过设置 <code>ShowColumnWidthTooltip</code> 参数控制是否显示列宽提示栏，默认 <code>false</code>", "WidthButtonText1": "明细", "WidthButtonText2": "编辑", "WidthButtonText3": "权限", "WidthButtonText4": "审批", "WidthConfirmButtonText": "确认", "CustomerButtonTitle": "自定义按钮处理方法", "CustomerButtonContent": "通过不同的函数区分按钮处理逻辑，参数 Items 为 Table 组件中选中的行数据集合，当前选择数据 {0} 条", "OnRowButtonClickContent": "通过不同的函数区分按钮处理逻辑，参数 Item 为当前行数据"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesColumnList": {"TablesColumnTitle": "Table 表格", "TablesColumnDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "ShownWithBreakPointTitle": "根据屏幕宽度自动显示/隐藏列", "ShownWithBreakPointIntro": "通过指定 <code>ShownWithBreakPoint</code> 属性设置在不同宽度下是否显示", "ShownWithBreakPointP1": "<code>ShownWithBreakPoint</code> 枚举值为：", "ShownWithBreakPointLi1": "<code>None</code> 未设置均显示", "ShownWithBreakPointLi2": "<code>Small</code> 屏幕大于等于 <code>576px</code> 时显示", "ShownWithBreakPointLi3": "<code>Medium</code> 屏幕大于等于 <code>768px</code> 时显示", "ShownWithBreakPointLi4": "<code>Large</code> 屏幕大于等于 <code>992px</code> 时显示", "ShownWithBreakPointLi5": "<code>ExtraLarge</code> 屏幕大于等于 <code>1200px</code> 时显示", "ShownWithBreakPointP2": "本例中列 <code>Count</code> 列设置为 <code>BreakPoint.Large</code> 即屏幕在大于 <code>992px</code> 时才显示", "ShownWithBreakPointP3": "注意：", "ShownWithBreakPointP4": "由于 <code>Table</code> 组件默认是支持移动端适配，所以小屏幕时采用的是卡片式模式，本例中显式设置使用 <code>RenderMode=\"TableRenderMode.Table\"</code> 模式", "VisibleTitle": "自定义显示/隐藏列", "VisibleIntro": "通过指定 <code>ShowColumnList</code> 属性设置列是否显示", "VisibleP1": "<code>ShowColumnList</code> 默认值为 false，显式指定为 true 后工具栏出现相应列调整按钮", "VisibleP2": "<code>TableColumn</code> 增加 <code>Visiable</code> 属性，其默认值为 true，显示设置为 false 时不显示此列", "VisibleP3": "此例中 <b>数量</b> 列通过设置 <code>Visible</code> 未显示，可以通过列控制按钮进行显示设置", "VisibleP4": "更改 <b>列</b> 状态后触发 <code>OnColumnVisibleChanged</code> 回调委托", "ResetVisibleColumnsButtonText": "设置列隐藏信息", "ResetVisibleColumnsDesc": "通过调用 <code>Table</code> 实例方法 <code>ResetVisibleColumns</code> 可设置任意列显示隐藏属性"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesColumnTemplate": {"TablesColumnTitle": "Table 表格", "TablesColumnDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "TableColumnTitle": "自定义列数据模板", "TableColumnIntro": "自定义 <code>TableColumn</code> 列的 <code>Template</code> 模板来实现任意显示 UI，<code>EditTemplate</code> 可实现编辑模式下 UI", "TableColumnP1": " 本例中列 <code>DateTime</code> 值根据 <code>Complete</code> 值是否为 <code>true</code> 显示不同颜色", "TableColumnP2": "本例中列 <code>Complete</code> 根据 <code>Complete</code> 值自定义显示为 <code>Checkbox</code> 组件", "TableColumnP3": "<code>Template</code> 模板自带 <code>Context</code> 相关联上下文，其值为 <code>TableColumnContext</code> 类型", "TableColumnLi1": "<code>Value</code> 为当前绑定列的数据值", "TableColumnLi2": "<code>Row</code> 为当前绑定列所在行的数据值", "TemplateTitle": "自定义列模板", "TemplateIntro": "通过指定 <code>Template</code> 设置最后一列内显示为自定义按钮", "AutoGenerateColumnsTitle": "自动生成列", "AutoGenerateColumnsIntro": "通过指定 <code>AutoGenerateColumns</code> 属性值为 <code>true</code>，开启根据绑定模型自动生成列信息功能", "AutoGenerateColumnsP1": "本例中通过设置 <code>AutoGenerateColumns</code> 值为 <code>true</code> 开启自动生成列功能，默认绑定模型实体类所有属性全部生成，实体类可以通过 <code>AutoGenerateColumnAttribute</code> 标签类进行功能设置，如：", "AutoGenerateColumnsLi1": "<code>Ignore</code> 表示忽略此属性，即不生成", "AutoGenerateColumnsLi2": "<code>Readonly</code> 表示只读", "AutoGenerateColumnsLi3": "更多属性详见 <a href=\"{0}/blob/main/src/BootstrapBlazor/Attributes/AutoGenerateColumnAttribute.cs\" target=\"_blank\">源码</a>", "AutoGenerateColumnsP2": " 本例中通过 <code>[AutoGenerateColumn(Order = 1, FormatString = \"yyyy-MM-dd\")]</code> 标签格式化 <code>日期</code> 列；通过代码中设置模板列对 <code>Complete</code> 列进行自定义使用 <code>Switch</code> 组件进行渲染；通过 <code>[AutoGenerateColumn(Order = 10)]</code> 标签中的 <code>Order</code> 对显示顺序进行设定", "TableTemplateTips": "特别注意：在使用 <b>列模板</b> 时，实际的应用场景经常遇到几列或者多列需要联动的情况，此时直接将联动的组件放置到 <code>EditTemplate</code> 内时是无法完成联动的，正确的做法是将需要联动小组件独立成一个组件，在此组件内部完成联动"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesEdit": {"TablesEditTitle": "Table 表格", "TablesEditDescription": "常用于单表维护，通过属性配置实现简单的增、删、改、查、排序、过滤、搜索等常用功能，通过 <code>Template</code> 的高级用法能实现非常复杂的业务需求功能", "TablesEditItemsTitle": "使用集合作为数据源实现编辑功能", "TablesEditItemsIntro": "设置 <code>Items</code> 作为数据源，无需设置 <code>OnSaveAsync</code> <code>OnDeleteAsync</code> 回调委托使用内置处理逻辑进行更新与删除功能", "TablesEditItemsDescription": "设置 <code>Items</code> 作为数据源，必须使用双向绑定 <code>@bind-Items</code>, 同时 <code>TItem</code> 泛型约束的类实例，如本例中的 <code>Foo</code> ，需要正确配置 <code>[Key]</code> 标签，否则内置编辑功能无法正常工作", "TablesEditTemplateTitle": "具有单表维护功能的表格", "TablesEditTemplateIntro": "通过设置 <code>EditTemplate</code> 自定义编辑弹窗，如果属性需要联动时必须像本例这样封装成一个独立的组件再放置到模板中", "TablesEditTemplateDescription": "本例中设置 <code>Count</code> 右侧对齐，<code>Complete</code> 列设置为居中对齐，布尔类型列自动渲染成 <code>Switch</code> 组件，点击 <code>学历</code> 下拉框时右侧只读组件描述信息联动更新，<b>此功能必须封装成独立组件</b>", "TablesEditTemplateDisplayLabel": "详细信息", "TablesEditTemplateDisplayDetail1": "小学六年", "TablesEditTemplateDisplayDetail2": "初中三年", "TablesEditOnAddAsyncTitle": "自动生成单表维护功能的表格", "TablesEditOnAddAsyncIntro": "当设置了 <code>OnAddAsync</code> 或者 <code>OnSaveAsync</code> 回调委托方法时，如果未设置 <code>EditTemplate</code> 编辑模板时，组件会尝试自动生成表单维护 UI", "TablesEditOnAddAsyncDescription": "通过设置 <code>TItem</code> 泛型约束的类实例 <code>Foo</code> 属性的 <code>[Required]</code> 等验证标签即可实现客户端验证", "TablesEditOnAddAsyncTips1": "数据绑定类型为可为空类型时自动允许为空，如日期绑定列为 <code>DateTime?</code> 类型", "TablesEditOnAddAsyncTips2": "数据绑定类型为数值类型时如，如数量绑定列为 <code>int</code> 类型，自动进行数值验证", "TablesEditOnAddAsyncTips3": "表格呈现的有些数据列是计算得到的结果，此种类型的列是无法参与编辑的，通过设置 <code>Ignore=true</code> 自动生成编辑 UI 时就不会生成此列编辑组件，如本示例中 <code>Count</code> 列在编辑弹窗中是不出现的", "TablesEditOnAddAsyncTips4": "通过设置 <code>Readonly=true</code> 自动生成编辑 UI 会将此字段进行只读处理，新建时请对 <code>Model</code> 进行默认值赋值", "TablesEditOnAddAsyncTips5": "通过设置 <code>IsExtendButtonsInRowHeader=true</code> 使扩展按钮在行前面显示", "TablesEditOnAddAsyncTips6": "通过设置 <code>EditDialogDraggable='true'</code> 使编辑弹出框可拖拽", "TablesColumnEditTemplateTitle": "自定义列编辑模板", "TablesColumnEditTemplateIntro": "当设置列的 <code>EditTemplate</code> 时，组件自动生成表单维护 UI 时使用此模板作为呈现 UI", "TablesColumnEditTemplateDescription1": "通过设置姓名列的 <code>EditTemplate</code> 自定义编辑时使用下拉框来选择姓名", "TablesColumnEditTemplateTips": "本例中 <code>Name</code> 列为自定义组件 <code>TableNameDrop</code>，新建时默认为 <code>请选择 ...</code>；<code><PERSON>bby</code> 列为自定义组件 <code>DemoHobbyTemplate</code> 使用多选框组件进行渲染", "TablesEditModeTitle": "设置编辑模式", "TablesEditModeIntro": "通过设置表格的 <code>EditMode</code> 属性，设置组件是弹窗编辑行数据还是行内编辑数据", "TablesEditModeDescription": "<code>EditMode</code> 为枚举类型其值分别为：<code>Popup</code> <code>EditForm</code> <code>InCell</code> 其默认值为 <code>Popup</code> 弹窗编辑行数据", "TablesEditModeTips1": "本例中设置数据源 <code>Items</code> 为双向绑定，特别适用与父子表录入，保存时直接使用数据源即可", "TablesEditModeTips2": "<code>EditForm</code> 模式示例", "TablesEditInjectDataServiceTitle": "使用注入数据服务", "TablesEditInjectDataServiceIntro": "未提供数据操作回调方法时组件自动寻找注册的数据服务进行对数据的增删改查", "TablesEditDataServiceTitle": "使用自定义数据服务", "TablesEditDataServiceIntro": "通过设置表格的 <code>DataService</code> 属性，使用独立的数据服务进行对数据的增删改查", "TablesEditDataServiceDescription": "自定义数据服务", "TablesEditDataServiceTips1": "开启使用注入数据服务后，可通过设置 <code>DataServices</code> 参数对组件进行单独设置，如未设置内部使用注入服务提供的实例", "TablesEditDataServiceTips2": "本例中通过设置 <code>EditDialogShowMaximizeButton</code> 参数，使编辑弹窗中显示 <b>最大化</b> 按钮", "TablesEditFooterTemplateTitle": "自定义编辑弹窗按钮", "TablesEditFooterTemplateIntro": "通过设置 <code>EditFooterTemplate</code> 自定义编辑弹窗 <code>Footer</code>", "TablesEditFooterTemplateDescription": "点击表格内编辑按钮，弹出编辑弹窗，弹窗内 <code>Footer</code> 内按钮均为自定义按钮。为方便二开的同时保留原有 <b>关闭</b> 与 <b>保存</b> 两个按钮功能，额外内置提供了两个与之相对应的组件 <code>DialogCloseButton</code> <code>DialogSaveButton</code> 这两个按钮无需编写点击相关处理方法", "TablesEditModeAddModalTitle": "增加测试数据窗口", "TablesEditModeEditModalTitle": "编辑测试数据窗口", "TablesEditModeInCell": "<code>InCell</code> 模式示例", "TablesEditModeDrawer": "<code>Drawer</code> 模式示例", "TablesEditInjectDataServiceDescription": "通过注册数据服务进行增、删、改、查的数据库操作，而无需对以下回调委托进行赋值，优先级别为有回调方法优先调用回调方法，如无则调用注入服务进行数据操作", "TablesEditInjectDataServiceTips1": "Startup 文件注入数据服务", "TablesEditInjectDataServiceTips2": "实现原理与用法介绍", "TablesEditInjectDataServiceTips3": "自定义数据服务", "TablesEditInjectDataServiceTips4": "开启使用注入数据服务后，可通过设置 <code>DataServices</code> 参数对组件进行单独设置，如未设置内部使用注入服务提供的实例", "TablesEditShowSearchPlaceHolderString": "不可为空，50字以内", "TablesVisibleTitle": "Table 编辑时显示/隐藏", "TablesVisibleIntro": "<div><code>Visible</code> 默认值为 <code>true</code> 如果有列设置了 <code>IsVisibleWhenAdd</code> 或者 <code>IsVisibleWhenEdit</code> 属性为 <code>false</code> 时, 新建或者更新时隐藏此列。本例中 <b>新建</b> 弹窗不显示 <b>数量</b> <b>编辑</b> 弹窗不显示 <b>是否</b> 编辑项。</div><div>可以通过在数据模型中使用 <code>[AutoGenerateClass(Visible = false)]</code> 全部禁止显示，再通过 <code>IsVisibleWhenAdd</code> 或者 <code>IsVisibleWhenEdit</code> 单独设置编辑状态下的可见性，本例中，<b>地址</b> 列默认不可见，<b>新建、编辑</b> 弹窗内均可以编辑</div>", "TablesReadonlyColumnTitle": "只读列", "TablesReadonlyColumnIntro": "通过设置 <code>Field</code> 与 <code>FieldName</code> 即可", "TablesReadonlyColumnDescription": "对应数据库计算列，或者模型只读属性", "TablesReadonlyColumnTips1": "不要写 <code>@bind-Field</code> 只读列在 <b>新建</b> 或者 <b>编辑</b> 时均无法录入", "TablesReadonlyColumnTips2": "老版本需要写完整代码如下", "TablesTemplateColumnTitle": "模板列", "TablesTemplateColumnIntro": "通过使用 <code>TableTemplateColumn</code> 代替 <code>TableColumn</code> 组件，即可不绑定模型属性", "TablesTemplateColumnDescription": "通过设置 <code>Text</code> 属性设置标题显示文字，通过 <code>Template</code> 设置单元格显示内容，模板上下文为 <code>TableColumnContext</code> 实例，可通过 <code>@v.Row</code> 获得当前行模型实例，模板列不参与数据编辑功能如 <b>新建</b> <b>编辑</b>", "TableTemplateColumnText": "模板列"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesVirtualization": {"TablesVirtualizationTitle": "Table 虚拟滚动行", "TablesVirtualizationDescription": "Table 组件显示大数据时通常采用分页加载数据，还有一种虚拟行的技术类似手机滚动到底部时后台自动加载数据", "VirtualizationNormalTitle": "基础用法", "VirtualizationNormalIntro": "设置 <code>Items</code> 参数组件自动实现虚拟滚动", "VirtualizationNormalDescription": "<p>需要设置 <code>ScrollMode</code> <code>Height</code> <code>RowHeight</code> <code>PageItems</code> 参数对虚拟滚动进行设置</p><p>通过 <code>ShowFooter</code> 控制是否显示 <code>Footer</code>，通过 <code>IsFixedFooter</code> 控制 <code>Footer</code> 是否固定在未端</p>", "TablesFooterFixedLabel": "是否固定 Footer", "TablesFooterInfo": "合计：", "TablesFooterFixedText": "固定", "TablesFooterNotFixedText": "跟随", "VirtualizationDynamicTitle": "动态获取数据", "VirtualizationDynamicIntro": "设置 <code>OnQueryAsync</code> 回调通过参数 <code>StartIndex</code> <code>PageItems</code> 获取数据", "VirtualizationDynamicDescription": "快速滚动时显示行占位，提升用户体验"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesLoading": {"TablesLoadingTitle": "显示数据加载功能", "TablesLoadingDescription": "调用远端数据接口时，由于网络原因可能出现延时情况，可使用显示加载功能进行屏蔽", "TablesLoadingShowLoadingTitle": "基础用法", "TablesLoadingShowLoadingIntro": "设置 <code>ShowLoading</code> 即可", "TablesLoadingShowSkeletonTitle": "骨架屏", "TablesLoadingShowSkeletonIntro": "设置 <code>ShowSkeleton</code> 即可", "TablesLoadingShowLoadingInFirstRenderTitle": "关闭加载动画", "TablesLoadingShowLoadingInFirstRenderIntro": "设置 <code>ShowLoadingInFirstRender</code> 即可"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesTree": {"TablesTreeTitle": "Table 树形数据展示", "TablesTreeDescription": "表格支持树形数据的展示", "TablesTreeTip_title": "通过 <code>IsTree</code> 参数控制是否为树形数据", "TablesTreeTip_note1": "通过 <code>TreeNodeConverter</code> 对数据集进行树状结构转换", "TablesTreeTip_note2": "设置 <code>TableTreeNode</code> 其 <code>IsExpand</code> 参数控制当前子节点是否展开", "TablesTreeTip_note3": "点击子项展开小箭头时，通过 <code>OnTreeExpand</code> 回调委托方法获取子项数据", "TablesTreeTip_note4": "保持行状态回落机制，<code>ModelEqualityComparer</code> <code>CustomKeyAttribute</code> <code>IEqualityComparer&lt;TItem&gt;</code> <code>Equals</code> 重载方法", "TablesTreeStep1": "第一步：设置 <code>IsTree</code> 为 <code>true</code>", "TablesTreeStep2": "第二步：设置 <code>Items</code> 或者 <code>OnQueryAsync</code> 获得组件数据集合", "TablesTreeStep3": "第三步：设置 <code>TreeNodeConverter</code> 将组件数据集合转化为树状结构", "TablesTreeStep4": "第四步：设置 <code>OnTreeExpand</code> 回调委托响应行展开获取子项数据集合", "TablesTreeDataTitle": "树形数据展示", "TablesTreeDataIntro": "通过设置 <code>IsTree</code> 开启树形表格", "TablesTreeLevelTitle": "层次缩进", "TablesTreeLevelIntro": "通过设置 <code>IndentSize</code> 以控制每一层的缩进宽度。", "TablesTreeLevelTips1": "默认层次缩进宽度为 <code>16px</code> 通过设置 <code>Indent</code> 更改缩进宽度", "TablesTreeLevelTips2": "本例中更改缩进宽度为 <code>8px</code>", "TablesTreeEditTitle": "具有单表维护功能的树形数据", "TablesTreeEditIntro": "实现简单的增、删、改、查功能。", "TablesTreeIconTitle": "图标", "TablesTreeIconIntro": "通过设置 <code>TreeIcon</code> 更改指示小箭头图标", "TablesTreeIconTips1": "默认层次缩进宽度为 <code>16px</code> 通过设置 <code>Indent</code> 更改缩进宽度", "TablesTreeIconTips2": "本例中更改缩进宽度为 <code>8px</code>"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesWrap": {"TablesWrapTitle": "Table 换行示例", "TablesWrapDescription": "当表头或者行内单元格内容超长时，通过样式更改实现省略、换行等效果", "TablesWrapTip": "<b>注意：</b>由于 <code>Table</code> 组件已实现适配移动端功能，此演示需在电脑端查看", "TablesWrapNormalTitle": "表头超长示例", "TablesWrapNormalIntro": "在某种特殊情况下可能表头比较长，需要将表头控制在一个固定宽度内，通过设置 <code>ShowHeaderTooltip</code> 鼠标移动到表头上时显示 <code>Tooltip</code> 来显示完整表头信息", "TablesWrapNormalDescription": "可以拖动窗口大小，窗口过小时自动出现横向滚动条，通过设置 <code>Width</code> 固定宽度，通过设置 <code>HeaderTextEllipsis</code> 表头溢出截断", "TablesWrapHeaderTextWrapTitle": "表头超长折行", "TablesWrapHeaderTextWrapIntro": "通过设置 <code>HeaderTextWrap</code> 使超长表头折行", "TablesWrapHeaderTextWrapDescription": "可以拖动窗口大小，窗口过小时自动出现横向滚动条后，表头会自动折行", "TablesWrapLongDataTitle": "单元格数据超长折行示例", "TablesWrapLongDataIntro": "在某种特殊情况下可能单元格内容比较长，需要进行折行处理", "TablesWrapLongDataTips1": "可以拖动窗口大小，窗口过小时 <b>地址</b> 列自动进行折行处理", "TablesWrapLongDataTips2": "通过设置 <code>TextWrap</code> 来开启自动换行功能", "TablesWrapLongDataTips3": "<b>注意：</b>推荐使用 <code>Width</code> 对列宽度进行设置", "TablesWrapDataResizingTitle": "单元格数据超长省略示例", "TablesWrapDataResizingIntro": "在某种特殊情况下可能单元格内容比较长，需要进行省略处理", "TablesWrapDataResizingTips1": "可以拖动窗口大小，窗口过小时 <b>地址</b> 列自动进行省略处理", "TablesWrapDataResizingTips2": "通过设置 <code>TextEllipsis</code> 来开启文本超长省略功能", "TablesWrapDataResizingTips3": "<b>注意：</b>推荐使用 <code>Width</code> 对列宽度进行设置，如未设置列宽内部自动使用 200px 宽度", "TablesWrapDataResizingTips4": "单元格内文本被省略后，可以通过 <code>ShowTips</code> 属性来控制鼠标悬停是否显示全部文本，默认为 <code>false</code>，通过设置 <code>GetTooltipTextCallback</code> 回调方法可以自定义 <code>Tooltip</code> 显示内容", "TablesWrapDataResizingTips5": "拖动地址列，单元格显示内容自动增加与减少", "TablesWrapCustomCellTitle": "自定义单元格内排版", "TablesWrapCustomCellIntro": "使用模板对单元格内数据进行特殊布局", "TablesWrapNormalColumHeaderText_DateTime": "我是超级长的时间表头", "TablesWrapNormalColumHeaderText_Name": "我是超级长的姓名表头", "TablesWrapNormalColumHeaderText_Address": "我是超级长的地址表头", "TablesWrapHeaderTextWrapColumHeaderText_DateTime": "我是超级长的时间表头", "TablesWrapHeaderTextWrapColumHeaderText_Name": "我是超级长的姓名表头", "TablesWrapHeaderTextWrapColumHeaderText_Address": "我是超级长的地址表头", "TablesWrapLongDataColumHeaderText_DateTime": "时间", "TablesWrapLongDataColumHeaderText_Name": "姓名", "TablesWrapLongDataColumHeaderText_Address": "地址", "TablesWrapDataResizingColumHeaderText_DateTime": "时间", "TablesWrapDataResizingColumHeaderText_Name": "姓名", "TablesWrapDataResizingColumHeaderText_Address": "地址", "TablesWrapCustomCellColumHeaderText_DateTime": "时间", "TablesWrapCustomCellColumHeaderText_Name": "姓名", "TablesWrapCustomCellColumHeaderText_Address": "地址", "TablesWrapCustomCellTemplate_State": "状态", "TablesWrapCustomCellTemplate_Time": "时间"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesTracking": {"TablesTrackingTitle": "Table 表格", "TablesTrackingDescription": "适用于父子表一起提交的应用场景", "TablesTrackingNormalTitle": "跟踪模式", "TablesTrackingNormalIntro": "设置 <code>IsTracking</code> 配合 <code>Items</code> 可以不需要设置 <code>OnSaveAsync</code> 等回调方法", "TablesTrackingNormalTips1": "可通过设置 <code>ShowToastAfterSaveOrDeleteModel='false'</code> 关闭 <b>保存</b> <b>删除</b> 按钮的提示信息", "TablesTrackingNormalTips2": "此模式下所有数据编辑均编辑原始数据，通过 <code>bind-Items</code> 更新数据源，所以不提供 <b>取消</b> 按钮"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesFooter": {"TablesFooterTitle": "Table 统计功能", "TablesFooterDescription": "用于数据统计", "TablesFooterStatisticsTitle": "数据统计示例", "TablesFooterStatisticsIntro": "设置 <code>ShowFooter=true</code> 显示 <code>Footer</code> 自定义合计功能，通过设置 <code>IsFixedFooter</code> 控制是否固定 <code>Footer</code>", "TablesFooterStatisticsTips1": "<code>Table</code> 组件有 <code>TableFooter</code> 模板，其数据上下文为 <code>Table</code> 组件的数据集合 <code>IEnumerable&lt;TItem&gt;</code>", "TablesFooterStatisticsTips2": "<code>TableFooter</code> 模板中关联的上下文 <code>context</code> 值为当页数据集合", "TablesFooterStatisticsTips3": "<code>TableFooter</code> 模板内可以自定义单元格 <code>td</code> 内容，也可以使用内置的 <code>TableFooterCell</code> 组件进行数据显示", "TablesFooterTemplateTitle": "Footer 模板", "TablesFooterTemplateIntro": "设置 <code>FooterTemplate</code> 自定义表格底部显示内容", "TablesFooterTemplateDescription": "无数据时默认显示 <code>Footer</code> 可通过设置 <code>@nameof(Table<Foo>.IsHideFooterWhenNoData)</code> 参数隐藏 <code>Footer</code>", "TablesFooterStatisticsTotal": "合计：", "TablesFooterTemplateSentences": "这里可以写一些描述性的语句", "TablesFooterTemplateTotal": "合计："}, "BootstrapBlazor.Server.Components.Samples.Table.TablesAutoRefresh": {"TablesAutoRefreshTitle": "自动刷新表格功能", "TablesAutoRefreshDescription": "在某种应用场景中，数据源的变化需要重新刷新表格组件", "TablesAutoRefreshNormalTitle": "自动刷新", "TablesAutoRefreshNormalIntro": "本示例演示在后台线程中对数据源进行监控，当数据源变化时通知表格组件进行数据刷新", "TablesAutoRefreshNormalTips1": "通过设置 <code>IsAutoRefresh</code> 属性值来开启自动刷新功能，<code>AutoRefreshInterval</code> 属性值默认为 2000 毫秒，此值为自动刷新时间间隔，周期性调用组件的 <code>QueryAsync</code> 方法使表格具有自动刷新功能", "TablesAutoRefreshNormalTips2": "本例中每间隔 2 秒钟数据增加一条新数据并保持最多 10 条数据", "TablesAutoRefreshControlTitle": "通过设置变量控制是否自动更新", "TablesAutoRefreshControlIntro": "本示例通过设置变量控制是否自动更新", "TablesAutoRefreshControlDescription": "通过点击按钮开始/关闭是否自动更新功能", "TablesAutoRefreshControlToggleAuto": "更改 Auto", "TablesAutoRefreshControlIsAutoRefresh": "当前值"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesSelection": {"TablesSelectionTitle": "Table 表格", "TablesSelectionDescription": "通过设置 <code>SelectedRows</code> 设置表格行状态，通过选中样式可以设置高亮", "TablesSelectionNormalTitle": "设置选中行功能", "TablesSelectionNormalIntro": "通过设置 <code>SelectedRows</code> 属性集合初始化表格时默认为选中状态", "TablesSelectionNormalTips": "保持行状态回落机制，<code>ModelEqualityComparer</code> <code>CustomKeyAttribute</code> <code>IEqualityComparer&lt;TItem&gt;</code> <code>Equals</code> 重载方法", "TablesSelectionNormalSelectRow": "选中的行记录", "TablesSelectionNormalButtonText": "清除选择", "TablesSelectionKeepInfo": "保持选中行状态：", "TablesSelectionKeepOnText": "保持", "TablesSelectionKeepOffText": "不保持", "TablesSelectionCountText": "选中的行数：{0}", "TablesSelectionScrollTitle": "渲染结束后回调方法", "TablesSelectionScrollIntro": "通过设置 <code>OnAfterRenderCallback</code> 回调方法，执行一些特殊操作", "TablesSelectionScrollDescription": "点击按钮后选中最后一行，并且将选中行自动滚动到视窗可见区域内，本代码为示例，本功能可通过设置 <code>AutoScrollLastSelectedRowToView=\"true\"</code> 开启，通过 <code>AutoScrollVerticalAlign</code> 参数设置对齐方式", "TablesSelectionScrollButtonText": "选中最后一行"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesExcel": {"TablesExcelTitle": "Table 表格", "TablesExcelDescription": "常用于大数据单表维护", "TablesExcelOnQueryTitle": "绑定集合", "TablesExcelOnQueryIntro": "通过 <code>OnQueryAsync</code> 回调获得数据集合", "TablesExcelTips": "使用 <code>List&lt;TItem&gt;</code> 泛型集合作为数据源时，需要按照下面的步骤进行设置", "TablesExcelSetDataSourceTitle": "1. 设置数据源", "TablesExcelSetDataSourceDescription": "设置 <code>Table</code> 组件的 <code>Items</code> 属性或者 <code>OnQueryAsync</code> 回调委托方法", "TablesExcelNewLogicTitle": "2. 处理新建逻辑", "TablesExcelNewLogicDescription": "设置 <code>OnAddAsync</code> 回调委托函数处理 <b>新建</b> 逻辑", "TablesExcelNewLogicNote1": "此处代码为示例代码", "TablesExcelNewLogicNote1Address": "自定义地址", "TablesExcelNewLogicNote2": "输出日志信息", "TablesExcelNewLogicNote2Log1": "集合值变化通知 列", "TablesExcelNewLogicNote2Log2": "类型", "TablesExcelDeleteLogicTitle": "3. 处理删除逻辑", "TablesExcelDeleteLogicDescription": "设置 <code>OnDeleteAsync</code> 回调委托函数处理 <b>删除</b> 逻辑", "TablesExcelDeleteLogicNote1": "此处代码为示例代码", "TablesExcelDeleteLogicNote2": "输出日志信息", "TablesExcelDeleteLogicNote2Log1": "集合值变化通知 列", "TablesExcelDeleteLogicNote2Log2": "类型", "TablesExcelUpdateLogicTitle": "4. 处理更新逻辑", "TablesExcelUpdateLogicDescription1": "设置 <code>OnSaveAsync</code> 回调委托函数处理单元格 <b>更新</b> 逻辑", "TablesExcelUpdateLogicDescription2": "组件内部所有单元格编辑更新后会自动触发 <code>OnSaveAsync</code> 回调委托，参数是当前更新模型 <code>TItem</code>", "TablesExcelUpdateLogicNote": "此处代码为示例代码", "TablesExcelUpdateLogicLog1": "单元格变化通知 类: Foo ", "TablesExcelUpdateLogicLog2": "值: 单元格", "TablesExcelCellRenderTitle": "通过编辑模板单独控制单元格渲染方式", "TablesExcelCellRenderIntro": "高级用法", "TablesExcelCellRenderTips1": "<code>IsFixedHeader</code> 固定表头 高度设定为 <code>Height='500px'</code>", "TablesExcelCellRenderTips2": "<code>Name</code> 不可编辑显示头像"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesDynamicExcel": {"TablesDynamicExcelTitle": "Table 表格", "TablesDynamicExcelDescription": "常用于大数据单表维护", "TablesDynamicExcelNormalTitle": "Excel 模式", "TablesDynamicExcelNormalIntro": "通过设置表格的 <code>IsExcel</code> 属性，使组件呈现为类似 <code>Excel</code>", "TablesDynamicExcelNormalDescription": "<code>Excel</code> 模式下绑定是 <b>动态类型</b> 时，无法使用 <code>TableColumn</code> 对列属性进行设置，本例中使用 <code>DynamicContext</code> 实例对象 <code>DataTableDynamicContext</code> 构造函数进行设置", "TablesDynamicExcelEnumTitle": "枚举类型", "TablesDynamicExcelEnumIntro": "设置列 <code>Items</code> 值，将枚举类型渲染成 <code>Select</code> 组件", "TablesDynamicExcelEnumNotSet": "未设置", "TablesDynamicExcelEnumTip": "通过上面代码将 <code>Education</code> 列使用 <code>Select</code> 组件渲染", "TablesDynamicExcelKeyboardTitle": "键盘支持", "TablesDynamicExcelKeyboardIntro": "本示例用于测试 <code>Excel</code> 模式下键盘支持", "TablesDynamicExcelKeyboardDescription": "目前支持", "TablesDynamicExcelKeyboardTips_IsExcel": "开启 <code>IsExcel</code> 模式后，部分参数将不再生效，斑马线 <code>IsStriped</code> 树形表格 <code>IsTree</code> 明细行 <code>IsDetails</code> 多选栏 <code>IsMultipleSelect</code>", "TablesDynamicExcelKeyboardTips_DatTable": "使用 <code>DatTable</code> 为数据源时，需要按照下面的步骤进行设置", "TablesDynamicExcelKeyboardTips1_Title": "1. 设置数据源上下文", "TablesDynamicExcelKeyboardTips1_note1": "设置 <code>Table</code> 组件的 <code>TItem</code> 属性值为 <code>DynamicObject</code>", "TablesDynamicExcelKeyboardTips1_note2": "设置 <code>Table</code> 组件的 <code>DynamicContext</code> 属性值为 <code>DataTableDynamicContext</code> 实例", "TablesDynamicExcelKeyboardTips1_note3": "设置 Enum 类型渲染成 Select", "TablesDynamicExcelKeyboardTips1_note4": "将枚举转化为 List", "TablesDynamicExcelKeyboardTips2_Title": "2. 处理 <code>DataRow</code> 变化逻辑", "TablesDynamicExcelKeyboardTips2_note1": "设置 <code>OnChanged</code> 回调委托函数处理 <b>新建/删除</b> 逻辑", "TablesDynamicExcelKeyboardTips2_note2": "输出日志信息", "TablesDynamicExcelKeyboardTips3_Title": "3. 处理 <code>DataCell</code> 变化逻辑", "TablesDynamicExcelKeyboardTips3_note1": "设置 <code>OnValueChanged</code> 回调委托函数处理单元格 <b>更新</b> 逻辑", "TablesDynamicExcelKeyboardTips3_note2": "获得内置 OnValueChanged 回调", "TablesDynamicExcelKeyboardTips3_note3": "调用内部提供的方法", "TablesDynamicExcelKeyboardTips3_note4": "内部方法会更新原始数据源 DataTable", "TablesDynamicExcelKeyboardTips3_note5": "输出日志信息"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesExport": {"TablesExportTitle": "Table 表格", "TablesExportDescription1": "通过设置 <code>ShowExportButton</code> 参数显示导出按钮，组件内置导出 Excel/Csv/Pdf 功能", "TablesExportDescription2": "导出功能提供了导出回调方法 <code>OnExportAsync</code> 使用时可以通过提供自定义的导出方法进行数据导出，如果未提供数据导出方法，组件会根据注入的导出服务进行数据导出", "TablesExportTips": "注入服务", "TablesExportNote1": "增加 BootstrapBlazor 组件", "TablesExportNote2": "增加 Table Excel 导出服务", "TablesExportShowExportButtonTitle": "表格导出功能", "TablesExportShowExportButtonIntro": "通过设置 <code>ShowExportButton=\"true\"</code> 属性是否显示导出按钮，默认为<code>false</code>", "TablesExportShowExportCsvButtonTitle": "导出 Csv/Pdf", "TablesExportShowExportCsvButtonIntro": "通过设置 <code>ShowExportCsvButton=\"true\"</code> <code>ShowExportPdfButton=\"true\"</code> 控制 <code>Csv/Pdf</code> 导出按钮", "TablesExportOnExportAsyncTitle": "自定义导出方法", "TablesExportOnExportAsyncIntro": "通过设置 <code>OnExportAsync</code> 回调委托方法可自定义导出方法，不设置将使用组件内置导出函数，可通过设置列属性 <code>IgnoreWhenExport=\"true\"</code> 导出时忽略此列", "TablesExportButtonDropdownTemplateTitle": "自定义导出下拉框按钮", "TablesExportButtonDropdownTemplateIntro": "通过设置 <code>ExportButtonDropdownTemplate</code> 模板自定义导出按钮下拉框内容", "TablesExportButtonExcelText": "导出当前页数据 Excel", "TablesExportButtonExcelAllText": "导出全部数据 Excel", "TablesExportButtonCliBoardText": "导出当前页到剪切板", "TablesExportButtonCliBoardAllText": "导出全部页到剪切板", "TablesExportToastTitle": "数据导出", "TablesExportToastSuccessContent": "导出数据成功，4 秒后自动关闭", "TablesExportToastFailedContent": "导出数据失败，4 秒后自动关闭", "TablesExportPdfDesc": "导出 Pdf 功能依赖额外组件包", "TablesExportPdfNote1": "注入 <code>Html2Pdf</code> 服务", "TablesExportPdfNote2": "增加 Html2Pdf 导出服务"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesToolbar": {"TablesToolbarTitle": "Table 表格", "TablesToolbarDescription": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "TablesToolbarNormalTitle": "带 Toolbar 的表格", "TablesToolbarNormalIntro": "设置 <code>ShowToolbar</code> 显示工具条组件", "TablesToolbarQueryTableTitle": "带查询的表格", "TablesToolbarQueryTableIntro": "设置 <code>ShowSearch</code> 显示查询组件", "TablesToolbarQueryTableDescription": "本例中通过设置 <code>ShowCardView</code> 显示 <b>视图</b> 按钮，用于切换 <code>Table</code> 组件渲染方式", "TablesToolbarCustomButtonTitle": "自定义扩展按钮", "TablesToolbarCustomButtonIntro": "设置 <code>TableToolbarTemplate</code> 模板添加自定义扩展按钮", "TablesToolbarCustomButtonDescription": "本例中实现经典的应用场景", "TablesToolbarCustomButtonTips1_title": "点击下载按钮，后台开启下载线程进行数据处理，<code>禁用</code> 下载按钮", "TablesToolbarCustomButtonTips1_note1": "前台显示进度条提示，正在打包导出，此期间可以处理其他事务", "TablesToolbarCustomButtonTips1_note2": "数据处理完成后，关闭前台提示弹窗，<code>恢复</code> 下载按钮", "TablesToolbarCustomButtonTips2_title": "本例中通过设置 <code>TableToolbarButton</code> 按钮的 <code>IsAsync</code> 属性开启 <b>异步操作模式</b>，请注意 <code>OnClickCallback</code> 回调委托内需要使用真正的 <b>异步操作</b> 否则无效果", "TablesToolbarCustomDisplayButtonTitle": "自定义显示功能按钮", "TablesToolbarCustomDisplayButtonIntro": "通过设置 <code>ShowAddButton</code> <code>ShowEditButton</code> <code>ShowDeleteButton</code> 属性值来控制单独功能按钮是否显示，当 <code>ShowDefaultButtons</code> 设置为 <code>false</code> 时，所有按钮均不显示", "TablesToolbarCustomButton1": "下载1", "TablesToolbarCustomButton2": "下载2", "TableToolbarPopConfirmButton1": "确认下载", "TablesToolbarCustomComponentTitle": "自定义组件", "TablesToolbarCustomComponentIntro": "使用 <code>TableToolbarComponent</code> 组件实现将自己需要的组件放到工具栏上", "TablesToolbarCustomComponentDescription": "自定义组件在收缩成一个小图标时目前不渲染，稍后有更好的方案时再解决，如果想将组件放到内置按钮后面时，请使用 <code>TableToolbarTemplate</code> 模板", "TablesToolbarShowRefreshTitle": "不显示刷新按钮", "TablesToolbarShowRefreshIntro": "设置 <code>ShowToolbar=\"true\"</code> <code>ShowRefresh=\"false\"</code> 组合条件显示工具栏并且不显示刷新按钮", "TablesToolbarShowRefreshDesc": "点击 <code>刷新</code> 按钮组件内部调用组件实例方法 <code>QueryAsync</code> 如果是 <b>虚拟滚动</b> 模式根据数据源不同触发不同请求数据模式；如果是使用 <code>OnQueryAsync</code> 回调方法作为数据源时，会自动调用"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesCell": {"TablesCellTitle": "Table 表格", "TablesCellDescription": "单元格相关操作示例", "TableCellMergeCellTitle": "合并单元格", "TableCellMergeCellIntro": "基础的表格展示用法", "TableCellMergeCellTip": "本例中通过设置 <code>OnCellRenderHandler</code> 回调委托，通过判断条件对 <code>Name</code> 与 <code>Address</code> 两列进行单元格合并操作，并且通过设置 <code>TableCellArgs</code> 属性 <code>Class</code> 值为 <code>cell-demo</code> 样式表名称对合并后单元格进行背景色设置", "TableCellOnDoubleClickCellTitle": "双击单元格", "TableCellOnDoubleClickCellIntro": "通过设置 <code>OnDoubleClickColumn</code> 回调，设置当前单元格的双击事件", "TableCellOnDoubleClickCellTip": "设置双击单元格回调后，鼠标悬停单元格后单元格出现下标横线，以作提示可通过 <code>.table-cell .is-dbcell</code> 样式覆盖", "RowAttr": "当前单元格行数据 请自行转化为绑定模型", "ColumnNameAttr": "当前单元格绑定列名称", "ColspanAttr": "合并单元格数量", "ClassAttr": "当前单元格样式", "ValueAttr": "当前单元格显示内容", "TableCellOnDoubleClickCellToastTitle": "双击单元格回调", "TableCellOnDoubleClickCellCurrentCellName": "当前单元格名称：", "TableCellOnDoubleClickCellCurrentValue": "当前值："}, "BootstrapBlazor.Server.Components.Samples.Table.TablesLookup": {"TablesLookupTitle": "Table 表格", "TablesLookupDescription": "用于自动将外键列转换成 <code>Text</code> 显示文字", "TableLookupNormalTitle": "Lookup 外置数据源", "TableLookupNormalIntro": "通过设置 <code>Lookup</code> 自动翻译显示文字", "TableLookupNormalTips2": "组件自动将 <code>Complete</code> 转换为 <code>DataSource</code> 集合中的预设值", "TableLookupNormalTips3": "本例中使用 <code>ILookupService</code> 服务进行统一处理，通过设置 <code>LookupServiceKey=\"Foo.Complete\"</code>，从服务中获取 <code>Complete</code> 相关数据集合，将值 <code>true</code> 自动转化为 <code>通过</code> <code>false</code> 转化为 <code>未通过</code>", "TableLookupNormalTips1": "关于详细 <code>ILookupService</code> 文档请参阅 <a href=\"lookup\" target=\"_blank\">[传送门]</a>"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesRow": {"TablesRowTitle": "Table 表格", "TablesRowDesc": "用于展示多条结构类似的数据，可对数据进行排序、筛选、对比或其他自定义操作。", "RowNumberTitle": "显示行号", "RowNumberIntro": "通过设置 <code>ShowLineNo</code> 属性为 <code>true</code> 时表格显示行号了，默认值为 <code>false</code>", "RowNumberP": "设置 <code>LineNoText</code> 属性值来设置行号列头显示文本，默认为 <code>行号</code>", "ClickToSelectTitle": "点击单元格选中整行效果", "ClickToSelectIntro": "通过设置 <code>ClickToSelect</code> 属性值可以实现点击任意单元格选中整行效果，选中行样式被设置为 <code>active</code>", "ClickToSelectP": "可以设置 <code>OnClickRowCallback</code> 回调委托方法对点击行做相应处理，点击表格中任意一行后在下方显示选中行绑定数据的 <code>Name</code> 值", "ClickToSelectP2": "注意：此回调委托内部不进行 UI 渲染，需要 UI 数据更新操作时，请手动显式调用 <code>StateHasChanged</code> 方法", "DoubleClickToEditTitle": "双击单元格编辑本行效果", "DoubleClickToEditIntro": "通过设置 <code>DoubleClickToEdit</code> 属性值可以实现双击任意单元格编辑本行效果，此功能前提是 <code>Edit</code> 功能可用", "DoubleClickToEditP": "移动端（CardView）模式暂时不支持双击编辑当前行功能", "DoubleClickToEditP1": "多选模式下同样支持双击编辑功能，如果设置 <code>ClickToSelect</code> 点击选择效果后，双击编辑功能会导致行选中状态交替选中，请自行设置 <code>ClickToSelect=false</code> 关闭此功能", "DoubleClickRowCallbackTitle": "自定义行双击事件", "DoubleClickRowCallbackIntro": "本功能仅限 <code>Table</code> 组件单选模式下生效，通过设置 <code>DoubleClickRowCallback</code> 属性值可以实现双击任意单元格自定义回调委托方法，实现自己的需求功能", "DoubleClickRowCallbackP": "移动端（CardView）模式暂时不支持双击编辑当前行功能", "SetRowClassFormatterTitle": "自定义行高亮", "SetRowClassFormatterIntro": "通过设置 <code>SetRowClassFormatter</code> 属性值可以实现通过行数据逻辑对行样式进行设置,实现自己高亮需求功能", "SetRowClassFormatterP": "本例中 <code>SetRowClassFormatter</code> 方法通过判断绑定数据的 <code>Count > 60</code> 时行高亮显示", "RowNumberText": "序号", "ClickToSelectP3": "当前选中行：", "ClickToSelectNoneText": "无", "PlaceHolder": "不可为空，50字以内", "RowContentTemplateTitle": "行内容模板", "RowContentTemplateIntro": "通过设置 <code>RowContentTemplate</code> 模板，可通过分装组件的方式实现自定义行内单元格联动逻辑，达到性能最优化，避免单元格数据刷新导致联动后需要刷新整个表格组件的问题，可用于销售类软件，调整单价时总价列变化需求", "RowContentTemplateDesc": "本例中通过自定义组件，实现选择日期后，联动数量列，随机生成一个随机数字，并且保存到原始数据中，从而不需要刷新整个 <code>Table</code> 组件", "RowTemplateTitle": "行模板", "RowTemplateIntro": "通过设置 <code>RowTemplate</code> 模板，可以根据自己的业务逻辑实现行合并功能"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesDynamic": {"TablesDynamicTitle": "Table 表格", "TablesDynamicDescription": "支持动态添加列", "TablesDynamicDataTableTitle": "普通用法", "TablesDynamicDataTableIntro": "仅展示 <code>DataTable</code> 数据", "TablesDynamicEditTitle": "编辑功能", "TablesDynamicEditIntro": "增加编辑维护功能", "TablesDynamicEditDescription": "通过设置 <code>DataTableDynamicContext</code> 实例的 <code>OnChanged</code> 回调方法，新建行时自动设置值", "TablesDynamicDynamicColTitle": "动态列", "TablesDynamicDynamicColIntro": "通过代码动态调整 <code>DataTable</code> 表格组件自动更新", "TablesDynamicPageTitle": "分页", "TablesDynamicPageIntro": "通过与 <code>Pagination</code> 组件配合实现分页功能", "TablesDynamicDynamicColButtonAddColumnText": "增加列", "TablesDynamicDynamicColButtonRemoveColumnText": "移除列"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesSearch": {"TablesSearchTitle": "Table 表格", "TablesSearchDesc": "常用于单表维护，通过属性配置实现简单的增、删、改、查、排序、过滤、搜索等常用功能，通过 <code>Template</code> 的高级用法能实现非常复杂的业务需求功能", "SearchTableTips": "<code>Table</code> 组件的搜索功能提供两种模式，可通过 <code>SearchMode</code> 进行设置，默认为 <code>Popup</code> 即弹窗模式，<b>Top</b> 模式下搜索栏会内置到表格上方，此模式下 <b>刷新</b> 按钮与 <b>搜索</b> 按钮二合一，可以通过设置 <code>ShowSearchButton=false</code> 关闭搜索按钮", "SearchTableTitle": "具有搜索功能的表格", "SearchTableIntro": "设置 <code>ShowSearch</code> 显示查询组件，通过设置 <code>SearchTemplate</code> 模板自定义搜索 UI", "SearchTableLi1": "通过设置 <code>ShowEmpty=\"true\"</code> 开启无数据显示功能", "SearchTableLi2": "<code>EmptyText</code> 参数用于设置无数据时显示文字，默认取资源文件中的内置文字", "SearchTableLi3": "<code>EmptyTemplate</code> 参数用于自定义无数据显示模板", "AutoGenerateSearchTitle": "自动生成搜索功能的表格", "AutoGenerateSearchIntro": "当设置了 <code>ShowSearch</code> 时，如果未设置 <code>SearchTemplate</code> 编辑模板时，组件会尝试自动生成搜索条件 UI", "AutoGenerateSearchP": "列信息绑定时通过设置 <code>Searchable</code> 属性，设置搜索条件自动构建 UI，可通过设置 <code>SearchDialogShowMaximizeButton</code> 使搜索弹窗显示 <b>最大化</b> 按钮", "AutoGenerateSearchTips": "<p>自动构建搜索弹窗时，由于各列设置 <code>Searchable</code> 此时组件会通过 <code>SearchText</code> 与设置 <code>Searchable</code> 值为 <code>true</code> 的各列自动构建搜索拉姆达表达式，通过 <code>QueryPageOptions</code> 的属性 <code>Searches</code> 获得</p>", "AutoGenerateSearchComment": "逻辑关系使用", "CustomColSearchTitle": "自定义列搜索模板", "CustomColSearchIntro": "当设置了 <code>SearchTemplate</code> 时，组件自动生成搜索 UI 时使用此模板作为呈现 UI", "CustomColSearchP": "通过设置姓名列的 <code>SearchTemplate</code> 自定义编辑时使用下拉框来选择姓名", "CustomColSearchP1": "姓名列搜索模板使用下拉框进行数据过滤，并且增加了 <b>请选择...</b> 项", "CustomColSearchP2": "生成列搜索模板是查找顺序为 <code>SearchTemplate</code> -> <code>AutoGenerate</code> 优先查找是否设置了搜索模板，然后根据绑定字段类型自动生成", "CustomColSearchP3": "通过设置 <code>ShowSearch</code> 控制是否显示整个搜索栏", "CustomColSearchP4": "通过设置 <code>ShowSearchText</code> 控制是否显示模糊搜索栏", "CustomColSearchP5": "通过设置 <code>ShowResetButton</code> 控制是否显示重置搜索按钮", "CustomColSearchP6": "通过设置 <code>ShowAdvancedSearch</code> 控制是否显示高级搜索按钮", "CustomColSearchP7": "通过设置 <code>SearchDialogDraggable=\"true\"</code> 使搜索弹出框可拖拽", "CustomerSearchModelTitle": "自定义搜索模型", "CustomerSearchModelIntro": "设置了 <code>CustomerSearchModel</code> 与 <code>CustomerSearchTemplate</code>，完全自主控制搜索条件 UI", "CustomerSearchModelP": "由于某种应用场景我们的表格实体类 <code>TItem</code> 来充当搜索模型时并不理想，比如我们有一个属性是 <code>int</code> 或者 <code>enum</code> 时，我们作为搜索条件时需要有一个 <b>全部</b> 或者 <b>请选择 ...</b> 的空白条件项。此时使用默认模型时非常难用，就可以使用自定义搜索模型 <code>CustomerSearchModel</code> 与 <code>CustomerSearchTemplate</code> 来自定义控制搜索界面", "AddModelTitle": "增加测试数据窗口", "EditModelTitle": "编辑测试数据窗口", "SelectedItemText": "请选择 ...", "SelectedItemText1": "姓名1", "SelectedItemText2": "姓名2", "SelectedItemValue1": "姓名1", "SelectedItemValue2": "姓名2", "AutoGenerateSearchGroupBoxTitle": "搜索功能体验区", "DisplayText1": "搜索模式", "DisplayText2": "模糊搜索", "DisplayText3": "显示清空", "DisplayText4": "显示搜索", "SearchTableGroupBoxText": "搜索条件", "NamePlaceholder": "请输入姓名，50字以内", "AddressPlaceholder": "请输入地址，500字以内", "AutoHeightIntro": "<p><b>高度自适应</b></p><p>本例中设置父容器高度为 <code>600px</code> 展开/收起搜索栏时，表格自动充满父容器</p>"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesFilter": {"MultiFilterTitle": "多选列表筛选", "MultiFilterIntro": "通过 <code>FilterTemplate</code> 使用内置 <code>MultiFilter</code> 组件提供多选筛选功能", "MultiFilterTips": "组件提供 <code>Items</code> <code>OnGetItemsAsync</code> 两种设置数据源方式", "MultiFilterTipsLi1": "<code>Items</code> 适合数据量小且静态事先准备好的数据集合", "MultiFilterTipsLi2": "<code>OnGetItemsAsync</code> 适合数据量大且动态的数据集合，出于性能考虑组件内部采用局域懒记载方式填装数据，即点开过滤窗口时才回调获得数据", "TablesFilterTitle": "筛选和排序功能", "TablesFilterDesc": "筛选可快速查找到自己想看的数据；排序可快速查找或对比数据。", "TablesFilterDescLi1": "对某一列数据进行筛选，通过指定列的 <code>Filterable</code> 属性来指定需要筛选的列", "TablesFilterDescLi2": "对某一列数据进行排序，通过指定列的 <code>Sortable</code> 属性来指定需要排序的列，通过多次点击更改排序规则", "FilterableTitle": "可筛选数据的表格", "FilterableIntro": "设置 <code>TableColumn</code> 列的 <code>Filterable</code> 属性，控制列头是否开启数据筛选功能", "FilterableP": "<code>Filterable</code> 目前支持两种应用方式：", "FilterableLi1": "查询方法参数中的 <code>Filters</code> 是筛选条件，数据库查询数据时可以通过此属性自行进行数据筛选", "FilterableLi2": "无感使用，正常查询数据，对筛选不进行任何处理，组件内部将根据 <code>Filter</code> 进行内部处理", "FilterableLi3": "外部已经进行了筛选时请设置 <code>QueryData&lt;TItem&gt;</code> 参数的 <code>IsFiltered</code> 属性值为 <code>true</code>", "FilterableLi4": "各列可分别设置筛选条件，各列之间筛选条件为 <code>And</code> 并且的关系", "FilterableDiv": "本示例分别为 <code>DateTime、string、bool、enum、int</code> 类型，弹出筛选框也分别不同", "FilterableAlert": "开启筛选功能仅需要设置 <code>TableColumn</code> 的 <code>Filterable</code> 值为 <code>true</code>，无需额外任何代码", "FilterTemplateTitle": "自定义筛选模板", "FilterTemplateIntro": "设置 <code>FilterTemplate</code> 模板值，自定义列筛选模板，非常适用于复杂类型的筛选弹窗", "ShowFilterHeaderTitle": "显示过滤表头", "ShowFilterHeaderIntro": "设置 <code>ShowFilterHeader</code> 属性值为 <code>true</code> 时显示过滤表头", "DefaultSortTitle": "默认排序功能", "DefaultSortIntro": "设置 <code>DefaultSort</code> 属性值为 <code>true</code> 时作为默认排序列", "DefaultSortLi1": "设置 <code>DefaultSort=true</code> 开启默认排序功能，当多列设置此属性时，第一列起作用", "DefaultSortLi2": "设置 <code>DefaultSortOrder</code> 值，设置默认排序规则", "DefaultSortP": "本例中默认排序为最后一列倒序排序", "SortListTitle": "多列排序", "SortListIntro": "设置 <code>SortList</code> 属性，表格加载时使用此参数进行多列排序", "SortListP": "本例中默认排序规则为：<code>DeteTime desc</code> <code>Address</code>，无需自己处理排序逻辑，组件内部已经内置", "OnSortTitle": "动态多列排序", "OnSortIntro": "设置 <code>OnSort</code> 参数", "OnSortP": "点击列头进行排序时，组件内部调用 <code>OnSort</code> 回调，可以在此处根据业务逻辑设置其返回值即可实现动态多列排序功能，本例中点击 <b>时间</b> 列头进行正序排序时，内部使用 <code>DateTime, Count</code> 倒序时使用 <code>DateTime desc, Count desc</code>", "OnAdvancedSortTitle": "自定义排序", "OnAdvancedSortIntro": "通过设置 <code>ShowAdvancedSort=\"true\"</code> 显示 <b>高级排序</b> 按钮，通过自定义条件进行多列排序操作", "SetFilterInCodeTitle": "通过代码设置过滤条件", "SetFilterInCodeIntro": "示例展示如何通过代码设置过滤条件", "SetFilterInCodeButtonText1": "名称包含01", "SetFilterInCodeButtonText2": "重置条件", "TablesFilterTemplateDescription": "<p><code>FilterTemplate</code> 类型为 <code>RenderFragment</code> <span>其值为自定义组件，组件必须继承</span> <code>FilterBase</code> 本例中最后一列 <b>数量列</b> 通过筛选模板使用自定义组件 <code>CustomerFilter</code>  <a href=\"{0}\" target=\"_blank\">[传送门] CustomerFilter 组件源码</a></p><p class=\"code-label\">注意事项：</p><ul class=\"ul-demo\"><li>自定义过滤组件使用 <code>FilterProvider</code> 包裹，<code>FilterProvider</code>必须在 <code>FilterTemplate</code> 节点下</li><li>通过 <code>FilterProvider</code> 组件的参数可微调过滤器；例如通过设置 <code>ShowMoreButton</code> 控制是否显示 <b>+ -</b> 符号</li></ul-demo>"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesFixedHeader": {"TablesFixedHeaderTitle": "固定表头功能", "TablesFixedHeaderDesc": "滚动数据时表头固定方便查看各列表头信息", "TablesFixedHeaderDescP": " 本组件固定表头做法采用的是 <code>双表头</code> 做法，常见问题时出现列不对齐的问题，根据自己实际情况请自行添加部分样式,<br />固定表头用法请尽可能的设置每列宽度，以避免列宽不对齐的问题", "FixedHeaderTitle": "宽度自适应", "FixedHeaderIntro": "设置 <code>Height=200</code> 和 <code>IsFixedHeader=\"true\"</code> 固定表头", "FixedHeaderP": "数据比较多加载后即出现纵向滚动条示例", "FixedHeaderP1": "请设置个列宽度，允许一列宽度不设置，进行自动填充", "FixedWidthTitle": "宽度固定", "FixedWidthIntro": "设置 <code>Height=200</code> 和 <code>IsFixedHeader=\"true\"</code> 固定表头，当每页显示数据为 <code>10</code> 行时高度超出设置值 <code>200</code> 后，Table 组件出现纵向滚动条", "FixedWidthP": "所有列均设置宽度，当屏幕过小时会自动出现横向滚动条", "AllowResizingTitle": "宽度可调整", "AllowResizingIntro": "固定表头的同时设置 <code>AllowResizing</code> 属性，使列宽可以调整", "AllowResizingP": "通过设置 <code>Height</code> 参数固定表头，通过设置 <code>AllowResizing</code> 参数允许调整列宽", "AutoHeightTitle": "自适应高度", "AutoHeightIntro": "不设置 <code>Height</code> 值通过父容器自适应高度", "AutoHeightP": "本例中 <code>Table</code> 表格父容器高度改变时，组件会自适应高度与宽度", "DialogIntro": "<code>Table</code> 组件在弹窗 <code>Dialog</code> 内使用", "DialogDesc": "<p>在弹窗中使用固定表头的 <code>Table</code> 注意事项：</p><div>弹窗要给 <code>DialogOption</code> 的 <code>Class</code> 参数赋值，本例中设置为 <code>dialog-table</code>，这个操作是为了让我们自定义样式能够定位到弹窗中 <code>Table</code> 组件的父容器 <code>modal-body</code> 并设置其高度，这里可以使用自适应高度的写法 <code>height: calc(100vh - 400px);</code>；当 <code>Table</code> 组件的父级容器有高度后，组件自己就会通过计算进行自适应高度适配了</div>", "DialogTitle": "弹窗内使用", "DialogButtonText": "弹窗"}, "BootstrapBlazor.Server.Components.Samples.Table.TablesAttribute": {"TableAttributeTitle": "特性标签 AutoGenerateClassAttribute/AutoGenerateColumnAttribute", "TableAttributeDescription": "通过使用特性标签可以大大节约代码", "AutoGenerateClassAttribute": "类标签 AutoGenerateClassAttribute", "AutoGenerateColumnAttribute": "列标签 AutoGenerateColumnAttribute", "AutoGenerateClassP1": "1. 使用用法", "AutoGenerateClassP1Content": "本例中通过设置 <code>Filterable=true</code> 使表格所有列均开启可搜索功能", "AutoGenerateCode": "等效代码如下：", "AutoGenerateClassP2": "1. 使用用法", "AutoGenerateClassP2Content": "本例中通过设置 <code>Filterable=true</code> 使表格 <code>Name</code> 列开启可搜索功能", "TableAttributeIntro": "权重说明", "TableAttributeIntroLI1": "只写了 <code>AutoGenerateClassAttribute</code> 以其参数值为准", "TableAttributeIntroLI2": "只写了 <code>AutoGenerateColumnAttribute</code> 以其参数值为准", "TableAttributeIntroLI3": "写了 <code>AutoGenerateClassAttribute</code> 并且写了 <code>AutoGenerateColumnAttribute</code> 合并两者参数值取 <code>AutoGenerateColumnAttribute</code> 值", "TableAttributeIntroLI4": "写了 <code>TableColumn</code> <code>参数值时以 TableColumn</code> 参数值为最终值", "TableAttributeQA1": "写了 <code>AutoGenerateClassAttribute</code> 并且写了 <code>AutoGenerateColumnAttribute</code> 合并两者参数值取值举例说明", "TableAttributeQAP1": "由于 <code>AutoGenerateClassAttribute</code> 设置 <code>Filterable</code> 值为 <code>true</code>，而 <code>AutoGenerateColumn</code> 保持 <code>Filterable</code> 默认值 <code>false</code> 最终 <code>UI</code> 使用 <code>true</code>", "TableAttributeQAP2": "由于 <code>AutoGenerateClassAttribute</code> 未设置 <code>Sortable</code> 保持默认值 <code>false</code>，而 <code>AutoGenerateColumn</code> 设置 <code>Sortable</code> 值为 <code>true</code> 最终 <code>UI</code> 使用 <code>true</code>", "TableAttributeQAP3": "最新版本 <code>TableColumn</code> 的 <code>Filterable</code> 等一些参数值均更改为 <b>可为空数据类型</b>，其默认值由原来的 <code>false</code> 更改为 <code>null</code>，可由此判断，只要 <code>TableColumn</code> 的参数 <code>Filterable</code> 值不为空，即以 <code>TableColumn</code> 值为最终 <code>UI</code> 值", "TableAttributeColumn": "TableColumn 显示设置值"}, "BootstrapBlazor.Server.Components.Pages.Introduction": {"Title": "简介", "SubTitle": "BootstrapBlazor 是一套基于 Bootstrap 和 Blazor 的企业级组件库，可以认为是 Bootstrap 项目的 Blazor 版实现。", "UpdateTitle": "更新日志", "UpdateLog": "为了修复组件 <code>bug</code> 每天可能有小版本发布更新日志", "UpdateLogLink": "[传送门]", "LearnTitle": "学习资料", "LearnLi1": "Blazor 官方文档", "LearnLi2": "使用 Blazor WebAssembly 和 Visual Studio Code 生成 Web 应用", "LearnLi3": "什么是 Blazor", "LearnLi4": "练习 - 配置开发环境", "LearnLi5": "Blazor 组件", "LearnLi6": "练习 - 添加组件", "LearnLi7": "数据绑定和事件", "LearnLi8": "练习 - 数据绑定和事件", "Summarize": "总结", "ProjectsShow": "作品展示", "P5": "国内最大代码托管协作开发平台 <b><a href='{0}' target='_blank'>码云</a></b> C# 板块最高星 <a href='{1}' target='_blank'><img src='{2}' alt='star'></a> 作品 <b><a href='{3}' target='_blank'>通用后台管理系统 BootstrapAdmin</a></b> 准备使用本组件实现全部功能", "ShowWebSiteTitle1": "实战网站（混合版本）", "ShowWebSiteTitle2": "实战网站（纯 Blazor 版本）", "GetStarted": "快速上手", "QuickStart": "快速入门", "Features": "组件特色", "P6": "Bootstrap Blazor UI 组件库提供了从基本的 <code>Button</code> 组件到高级的网页级 <code>Online</code> 组件", "Advantage": "优势", "AdvantageLi1": "使用组件无需编写 <code>JavaScript</code>", "AdvantageLi2": "组件支持所有 <code>html</code> 特性", "AdvantageLi3": "组件支持数据双向绑定", "AdvantageLi4": "组件支持自动客户端验证", "AdvantageLi5": "组件支持组合", "Community": "交流群"}, "BootstrapBlazor.Server.Components.Components.QQGroup": {"Group": "QQ群", "Status": "满", "Welcome": "欢迎加群讨论"}, "BootstrapBlazor.Server.Components.Components.EventTable": {"Title": "事件 Event"}, "BootstrapBlazor.Server.Components.Samples.Handwrittens": {"Title": "Handwritten 手写签名", "SubTitle": "用于移动终端签名保存为 Base64 编码字符串", "Tips": "已弃用，请使用 <a href=\"signature-pad\">BootstrapBlazor.SignaturePad</a> 组件，已知问题在弹窗中无法签名", "BaseUsageText": "基础用法", "IntroText1": "触摸设备直接手写签名保存为 Base64", "IntroText2": "桌面浏览器测试请用F12模拟为触摸设备", "HandwrittenButtonText": "签名", "SaveButtonText": "保存按钮文本", "SaveButtonTextDefaultValue": "保存", "ClearButtonText": "清除按钮文本", "ClearButtonTextDefaultValue": "清除", "Result": "手写签名imgBase64字符串", "HandwrittenBase64": "手写结果回调方法"}, "BootstrapBlazor.Server.Components.Samples.ImageViewers": {"ImageViewerTitle": "ImageViewer 图片", "ImageViewerDescription": "图片容器，在保留原生 img 的特性下，支持懒加载，自定义占位、加载失败等", "ImageViewerNormalTitle": "基本用法", "ImageViewerNormalIntro": "加载并显示图片文件", "ImageViewerPlaceHolderTitle": "占位内容", "ImageViewerPlaceHolderIntro": "可通过设置 <code>ShowPlaceHolder</code> 开启占位符功能，通过设置 <code>PlaceHolderTemplate</code> 自定义占位内容", "ImageViewerPlaceHolderTemplateTitle": "占位模板", "ImageViewerPlaceHolderTemplateIntro": "可通过设置 <code>PlaceHolderTemplate</code> 开启占位模板功能，未设置 <code>Url</code> 或者正在加载时显示此模板内容", "ImageViewerErrorTemplateTitle": "加载失败", "ImageViewerErrorTemplateIntro": "可通过设置 <code>ErrorTemplate</code> 开启错误模板功能，参数 <code>Url</code> 无法加载图片时显示此模板内容", "ImageViewerPreviewListTitle": "大图预览", "ImageViewerPreviewListIntro": "可通过设置 <code>PreviewList</code> 开启预览大图的功能，设置 <code>ZoomSpeed</code> 控制滚动缩放时的速度", "ImageViewersAttrUrl": "图片 Url", "ImageViewersAttrAlt": "原生 alt 属性", "ImageViewersAttrShowPlaceHolder": "是否显示占位符 适用于大图片加载", "ImageViewersAttrHandleError": "加载失败时是否显示错误占位符", "ImageViewersAttrPlaceHolderTemplate": "占位模板 未设置 Url 或者正在加载大图时生效", "ImageViewersAttrErrorTemplate": "错误模板 图片路径错误时生效", "ImageViewersAttrFitMode": "原生 object-fit 属性", "ImageViewersAttrZIndex": "原生 z-index 属性", "ImageViewersAttrPreviewList": "预览大图链接集合", "ImageViewersAttrOnLoadAsync": "图片加载成功时回调方法", "ImageViewersAttrOnErrorAsync": "图片加载失败时回调方法", "ImageViewersAttrIsIntersectionObserver": "图片懒加载", "ImageViewerNormalTips1": "<code>object-fit: fill</code> 填充 默认值 使图片拉伸填满整个容器, 不保证保持原有的比例", "ImageViewerNormalTips2": "<code>object-fit: contain</code> 包含 保持原有尺寸比例缩放 保证整个图片都可以出现在容器中，因此此参数可能会在容器内留下空白", "ImageViewerNormalTips3": "<code>object-fit: cover</code> 覆盖 保持原有尺寸比例缩放，宽度和高度至少有一个和容器一致（尺寸小的一致）因此此参数可能会让图片部分区域不可见", "ImageViewerNormalTips4": "<code>object-fit: none</code> 无 保持原有尺寸比例，同时保持图片原始尺寸大小，多出的部分隐藏", "ImageViewerNormalTips5": "<code>object-fit: scale-down</code> 降低 就好像依次设置了 <b>none</b> 或 <b>contain</b> 最终呈现的是尺寸比较小的那个", "ImageViewerPlaceHolderTips1": "图片加载后浏览器默认会缓存，建议 <kbd>F12</kbd> 关闭缓存体验此功能", "ImageViewerPlaceHolderDefault": "默认", "ImageViewerPlaceHolderCustom": "自定义", "ImageViewerPlaceHolderLoading": "加载中 ...", "ImageViewerPlaceHolderTemplateTips1": "图片加载后浏览器默认会缓存，建议 <kbd>F12</kbd> 关闭缓存体验此功能", "ImageViewerPlaceHolderTemplateUrl": "未设置 Url", "ImageViewerPlaceHolderTemplatePlaceholder": "占位模板", "ImageViewerPlaceHolderTemplateLoadingShow": "加载时显示", "ImageViewerErrorTemplateUrlError": "Url 路径错误", "ImageViewerErrorTemplateCustom": "自定义", "ImageViewerErrorTemplateLoadFailed": "加载失败", "ImagePreviewerTitle": "单独使用 ImagePreviewer", "ImagePreviewerIntro": "配合 <code><PERSON><PERSON></code> 等其他组件，直接弹出 <code>ImagePreviewer</code>", "ImagePreviewerButton": "显示 Previewer", "IntersectionObserverTitle": "懒加载", "IntersectionObserverIntro": "通过设置 <code>IsIntersectionObserver=\"true\"</code> 开启懒加载特性，当图片在不可见区域时不加载图片，当图片即将可见时才开始加载图片"}, "BootstrapBlazor.Server.Components.Samples.Geolocations": {"GeolocationsTitle": "地理定位/移动距离追踪", "GeolocationNormalText": "基础用法", "GeolocationNormalIntro": "通过浏览器 API 获取定位信息。", "GeolocationNormalIntro2": "单击按钮以获取地理位置坐标。", "GeolocationNormalIntro3": "注意: 出于安全考虑，当网页请求获取用户位置信息时，用户会被提示进行授权。注意不同浏览器在请求权限时有不同的策略和方式。Windows10 在未开启定位的情况下无法获取位置。", "GetLocationButtonText": "获取位置", "WatchPositionButtonText": "移动距离追踪", "ClearWatchPositionButtonText": "停止追踪", "GetLocationResultSuccess": "调用 GetLocaltion 成功", "GetLocationResultFailed": "调用 GetLocaltion 失败", "WatchPositionResultSuccess": "调用 WatchPosition 成功", "WatchPositionResultFailed": "调用 WatchPosition 失败", "ClearWatchPositionResultSuccess": "停止追踪成功", "ClearWatchPositionResultFailed": "停止追踪失败", "Longitude": "经度", "Latitude": "纬度", "Accuracy": "位置精度", "Altitude": "海拔", "AltitudeAccuracy": "海拔精度", "Heading": "方向", "Speed": "速度", "LastUpdateTime": "时间戳", "CurrentDistance": "移动距离", "TotalDistance": "总移动距离"}, "BootstrapBlazor.Server.Components.Samples.Notifications": {"NotificationsTitle": "通知", "NotificationsNormalTitle": "基础用法", "NotificationsNormalIntro": "通过浏览器API发送通知信息", "NotificationsNormalDescription": "单击按钮以发送通知", "NotificationsNormalTips1": "注意: 出于安全考虑，当网页请求发送通知时，用户会被提示进行授权", "NotificationsNormalButtonText": "显示通知", "NotificationSilentButtonText": "显示静默通知", "NotificationsNormalCheckPermissionText": "检查权限", "NotificationsNormalPermissionText": "允许通知", "NotificationsNormalSilentText": "静默", "NotificationsNormalTitleText": "标题", "NotificationsNormalMessageText": "信息", "NotificationsIconText": "图标", "NotificationsSoundText": "通知触发时要播放的音频文件的 URL", "CheckPermissionResultSuccess": "获取权限成功", "NotificationResultSuccess": "调用通知成功", "NotificationsNormalTips2": "使用 <code>BrowserNotification</code> 静态方法直接调用 <code>Dispatch</code> 方法", "NotificationsNormalShowNotificationCallbackText": "发送结果回调", "NotificationsNormalGetPermissionCallbackText": "检查通知权限结果回调", "NotificationsNormalOnClickText": "通知点击后的回调方法名称", "NotificationsNormalTitleSampleText": "你有新的物流通知", "NotificationsNormalMessageSampleText": "您的包裹已到达亮马河南路,距离分发只剩3站."}, "BootstrapBlazor.Server.Components.Samples.SignaturePads": {"BaseUsageText": "基础用法", "BaseUsageIntro": "鼠标滑动进行签名", "ButtonCssStyleText": "按钮CSS式样", "ButtonCssStyleIntro": "设置按钮样式", "CustomButtonText": "自定义按钮文本", "CustomButtonIntro": "自定义按钮文本", "Tips": "复杂签名会导致传输数据量大 ssr 会出现断流显示 reload 错误,配置更改单个传入集线器消息的最大大小(默认 32KB)解决这个问题。<code>builder.Services.AddServerSideBlazor().AddHubOptions(o => o.MaximumReceiveMessageSize = null);</code>", "Title": "SignaturePad 手写签名", "SignText": "请签名", "CleanText": "清除", "ResponsiveInterfaceText": "响应式签名界面", "ResultBase64Text": "签名 Base64", "ResultText": "您的签名", "SignatureText": "签名", "BackText": "返回前页", "TipsResponsive": "设备宽度小于 640px (例如手机) 自动置顶无边框, 宽度小于 500px 按钮自动旋转. 可选透明背景。"}, "BootstrapBlazor.Server.Components.Samples.SignaturePadPageResponsive": {"ResponsiveInterfaceText": "响应式签名界面", "ResultBase64Text": "签名 Base64", "ResultText": "您的签名", "SignatureText": "签名", "BackText": "返回前页", "TipsResponsive": "设备宽度小于 640px (例如手机) 自动置顶无边框, 宽度小于 500px 按钮自动旋转. 可选透明背景。"}, "BootstrapBlazor.Server.Components.Samples.Reconnectors": {"Title": "Reconnector 重连组件", "SubTitle": "用于自定义连接失败时各种状态信息", "ReconnectingTemplateAttr": "正在尝试重试连接对话框的模板", "ReconnectFailedTemplateAttr": "连接失败对话框的模板", "ReconnectRejectedTemplateAttr": "连接被拒绝对话框的模板", "ReconnectingTemplateText": "正在连接模板", "ReconnectFailedTemplateText": "连接失败模板", "ReconnectRejectedTemplateText": "连接拒绝模板", "Intro": "组件内置了默认样式如果不满意可根据自己需求进行样式覆盖，组件应用于 <code>components-reconnect-modal</code> 元素的 <code>CSS</code> 类", "IntroTitle": "可单独设置不同状态模板", "Usage": "使用方法", "Step1": "1. <code>_Layout.cshtml</code> 文件中增加如下代码", "Step2": "2. <code>App.razor</code> 文件中增加如下代码", "TableHeader1": "CSS 类", "TableHeader2": "说明", "TableRow1": "连接已丢失。 客户端正在尝试重新连接。显示模式。", "TableRow2": "将为服务器重新建立活动连接。隐藏模式。", "TableRow3": "重新连接失败，可能是由于网络故障引起的。 若要尝试重新连接，请在 <b>JavaScript</b> 中调用 <code>window.Blazor.reconnect()</code>", "TableRow4": "已拒绝重新连接。 已达到服务器，但拒绝连接，服务器上的用户状态丢失。 若要重新加载应用，请在 <b>JavaScript</b> 中调用 <code>location.reload()</code>。当出现以下情况时，可能会导致此连接状态：", "TableRow5": "<li>服务器端线路发生故障。</li><li>客户端断开连接的时间足以使服务器删除用户的状态。用户组件的实例已被处置。</li><li>服务器已重启，或者应用的工作进程被回收。</li>", "Application": "实战应用"}, "BootstrapBlazor.Server.Components.Samples.OnScreenKeyboards": {"OnScreenKeyboardsTitle": "On-Screen keyboard 屏幕键盘", "OnScreenKeyboardsNormalText": "基础用法", "OnScreenKeyboardsNormalIntro": "全键盘", "OnScreenKeyboardsTypekeyboardTitle": "字母键盘", "OnScreenKeyboardsTypeNumpadTitle": "数字键盘", "OnScreenKeyboardsThemeDarkTitle": "暗黑主题", "OnScreenKeyboardsSpecialcharactersTitle": "特殊符号-欧洲", "OnScreenKeyboardsSpecialcharactersCustomerTitle": "特殊符号自定义", "OnScreenKeyboardsAppTitle": "BootstrapBlazor 组件", "OnScreenKeyboardsAppDescription": "双绑", "OnScreenKeyboardsTips": "如果表现不正常,可以加上这句 css 调试看看", "OnScreenKeyboardsCss": "<code> .kioskboard-body-padding { padding-top: unset !important;} </code>", "OnScreenKeyboardsBasicTitle": "基础用法"}, "BootstrapBlazor.Server.Components.Samples.Bluetooth": {"BluetoothTitle": "IBluetooth 蓝牙服务", "BluetoothIntro": "提供了查询蓝牙可用性和请求访问设备的方法", "BluetoothDescription": "允许网站与连接到用户计算机的蓝牙设备进行通信。例如可以连接蓝牙打印机进行打印操作", "BluetoothTipsLi1": "该功能仅在部分或所有支持浏览器的安全上下文(HTTPS)中可用", "BluetoothTipsLi2": "这是一项实验性技术，在生产中使用之前请仔细,检查 <a href=\"https://developer.mozilla.org/en-US/docs/Web/API/Bluetooth#browser_compatibility\" target=\"_blank\">浏览器兼容性表</a>", "BluetoothTipsTitle": "注意：<code>IBluetoothDevice</code> 接口实例继承 <code>IAsyncDisposable</code> 路由切换时需要对其进行资源释放，调用其 <code>DisposeAsync</code> 即可", "NotSupportBluetoothTitle": "扫描设备", "NotSupportBluetoothContent": "当前浏览器不支持串口操作，请更换 Edge 或者 Chrome 浏览器", "BluetoothRequestText": "扫描", "BluetoothConnectText": "连接", "BluetoothDisconnectText": "断开", "BluetoothGetBatteryText": "读取电量", "BluetoothGetHeartRateText": "读取心率", "BaseUsageTitle": "基础用法", "BaseUsageIntro": "通过 <code>IBluetooth</code> 服务，请求与蓝牙设备通讯", "BluetoothGetCurrentTimeText": "读取时间", "BluetoothDeviceInfoText": "读取硬件信息", "UsageDesc": "点击扫描按钮，在弹窗中选中手机进行测试", "BluetoothGetServicesText": "读取服务列表", "BluetoothGetCharacteristicsText": "读取特征列表", "BluetoothReadValueText": "读取特征值"}, "BootstrapBlazor.Server.Components.Samples.FileIcons": {"Title": "File Icon 文件图标", "Intro": "通过文件扩展名自定义显示图标", "BaseUsageTitle": "基本用法", "BaseUsageIntro": "通过设置 <code>Type</code> 设置图标扩展名", "ColorTitle": "颜色", "ColorIntro": "通过设置 <code>IconColor</code> 设置图标颜色", "BackgroundTemplateTitle": "自定义背景图", "BackgroundTemplateIntro": "通过设置 <code>BackgroundTemplate</code> 通过模板自定义背景模板", "CustomCssTitle": "自定义样式", "CustomCssIntro": "通过设置 <code>class</code> 自定义样式调整图标大小", "ExtensionAttr": "文件扩展名", "IconColorAttr": "扩展名标签背景色", "BackgroundTemplateAttr": "自定义背景图模板", "SizeTitle": "大小", "SizeIntro": "通过设置 <code>Size</code> 设置图标大小"}, "BootstrapBlazor.Server.Components.Samples.PdfReaders": {"Title": "PDF Reader PDF阅读器", "PdfReaderNormalText": "基础用法", "PdfReaderNormalIntro": "Filename 参数显示本服务器pdf文件,或 Stream 参数指定用于渲染的文件流", "PdfReaderStreamModeText": "流模式", "PdfReaderStreamModeIntro": "可跨域读取文件", "PdfReaderCompatibilityModeText": "兼容模式", "PdfReaderCompatibilityModeIntro": "兼容旧版浏览器", "PdfReaderAdvancedText": "高级参数", "PdfReaderAdvancedIntro": "可以使用高级参数", "PdfReaderIssue": "破坏性更新参考链接: 移除pdfobject", "AttributesPdfReaderFilename": "PDF文件路径(Url或相对路径)", "AttributesPdfReaderStreamMode": "使用流化模式,可跨域读取文件", "AttributesPdfReaderWidth": "宽 单位(px/%)", "AttributesPdfReaderHeight": "高 单位(px/%)", "AttributesPdfReaderStyleString": "组件外观 Css Style", "AttributesPdfReaderPage": "页码", "AttributesPdfReaderPageMode": "页面模式", "AttributesPdfReaderZoom": "缩放模式", "AttributesPdfReaderSearch": "查询字符串", "AttributesPdfReaderRefresh": "刷新组件", "AttributesPdfReaderNavigateToPage": "跳转到指定页码", "AttributesPdfReaderRefreshPage": "刷新并跳转页码", "AttributesPdfReaderRefreshComponent": "刷新组件(查询关键字,页码,页面模式,缩放模式)", "AttributesPdfReaderStream": "用于渲染的文件流,为空则用URL参数读取文件", "AttributesPdfReaderViewerBase": "浏览器页面路径", "AttributesPdfReaderViewerBaseDefaultValue": "内置", "AttributesPdfReaderNavPanels": "显示导航窗格", "AttributesPdfReaderToolbar": "显示工具栏", "AttributesPdfReaderStatusBar": "显示状态栏", "AttributesPdfReaderDebug": "显示调试信息", "PdfReaderAdvancedViewMode": "视图模式", "PdfReaderAdvancedPageMode": "页面模式", "PdfReaderAdvancedPagePrevious": "页码-", "PdfReaderAdvancedPageNext": "页码+", "PdfReaderAdvancedGoToPage": "跳转", "PdfReaderAdvancedRefreshPage": "刷新", "PdfReaderAdvancedStreamModeLabel": "流模式", "PdfReaderAdvancedFilenameLabel": "文件相对路径或者URL", "PdfReaderAdvancedSearchLabel": "搜索", "PdfReaderAdvancedReadOnlyLabel": "禁用复制/打印/下载", "PdfReaderAdvancedWatermarkLabel": "水印内容", "PdfReaderCompatibilityModeTips": "- Chrome < 97 自动使用 2.4.456 版本<br/>- Chrome < 109 自动使用 2.6.347 版本<br/>- 注:ReadOnly 和 Watermark 在这两种兼容模式下不能使用", "LocalFileName": "PDF本地文件路径"}, "BootstrapBlazor.Server.Components.Samples.PdfViewers": {"PdfViewerTitle": "PDFViewer PDF 阅读器", "PdfViewerDescription": "在组件中打开 Pdf 文件阅读其内容", "PdfViewerNormalTitle": "基础用法", "PdfViewerNormalIntro": "通过设置 <code>Url</code> 参数加载 Pdf 文件，设置 <code>UseGoogleDocs</code> 使用 docs.google.com 预览", "PdfViewerToastSuccessfulContent": "PDF 文档加载成功", "PdfViewerToastNotSupportContent": "当前浏览器不支持 Pdf 文档预览功能"}, "BootstrapBlazor.Server.Components.Samples.VideoPlayers": {"VideoPlayersTitle": "VideoPlayer 视频播放器", "VideoPlayersNormalTitle": "基础用法", "VideoPlayersNormalIntro": "", "VideoPlayersChangeURLTitle": "切换播放资源", "VideoPlayersChangeURLIntro": "使用 Reload 方法切换播放资源", "VideoPlayersTips": "默认启用静音模式,只有这样自动播放功能才符合现代浏览器标准.", "MineType": "MineType 类型", "ResourceUrl": "资源地址", "MineTypeDesc": "资源类型,video/mp4, application/x-mpegURL, video/ogg .. ", "ValueList": "(见页脚)", "Width": "宽度", "Height": "高度", "ShowControls": "显示控制条", "AutoPaly": "自动播放", "Poster": "设置封面资源,相对或者绝对路径", "SwitchResource": "切换播放资源", "SetPoster": "设置封面", "ShowBar": "是否显示 bar", "OnError": "错误回调"}, "BootstrapBlazor.Server.Components.Components.PackageTips": {"Tips": "<p class=\"code-label\">注意事项 ：</p><p>本组件依赖于 <a href='https://www.nuget.org/packages?q={0}' target='_blank'><code>{0}</code></a>，使用本组件时需要引用其组件包</p><p class=\"code-label\">Nuget 包安装</p><p>使用 <a href='https://www.nuget.org/packages?q={0}' target='_blank'>nuget.org</a> 进行 <code>{0}</code> 组件的安装</p>"}, "BootstrapBlazor.Server.Components.Samples.FileViewers": {"Title": "FileViewer 文件预览器", "BaseUsageText": "基础用法", "BaseUsageIntro": "Excel/Word 文件路径或者URL", "BaseUsageText2": "重新载入文件", "BaseUsageIntro2": "使用 Reload 方法重新载入文件", "Tips": "目前仅支持 Excel(.xlsx) 和 Word(.docx) 格式.<br/><b>特别说明:</b><br/>如果 Linux 下提示 <code>The type initializer for 'Gdip' threw an exception.</code> 错误, 则使用需要安装 libgdiplus 并开启 System.Drawing 支持.<br/> 为非 Windows 平台启用 System.Drawing 支持,在你的项目文件中 (*.csproj), 添加:"}, "BootstrapBlazor.Server.Components.Samples.CountUps": {"Title": "计数器 CountUp", "Description": "多用于报表数据动态展示，数字以跳动的形式展现", "CountUpsNormalTitle": "基础用法", "CountUpsNormalIntro": "通过设置 <code>Value</code> 参数赋值", "Value": "计数结束值", "CountOption": "CountOption 实例值", "OnCompleted": "获得/设置 计数结束回调方法", "StartValue": "开始计数值", "Duration": "动画时长", "UseEasing": "是否使用过渡动画", "DecimalPlaces": "小数位数", "Decimal": "小数点符号", "UseGrouping": "是否分组", "UseIndianSeparators": "是否使用分隔符", "Separator": "分隔符", "Prefix": "前缀文本", "Suffix": "后缀文本", "SmartEasingThreshold": "动画阈值", "SmartEasingAmount": "动画总量", "EnableScrollSpy": "是否启用滚动监听", "ScrollSpyDelay": "滚动延时", "ScrollSpyOnce": "滚动监听一次", "Numerals": "数字符号集合"}, "BootstrapBlazor.Server.Components.Samples.EyeDroppers": {"EyeDropperTitle": "屏幕取色 EyeDropperService", "EyeDropperDescription": "用户可以从屏幕上采样颜色，包括在浏览器窗口之外", "EyeDropperNormalTitle": "基础用法", "EyeDropperNormalIntro": "使用 <code>EyeDropperService</code>，可以启动吸管模式。启动后，光标会发生变化以向用户指示该模式处于活动状态。然后用户可以从屏幕上的任何位置选择一种颜色，通过按 <kbd>Escape</kbd> 关闭吸管模式", "EyeDropperNormalButtonText": "取色", "EyeDropperTips1": "这是一项实验性技术，在生产中使用之前请仔细检查 <a href=\"https://developer.mozilla.org/en-US/docs/Web/API/EyeDropper_API#browser_compatibility\" target=\"_blank\">浏览器兼容性表</a>", "EyeDropperTips2": "为防止恶意网站在用户不知情的情况下从用户屏幕获取像素数据，必须由用户操作（如单击按钮）时调用", "EyeDropperTips3": "为了帮助用户更容易注意到吸管模式，浏览器将其设置为显而易见。短暂延迟后，正常的鼠标光标消失，取而代之的是一个放大镜。在滴管模式启动和用户可以选择像素以确保用户有时间查看放大镜之间也存在延迟", "EyeDropperTips4": "用户也可以随时取消吸管模式（按键 <kbd>Escape</kbd>）"}, "BootstrapBlazor.Server.Components.Pages.Chats": {"ChatsTitle": "Azure OpenAI 服务", "ChatsDescription": "Azure OpenAI 服务是一个基于 Azure 云服务的聊天机器人，可以通过对话的方式与用户进行交互", "ChatNormalTitle": "Azure OpenAI 服务", "ChatNormalIntro": "通过调用 <code>IAzureOpenAIService</code> 服务方法 <code>GetChatCompletionsAsync</code> 进行聊天对话", "ChatUserTitle": "聊天场景", "ChatUserMessageTitle": "你好 {0}", "ChatInfo": "由于 <code>GPT</code> 无免费接口，体验次数为 <b>{1}</b> 次，当前次数为 <b>{0}</b>"}, "BootstrapBlazor.Server.Components.Samples.HtmlRenderers": {"Title": "Html 转化器", "SubTitle": "把组件渲染成 Html 字符串", "BaseUsageText": "基础用法", "IntroText": "有些应用场景需要我们获得 <code>Component</code> 最终输出的 <code>Html</code> 代码片段，调用 <code>IComponentHtmlRenderer</code> 服务的 <code>RenderAsync</code> 实例方法即可"}, "BootstrapBlazor.Server.Components.Samples.ContextMenus": {"ContextMenuItemCopy": "拷贝", "ContextMenuItemPast": "粘贴", "ContextMenuTitle": "ContextMenu 右键菜单", "ContextMenuDescription": "用户点击鼠标右键时弹出的上下文关联菜单", "ContextMenuNormalTitle": "基础用法", "ContextMenuNormalIntro": "点击 <code>ContextMenuZone</code> 内区域右键，弹出上下文关联菜单", "ContextMenuCustomUITitle": "自定义组件", "ContextMenuCustomUIIntro": "点击 <code>Li</code> 弹出上下文关联菜单", "ContextMenuTableTitle": "Table 组件", "ContextMenuTableIntro": "点击 <code>Table</code> 组件行数据右键，弹出上下文关联菜单", "ContextMenuTreeTitle": "Tree 组件", "ContextMenuTreeIntro": "点击 <code>Tree</code> 组件行数据右键，弹出上下文关联菜单", "ContextMenuCallbackTitle": "ContextMenu 回调", "ContextMenuCallbackIntro": "通过设置 <code>ContextMenu</code> 组件参数 <code>OnBeforeShowCallback</code> 获得右键菜单弹出前回调事件，可用于数据准备工作，也可按需渲染菜单", "ContextMenuDisabledTitle": "禁止回调方法", "ContextMenuDisabledIntro": "通过设置 <code>ContextMenuItem</code> 组件参数 <code>OnDisabledCallback</code> 回调方法可用于设置当前右键选项是否禁用逻辑"}, "BootstrapBlazor.Server.Components.Samples.DockViews.Index": {"DockViewTitle": "DockView 可停靠视图", "DockViewDescription": "多标签停靠布局管理器", "DockViewIntro": "<p><code>DockView</code> 组件是封装的 <a href=\"https://github.com/golden-layout/golden-layout?wt.mc_id=DT-MVP-5004174\" target=\"_blank\">Golden Layout</a> 库，它是一个 <code>JavaScript</code> 布局管理器，它使您能够在网页中布局组件并通过拖放重新排列它们。其特点包括：</p><ul class=\"ul-demo\"><li>原生弹出窗口</li><li>触摸支持</li><li>支持 <code>Angular</code>、<code>Vue</code> 等应用框架</li><li>虚拟组件</li><li>全面的API</li><li>加载和保存布局</li><li>焦点组件</li><li>完全主题化</li><li>适用于现代浏览器（<code>Firefox</code>、<code>Chrome</code>）</li><li>响应式设计</li></ul>"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewCol": {"DockViewColTitle": "DockView 列布局", "DockViewColIntro": "通过设置 <code>Type=\"DockContentType.Column\"</code> 使 <code>DockView</code> 垂直布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewRow": {"DockViewRowTitle": "DockView 行布局", "DockViewRowIntro": "通过设置 <code>Type=\"DockContentType.Row\"</code> 使 <code>DockView</code> 水平布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewStack": {"DockViewStackTitle": "DockView 堆栈布局", "DockViewStackIntro": "通过设置 <code>Type=\"DockContentType.Stack\"</code> 使 <code>DockView</code> 堆栈布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewComplex": {"DockViewComplexTitle": "DockView 复杂布局", "DockViewComplexIntro": "通过组合、嵌套设置 <code>DockContentType</code> 值可以得到复杂布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewLayout": {"DockViewLayoutTitle": "DockView 自定义布局", "DockViewLayoutIntro": "通过设置 <code>DockView</code> 的属性 <code>LayoutConfig</code> 初始化控制面板的显示布局, 方法 <code>GetLayoutConfig</code> 获取面板的显示布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewLock": {"DockViewLockTitle": "DockView 锁定面板", "DockViewLockIntro": "通过设置 <code>Dock</code> 的属性 <code>IsLock</code>，控制所有面板是否能拖动"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewVisible": {"DockViewVisibleTitle": "DockView 可见性设置", "DockViewVisibleIntro": "通过设置 <code>Visible</code> 控制是否显示组件，使用 <code>OnVisibleStateChangedAsync</code> 设置面板关闭回调委托方法"}, "BootstrapBlazor.Server.Components.Samples.DockViews.DockViewNest": {"DockViewNestTitle": "DockView 嵌套布局", "DockViewNestIntro": "组件 <code>DockComponent</code> 内可嵌套放置 <code>DockView</code> 通过设置 <code>ShowHeader=\"false\"</code> 可使其标签不可见"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.Index": {"DockView2Title": "DockViewV2 可停靠视图", "DockView2Description": "多标签停靠布局管理器", "DockView2Intro": "<p><code>DockView</code> 组件是封装的 <a href=\"https://github.com/mathuo/dockview?wt.mc_id=DT-MVP-5004174\" target=\"_blank\">DockView</a> 库，它是一个 <code>JavaScript</code> 布局管理器，它使您能够在网页中布局组件并通过拖放重新排列它们。其特点包括：</p><ul class=\"ul-demo\"><li>原生弹出窗口</li><li>触摸支持</li><li>支持 <code>Angular</code>、<code>Vue</code> 等应用框架</li><li>虚拟组件</li><li>全面的API</li><li>加载和保存布局</li><li>焦点组件</li><li>完全主题化</li><li>适用于现代浏览器（<code>Firefox</code>、<code>Chrome</code>）</li><li>响应式设计</li></ul>"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewCol": {"DockViewColTitle": "DockViewV2 列布局", "DockViewColIntro": "通过设置 <code>Type=\"DockViewContentType.Column\"</code> 使 <code>DockViewV2</code> 垂直布局，通过设置 <code>Height=\"240\"</code> 初始化其面板高度"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewRow": {"DockViewRowTitle": "DockViewV2 行布局", "DockViewRowIntro": "通过设置 <code>Type=\"DockViewContentType.Row\"</code> 使 <code>DockViewV2</code> 水平布局，通过设置 <code>Width=\"240\"</code> 初始化其面板宽度"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewGroup": {"DockViewGroupTitle": "DockViewV2 组布局", "DockViewGroupIntro": "通过设置 <code>Type=\"DockViewContentType.Group\"</code> 使 <code>DockViewV2</code> 组布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewComplex": {"DockViewComplexTitle": "DockViewV2 复杂布局", "DockViewComplexIntro": "通过组合、嵌套设置 <code>DockViewContentType</code> 值可以得到复杂布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewLayout": {"DockViewLayoutTitle": "DockViewV2 自定义布局", "DockViewLayoutIntro": "通过设置 <code>DockViewV2</code> 的属性 <code>LayoutConfig</code> 初始化控制面板的显示布局, 方法 <code>GetLayoutConfig</code> 获取面板的显示布局"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewLock": {"DockViewLockTitle": "DockViewV2 锁定面板", "DockViewLockIntro": "通过设置 <code>DockViewV2</code> 的属性 <code>IsLock</code>，控制所有面板是否能拖动，通过设置 <code>ShowLock</code> 控制是否显示锁定按钮"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewTitle": {"DockViewTitleTitle": "DockViewV2 设置标题", "DockViewTitleIntro": "通过设置 <code>ShowTitleBar</code> 控制是否显示标题前功能按钮，使用 <code>OnClickTitleBarCallback</code> 设置回调委托方法，复杂功能使用 <code>TitleTemplate</code> 自定义"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewVisible": {"DockViewVisibleTitle": "DockViewV2 可见性设置", "DockViewVisibleIntro": "通过设置 <code>Visible</code> 控制是否显示组件，使用 <code>OnVisibleStateChangedAsync</code> 设置面板关闭回调委托方法"}, "BootstrapBlazor.Server.Components.Samples.DockViews2.DockViewNest": {"DockViewNestTitle": "DockViewV2 嵌套布局", "DockViewNestIntro": "组件 <code>DockViewComponent</code> 内可嵌套放置 <code>DockViewV2</code> 通过设置 <code>ShowHeader=\"false\"</code> 可使其标签不可见"}, "BootstrapBlazor.Server.Components.Samples.MouseFollowers": {"MouseFollowersTitle": "鼠标跟随器", "MouseFollowersDescription": "可以为网站上的鼠标光标创建惊人而流畅的效果。", "MouseFollowerNormalTitle": "普通模式", "MouseFollowerNormalIntro": "显示一个跟随元素", "MouseFollowerTextTitle": "文本模式", "MouseFollowerTextIntro": "在光标中显示文本", "MouseFollowerIconTitle": "图标模式", "MouseFollowerIconIntro": "在光标中显示SVG图", "MouseFollowerImageTitle": "图片模式", "MouseFollowerImageIntro": "在光标中显示图片", "MouseFollowerVideoTitle": "视频模式", "MouseFollowerVideoIntro": "在光标中播放视频", "MouseFollowersFollowerMode": "MouseFollowerMode.Normal 默认模式，MouseFollowerMode.Text 文本模式，MouseFollowerMode.Icon 图标模式，MouseFollowerMode.Image 图片模式，MouseFollowerMode.Video 视频模式", "MouseFollowersGlobalMode": "全局|局部，模式", "MouseFollowersContent": "文本，图标，图片路径，视频路径"}, "BootstrapBlazor.Server.Components.Samples.ExportPdfButtons": {"ExportPdfButtonsTitle": "将指定网页内容转成 Pdf", "ExportPdfButtonsDescription": "将 <code>Html</code> 片段或者网页元素导出为 <code>Pdf</code>", "ExportPdfButtonTips": "<code>IHtml2Pdf</code> 服务接口请参阅 <a target=\"_blank\" href=\"./html2pdf\">[传送门]</a>", "NormalTitle": "ElementId", "NormalIntro": "导出指定元素 <code>Id</code>", "SelectorTitle": "Selector", "SelectorIntro": "通过指定元素 <code>Selector</code> 导出 Pdf", "GroupBoxTitle": "编辑表单", "ToastTitle": "导出 Pdf", "ToastContent": "正在导出 Pdf 文件", "ToastDownloadTitle": "下载 Pdf", "ToastDownloadContent": "Pdf 文件 {0} 下载完成", "AttributeElementId": "指定导出 Pdf 元素 Id", "AttributeSelector": "指定导出 Pdf 元素选择器", "AttributeStyleTags": "指定导出 Pdf 元素样式表", "AttributeScriptTags": "指定导出 Pdf 元素脚本", "AttributePdfFileName": "指定导出 Pdf 文件名", "AttributeAutoDownload": "是否自动下载 Pdf", "AttributeOnBeforeExport": "导出 Pdf 之前回调委托", "AttributeOnBeforeDownload": "下载 Pdf 之前回调委托", "AttributeOnAfterDownload": "下载 Pdf 之后回调委托"}, "BootstrapBlazor.Server.Components.Samples.Html2Pdfs": {"Html2PdfTitle": "Html 导出为 Pdf", "Html2PdfDescription": "将 Html 片段或者网页元素导出为 Pdf", "Html2PdfNote": "如果导出内容过多，可能会触发 <code>signalR</code> 通讯中断问题，请自行调整 <code>HubOptions</code> 配置即可。", "Html2PdfElementTitle": "导出指定网页元素", "Html2PdfElementIntro": "将 <code>Html</code> 元素内容导出为 <code>Pdf</code> 流", "Html2PdfElementDesc": "调用 <code>IHtml2Pdf</code> 服务实例方法 <code>PdfStreamFromHtmlAsync</code> 将网页 <code>Html</code> 导出为 <code>Pdf</code>", "ExportPdfButtonTitle": "使用 ExportPdfButton 导出按钮", "ExportPdfButtonIntro": "通过设置 <code>ExportPdfButton</code> 组件参数 <code>ElementId</code> 将指定 <code>Id</code> 元素内容导出为 <code>Pdf</code>", "ExportButtonText": "导出 Pdf", "ToastTitle": "Pdf 导出", "ToastContent": "通过表格 Id 导出 Pdf 文件成功", "Tips1": "实现原理", "Tips2": "服务", "PackageIntro": "组件默认未实现 <code>IHtml2Pdf</code>，请通过引用包 <code>BootstrapBlazor.Html2Pdf</code> 实现", "Html2PdfIntro1": "1. 通过 <code>getHtml</code> 脚本获取网页元素 <code>Html</code> 代码", "Html2PdfIntro2": "2. 将 <code>Html</code> 代码，通过模板生成网页代码，<code>注意</code> 需要提供网页需要的样式表 <code>_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css</code>", "Html2PdfIntro3": "3. 通过 <code>IHtml2Pdf</code> 服务实例方法 <code>PdfStreamFromHtmlAsync</code> 将网页 转化为 <code>Pdf</code>", "Html2PdfIntro4": "4. <code>IHtml2Pdf</code> 实现服务使用 <code>PuppeteerSharp</code> 将网页 转化为 <code>Pdf</code>，首次使用时会下载 <code>Chromium</code> 运行时，网络不好的情况下可能会很慢"}, "BootstrapBlazor.Server.Components.Samples.Live2DDisplays": {"Live2DDisplayTitle": "Live2D 插件", "Live2DDisplayDescription": "基于 pixi-live2d-display 的 Live2D 插件，支持所有版本的 Live2D 模型。", "Live2DDisplayNormalTitle": "基本用法", "Live2DDisplayNormalIntro": "通过调节默认的参数选项，实现模型的动态变化。", "Live2DDisplayNormalTips": "添加以下代码到Program.cs中。", "Live2DDisplaysSource": "模型数据源，cdn路径或者本地路径。", "Live2DDisplaysScale": "模型显示比例。", "Live2DDisplaysXOffset": "模型在canvas画板上的X轴偏移。", "Live2DDisplaysYOffset": "模型在canvas画板上的Y轴偏移。", "Live2DDisplaysIsDraggable": "模型是否可以拖动（暂未实现）。", "Live2DDisplaysAddHitAreaFrames": "显示模型可点击区域框。", "Live2DDisplaysPosition": "模型默认显示位置枚举。", "Live2DDisplaysBackgroundColor": "canvas画板背景颜色。", "Live2DDisplaysBackgroundAlpha": "canvas画板背景是否透明。"}, "BootstrapBlazor.Server.Components.Samples.SlideButtons": {"SlideButtonTitle": "SlideButton 抽屉式弹窗", "SlideButtonIntro": "点击按钮后弹出功能菜单等信息，多用于二级功能展示", "SlideButtonHeaderText": "Foo 数据一览", "BaseUsageText": "基本用法", "BaseUsageIntro": "通过设置 <code>Items</code> 给出二级功能菜单数据项", "ShowHeaderText": "显示标题", "ShowHeaderIntro": "通过设置 <code>ShowHeader</code> 控制是否显示一个标题栏，默认值为 <code>false</code>", "AutoCloseText": "自动关闭", "AutoCloseIntro": "通过设置 <code>IsAutoClose</code> 控制是否点击弹窗以外区域自动关闭弹窗", "SlideButtonItemsText": "选项集合", "SlideButtonItemsIntro": "在页面中直接录入 <code>SlideButtonItems</code> 集合当数据源", "BodyTemplateText": "自定义模板", "BodyTemplateIntro": "通过设置 <code>BodyTemplate</code> 自定义弹窗内数据"}, "BootstrapBlazor.Server.Components.Samples.DialButtons": {"DialButtonTitle": "DialButton 拨号按钮", "DialButtonIntro": "点击按钮后弹出功能菜单等信息，多用于二级功能展示", "BaseUsageText": "基本用法", "BaseUsageIntro": "通过设置 <code>Placement</code> 枚举值设置弹窗与按钮位置关系"}, "BootstrapBlazor.Server.Components.Samples.CountButtons": {"Title": "CountButton 倒计时按钮", "Description": "多用于强制一段执行时间逻辑，如发送验证码后 60 秒等待输入", "BaseUsageText": "基本用法", "BaseUsageIntro": "通过 <code>Count</code> 参数值设置倒计时时间，默认 <b>5</b> 秒", "TextTitle": "计时文本", "TextIntro": "通过 <code>CountText</code> 参数值设置倒计时文本", "Text": "发送验证码", "CountButtonText": "{0} 秒后重试", "Count": "倒计时数量", "CountText": "倒计时文本", "CountTextCallback": "倒计时格式化回调方法"}, "BootstrapBlazor.Server.Components.Samples.QueryBuilders": {"QueryBuilderTitle": "条件生成器 QueryBuilder", "QueryBuilderSubTitle": "可用于表单、表格过滤条件生成", "QueryBuilderNormalTitle": "基本用法", "QueryBuilderNormalIntro": "在 <code>Razor</code> 文件中直接通过 <code>QueryGroup</code> <code>QueryColumn</code> 构建过滤条件", "QueryBuilderHeaderTitle": "显示控制按钮", "QueryBuilderHeaderIntro": "通过设置 <code>ShowHeader=\"false\"</code> 关闭控制按钮"}, "BootstrapBlazor.Server.Components.Samples.WebSerials": {"WebSerialTitle": "WebSerial 串口读写", "WebSerialIntro": "串口是一种具有广泛使用的通信协议，它可以实现计算机之间的数据传输", "WebSerialDescription": "允许网站与连接到用户计算机的外围设备进行通信。它提供了连接到操作系统需要通过串行 API 进行通信的设备的能力", "WebSerialNormalTitle": "基本用法", "WebSerialNormalIntro": "通过调用 <code>ISerialService</code> 服务，对串口设备进行连接、打开、关闭、读写操作", "BaudRateText": "波特率", "DataBitsText": "数据位", "StopBitsText": "停止位", "ParityTypeText": "校验位", "BufferSizeText": "缓冲区", "FlowControlTypeText": "流控制", "RequestPortText": "选择", "OpenPortText": "打开", "ClosePortText": "关闭", "WriteButtonText": "写入", "WriteDataText": "发送数据", "ReadDataText": "接收数据", "CRLFText": "末尾加回车换行", "HEXText": "HEX 发送", "LoopSendText": "循环发送", "LoopIntervalText": "发送间隔(ms)", "WebSerialTipsLi1": "该功能仅在部分或所有支持浏览器的安全上下文(HTTPS)中可用", "WebSerialTipsLi2": "这是一项实验性技术，在生产中使用之前请仔细,检查 <a href=\"https://developer.mozilla.org/en-US/docs/Web/API/SerialPort#browser_compatibility\" target=\"_blank\">浏览器兼容性表</a>", "WebSerialTipsTitle": "注意：<code>ISerialPort</code> 接口实例继承 <code>IAsyncDisposable</code> 路由切换时需要对其进行资源释放，调用其 <code>DisposeAsync</code> 即可", "NotSupportSerialTitle": "申请串口权限", "NotSupportSerialContent": "当前浏览器不支持串口操作，请更换 Edge 或者 Chrome 浏览器", "OpenPortSerialTitle": "打开串口操作", "OpenPortSerialContent": "打开串口失败", "GetSignalsButtonText": "串口参数", "GetInfoButtonText": "USB 信息"}, "BootstrapBlazor.Server.Components.Samples.MindMaps": {"MindMapTitle": "Mind Map 思维导图", "MindMapDescription": "用于将特定 Json 格式数据展示成 Web 思维导图", "MindMapNormalTitle": "基本用法", "MindMapLayout": "布局", "MindMapTheme": "主题", "Sample1ButtonText": "普通示例", "Sample2ButtonText": "周安排示例", "ExportButtonText": "导出 PNG", "ExportJsonButtonText": "导出 <PERSON><PERSON>", "GetFullDataButtonText": "GetFullData", "GetDataButtonText": "GetData", "SetDataButtonText": "SetData", "ResetButtonText": "复位", "FitButtonText": "自适应", "Scale1ButtonText": "缩小", "Scale2ButtonText": "放大", "CustomButtonText": "自定义", "Data": "初始数据", "Export": "下载为文件", "GetData": "获取数据", "SetData": "导入数据", "Reset": "复位", "SetTheme": "切换主题", "SetLayout": "切换布局", "MindMapExtensionDesc": "<p>由于 <code>MindMap</code> 封装的 <b>api</b> 方法比较多，组件库无法完全封装，所以提供了扩展方法。例如使用组件需要一个组合按钮，可以通过自己的代码调用自己的 <code>Javascript</code> 来控制 <code>MindMap</code></p><ul><li>自定义按钮调用组件实例方法 <code>MindMap.Execute(\"clickCustom\", \"args1\")</code>，假设参数为 <code>args1</code></li><li>自己实现 <code>Javascript</code> 负责处理 <code>clickCustom</code> 事件</li><li><code>clickCustom</code> 方法内 <code>this</code> 即为 <code>MindMap</code> 当前实例，可以调用其任意方法进行自己业务开发</li></ul><p>完整示例如下：</p>"}, "BootstrapBlazor.Server.Components.Samples.Mermaids": {"MermaidTitle": "Mermaid 构图工具", "MermaidDescription": "本组件可渲染 Markdown 启发的文本定义以动态创建和修改图表。", "MermaidNormalTitle": "基本用法", "MermaidNormalIntro": "Mermaid 基本样式", "MermaidStyleTitle": "增加自定义样式", "MermaidStyleIntro": "", "MermaidType": "图表类型"}, "BootstrapBlazor.Server.Components.Samples.Speeches.WebSpeeches": {"WebSpeechTitle": "Web Speech Api 网页原生语音处理 API", "WebSpeechSubTitle": "使用浏览器接口功能提供语音识别/合成服务", "WebSpeechNormalTitle": "语音合成", "WebSpeechNormalIntro": "输入文字后选择相对应的语种进行朗读", "WebSpeechSpeakButtonText": "朗读", "WebSpeechStopButtonText": "停止", "WebSpeechText": "开始朗读一段文字", "WebSpeechRecognitionTitle": "语音识别", "WebSpeechRecognitionIntro": "通过麦克风输入语音，进行语音识别", "WebSpeechRecognitionButtonText": "语音识别", "WebSpeechRecognitionContinuousTitle": "实时语音识别", "WebSpeechRecognitionContinuousIntro": "通过设置 <code>WebSpeechRecognitionOption</code> 参数 <code>Continuous=\"true\" InterimResults=\"true\"</code> 进行实时语音识别", "WebSpeechRecognitionContinuousButtonText": "持续识别", "RecognitionErrorNotAllowed": "用户已拒绝访问硬件设备", "RecognitionErrorNoSpeech": "未检测到语音", "RecognitionErrorAborted": "用户已取消", "RecognitionErrorAudioCapture": "硬件设备无法捕获音频", "RecognitionErrorNetwork": "网络错误", "RecognitionErrorNotSupported": "浏览器不支持语音识别", "RecognitionErrorServiceNotAllowed": "服务不允许", "RecognitionErrorBadGrammar": "语法错误", "RecognitionErrorLanguageNotSupported": "语言不支持"}, "BootstrapBlazor.Server.Components.Samples.ListGroups": {"ListGroupsTitle": "ListGroup 列表框", "ListGroupsSubTitle": "多用于数据浏览与选择", "ListGroupsTips": "<li>通过 <code>Items</code> 设置列表框显示数据集合</li><li>通过 <code>GetItemDisplayText</code> 设置列表框数据项显示文本</li><li>由于组件内部高度设置为 <code>100%</code> 使用时自动充满父容器，当父容器设置高度时，列表项自动出现滚动条，本例中设置外部容器元素 <code>list-group-demo</code> 高度为 <code>280px</code></li>", "BasicUsageTitle": "基本用法", "BasicUsageIntro": "通过 <code>Items</code> 参数值设置数据集合", "HeaderTextTitle": "标题", "HeaderTextIntro": "通过 <code>HeaderText</code> 参数值设置标题文字", "HeaderText": "列表框标题文字", "HeaderTemplateTitle": "标题模板", "HeaderTemplateIntro": "通过 <code>HeaderTemplate</code> 自定义标题模板", "AttrItems": "数据源集合", "AttrValue": "当前选中值", "AttrHeaderTemplate": "标题栏模板", "AttrHeaderText": "标题栏文字", "AttrItemTemplate": "选项模板", "AttrOnClickItem": "点击候选项回调方法", "GetItemDisplayText": "获得显示项显示内容回调方法"}, "BootstrapBlazor.Server.Components.Samples.Marquees": {"MarqueeTitle": "循环滚动组件", "MarqueeDescription": "组件多用于创建滚动文本，通常用于新闻滚动、广告滚动等效果", "MarqueeBase": "基本用法", "MarqueeBaseIntro": "通过参数调节文字滚动效果"}, "BootstrapBlazor.Server.Components.Samples.Stacks": {"Title": "Stack 布局", "BasicTitle": "普通用法", "BasicIntro": "可用于在水平或垂直堆栈中排列其子组件", "RowMode": "行布局", "ColumnMode": "列布局", "Mode": "布局类型", "Justify": "水平对齐方式", "AlignItems": "垂直对齐方式", "IsWrap": "是否折行", "IsReverse": "是否反转", "AlignSelf": "子项对齐方式", "StackItemGrowShrinkDesc": "<code>StackItem</code> 子项还有两个控制方式:", "StackItemGrowShrinkLi": "<li><b>Grow</b> 是否尽可能多的占用剩余空间 <code>flex-grow-0</code> <code>flex-grow-1</code></li><li><b>Shrink</b> 是否尽可能的把空间让给前面兄弟元素 <code>flex-shrink-0</code> <code>flex-shrink-1</code></li>", "AttrIsRow": "是否为行布局", "AttrIsWrap": "是否允许折行", "AttrIsReverse": "是否反向布局", "AttrJustify": "水平布局调整", "AttrAlignItems": "垂直布局模式", "AttrChildContent": "内容模板"}, "BootstrapBlazor.Server.Components.Samples.Segmenteds": {"Title": "Segmented 分段控制器", "SubTitle": "展示多个选项并允许用户选择其中单个选项", "BasicTitle": "基本", "BasicIntro": "通过参数 <code>Items</code> 设置组件数据源", "IsDisabledTitle": "不可用", "IsDisabledIntro": "使其中的一项禁止使用", "ItemTemplateTitle": "自定义渲染", "ItemTemplateIntro": "使用 <code>ItemTemplate</code> 自定义渲染", "IconTitle": "图标", "IconIntro": "设置 <code>Icon</code> 参数值改变其现实图标", "SizeTitle": "大小", "SizeIntro": "组件定义了三种尺寸（大、默认、小），高度分别为 <code>40px</code>、<code>32px</code> 和 <code>24px</code>。", "SegmentItemTitle": "SegmentItem 组件", "SegmentItemIntro": "可在 <code>Razor</code> 页面中直接书写 <code>SegmentItem</code> 添加数据源", "BlockTitle": "Block 充满父容器", "BlockIntro": "通过参数 <code>IsBlock</code> 可将组件充满父容器", "ItemsAttr": "数据源", "ValueAttr": "值", "ValueChangedAttr": "分段选择器选项改变时触发此事件", "OnValueChanged": "值改变回调委托方法", "SizeAttr": "设置组件大小", "IsDisabledAttr": "是否禁用", "ItemTemplateAttr": "设置 SegmentedItem 模板", "ChildContent": "组件子内容", "IsBlockAttr": "是否充满父容器", "ShowTooltipAttr": "超长被截断的 Item 是否显示 Tooltip", "ChildContentAttr": "子组件"}, "BootstrapBlazor.Server.Components.Samples.CodeEditors": {"Title": "代码编辑器", "BasicTitle": "基础用法", "BasicIntro": "基础用法", "Value": "获得或设置 Value", "Theme": "编辑器的主题", "Language": "编辑器使用的语言：csharp，JavaScript，...", "ValueChanged": "获取或设置更新绑定值的回调"}, "BootstrapBlazor.Server.Components.Samples.JSRuntimeExtensions": {"JSTips": "使用前，先按照以下代码导入模块。", "OpenUrlTitle": "OpenUrl", "OpenUrlIntro": "在特定窗口打开连接", "OpenUrlTips": "默认为:", "IsMobileTitle": "IsMobile", "IsMobileIntro": "通过正则表达式判断 <code>userAgent</code>，表示当前设备是否为移动设备", "EvalTitle": "Eval", "EvalIntro": "通过 <code>Eval</code> 函数，在当前作用域内动态运行 <code>JavaScript</code> 代码。", "FunctionTitle": "Function", "FunctionIntro": "通过 <code>Function</code> 函数，在全局作用域内动态运行 <code>JavaScript</code> 代码。", "OpenUrlAttr": "在特定窗口打开连接", "IsMobileAttr": "判断当前设备是否为移动设备", "EvalAttr": "调用 Eval 方法", "FunctionAttr": "调用 Function 方法"}, "BootstrapBlazor.Server.Components.Samples.Clipboards": {"ClipboardTitle": "ClipboardService", "ClipboardIntro": "剪切板服务！请注意，只能在 <code>HTTPS</code> 安全连接下运行，或者在本地 <code>localhost</code> 开发环境中使用", "ClipboardMessage": "文本内容：{0}, 已复制到剪切板", "ClipboardGetTextMessage": "读取剪切板文本内容: {0}", "ClipboardCopyMethod": "拷贝文本到剪切板方法", "ClipboardGetMethod": "读取剪切板内容方法", "ClipboardGetTextMethod": "读取剪切板文本内容方法"}, "BootstrapBlazor.Server.Components.Samples.Gantts": {"Title": "一个简单、交互式、现代的 Web 甘特图库，具有拖动、调整大小、依赖关系和时间刻度", "BasicTitle": "基础用法", "BasicDesc": "在时间线上拖动任务，调整大小以更改持续时间，单击以查看更多信息", "ViewModeTitle": "改变视图", "ViewModeDesc": "切换视图模式调用 <code>change_view_mode</code>", "AttrItems": "数据源", "AttrOnClick": "点击任务时触发的回调", "AttrOnDataChanged": "拖动任务时触发的回调", "AttrOnProgressChanged": "拖动任务进度时触发的回调", "AttrOption": "配置项", "AttrMethod": "改变甘特图视图"}, "BootstrapBlazor.Server.Components.Samples.ImageCroppers": {"Title": "ImageCropper 图像裁剪", "ImageCropperNormalText": "基础用法", "ImageCropperNormalIntro": "通过设置 <code>Url</code> 参数设置图片地址，可通过设置 <code>ImageCropperOption.AspectRatio=16/9f</code> 设置裁剪框比例，<code>1</code> 时为正方形", "ImageCropperRoundText": "圆角", "ImageCropperRoundIntro": "通过设置 <code>IsRound</code> 参数设置裁剪方式为圆形", "ImageCropperResetText": "复位", "ImageCropperReplaceText": "替换", "ImageCropperRotateText": "旋转", "ImageCropperEnableText": "启用", "ImageCropperDisabledText": "禁用", "ImageCropperClearText": "清除", "AttributesImageCropperUrl": "图片地址", "AttributesImageCropperIsDisabled": "是否被禁用", "AttributesImageCropperOnCropAsync": "剪裁结果回调方法", "AttributesImageCropperOptions": "裁剪选项", "AttributesImageCropperShape": "裁剪形状"}, "BootstrapBlazor.Server.Components.Samples.Translators": {"TranslatorsTitle": "AzureTranslator 翻译服务", "TranslatorsDescription": "将源语言的字符或字母转换为目标语言的对应字符或字母", "TranslatorsNormalTitle": "基础用法", "TranslatorsNormalIntro": "通过调用 <code>TranslateAsync</code> 进行文本翻译", "TranslatorsInjectService": "注入服务", "TranslatorsTranslate": "翻译"}, "BootstrapBlazor.Server.Components.Samples.BarcodeGenerators": {"Title": "BarcodeGenerator 条码生成器", "BarcodeGeneratorNormalText": "条码生成器", "BarcodeGeneratorNormalIntro": "通过给定 <code>Value</code> 值渲染成相对应格式的一维条码", "OnCompletedAsync": "条码生成(svg)回调方法", "Options": "条码选项", "Format": "条码类型", "Value": "条码值", "Width": "单个条形的宽度", "Height": "条形码的高度", "DisplayValue": "显示条码文字", "Text": "覆盖显示的文本", "FontOptions": "字体式样", "Font": "字体", "TextAlign": "文本对齐", "TextPosition": "文字位置", "TextMargin": "文本边距", "FontSize": "字体大小", "Background": "背景色", "LineColor": "线条颜色", "Margin": "间距", "MarginTop": "顶部间距", "MarginBottom": "底部间距", "MarginLeft": "左侧间距", "MarginRight": "右侧间距", "Flat": "底线平整 (仅EAN8/EAN13)"}, "BootstrapBlazor.Server.Components.Samples.ZipArchives": {"ZipArchivesTitle": "IZipArchive 压缩归档服务", "ZipArchivesSubTitle": "组件内置服务，可将文件或者文件夹压缩为指定名称的归档文件", "ZipArchiveInjectText": "注入服务", "ZipArchiveFileText": "归档文件", "ZipArchiveDirectoryText": "归档文件夹", "ZipArchiveExtractText": "解压缩到指定文件夹"}, "BootstrapBlazor.Server.Components.Samples.Tutorials.Translation.Translator": {"File": "文件", "Hide": "隐藏", "Language": "语言", "Load": "加载", "LoadContent": "所有翻译项已读取成功", "LoadTitle": "读取", "Operation": "操作", "Save": "保存", "SaveContent": "所有翻译项已保存成功", "SaveTitle": "保存", "Show": "显示", "ToolboxCardTitle": "工具栏", "Translate": "机翻"}, "BootstrapBlazor.Server.Components.Samples.SelectTables": {"Title": "TableSelect 表格选择器", "Intro": "下拉框为表格用于展示复杂类型的选择需求", "NormalTitle": "基本功能", "NormalIntro": "适用于候选项信息量比较大，用 <code>Table</code> 呈现信息量", "NormalDesc": "可通过 <code>IsClearable</code> 控制是否显示清除小按钮，默认值 <code>false</code>", "ColorTitle": "颜色", "ColorIntro": "通过设置 <code>Color</code> 改变组件边框颜色", "IsDisabledTitle": "禁用", "IsDisabledIntro": "设置 <code>IsDisabled</code> 属性值为 <code>true</code> 时，组件禁止选择", "TemplateTitle": "显示模板", "TemplateIntro": "通过自定义 <code>Template</code> 模板，呈现定制化显示内容", "AttributeItems": "数据源表格显示内容集合", "AttributeTableColumns": "设置表格显示列集合", "AttributeColor": "颜色", "AttributeIsDisabled": "是否禁用", "AttributeShowAppendArrow": "是否显示右侧扩展小箭头", "AttributeGetTextCallback": "获得显示值回调方法", "AttributePlaceHolder": "占位符", "AttributeHeight": "表格高度", "AttributeTableMinWidth": "弹窗表格最小宽度", "AttributeTemplate": "显示模板", "ValidateFormTitle": "客户端验证", "ValidateFormIntro": "下拉框未选择时，点击提交按钮时拦截。", "SortableTitle": "排序过滤", "SortableIntro": "通过设置 <code>TableColumn</code> 的 <code>Sortable</code> <code>Filterable</code> 开启排序与过滤功能，与组件 <code>Table</code> 一样", "SearchTitle": "搜索分页", "SearchIntro": "通过设置 <code>TableColumn</code> 的 <code>Searchable</code> 开启列可搜索功能，与组件 <code>Table</code> 一样，通过设置 <code>IsPagination</code> <code>ShowSearch</code> 开启分页与表格搜索功能"}, "BootstrapBlazor.Server.Components.Samples.SelectTables+SelectTableMode": {"Foo.Required": "{0} 是必填项"}, "BootstrapBlazor.Server.Components.Samples.SelectObjects": {"Title": "TableObject 任意选择器", "Intro": "下拉框为任意组件用于展示复杂类型的选择需求", "NormalTitle": "基本功能", "NormalIntro": "内置 <code>ListView</code> 组件选择图片", "NormalDesc": "可通过 <code>IsClearable</code> 控制是否显示清除小按钮，默认值 <code>false</code>", "MinWidthTitle": "设置最小宽度", "MinWidthIntro": "通过设置 <code>DropdownMinWidth</code> 值，来改变下拉框最小宽度", "HeightTitle": "设置高度", "HeightIntro": "通过设置 <code>Height</code> 值，来改变下拉框高度", "CustomComponentTitle": "自定义组件", "CustomComponentIntro": "任意组件均可放入下拉框内，需要有一个回调进行赋值或者关窗即可"}, "BootstrapBlazor.Server.Components.Samples.BrowserFingers": {"BrowserFingerTitle": "浏览器指纹", "BrowserFingerIntro": "通过调用 <code>IBrowserFingerService</code> 服务实例方法 <code>GetFingerCodeAsync</code> 获得客户端浏览器指纹，隐私模式下指纹是一致的", "GetFingerCodeAsync": "获得指纹方法"}, "BootstrapBlazor.Server.Components.Samples.SvgEditors": {"SvgEditorTitle": "Svg 编辑器", "BasicTitle": "Svg 编辑器", "BasicIntro": "用于 Svg 编辑"}, "BootstrapBlazor.Server.Components.Samples.FlipClocks": {"FlipClocksTitle": "FlipClock 卡片翻转时钟", "FlipClocksDescription": "用于网站计时，或者倒计时使用", "CountText": "计时器", "CountIntro": "通过设置 <code>ViewMode=\"FlipClockViewMode.Count\"</code> 开启计时功能，开始时间使用 <code>StartValue</code> 设置", "IsCountDownText": "倒计时", "IsCountDownIntro": "通过设置 <code>ViewMode=\"FlipClockViewMode.CountDown\"</code> 用于倒计时功能，开始时间使用 <code>StartValue</code> 设置，倒计时结束时触发 <code>OnCompletedAsync</code> 回调方法", "CustomText": "自定义参数", "CustomIntro": "通过设置参数自定义显示内容", "Custom": "参数设置", "Height": "高度", "FontSize": "字体大小", "CardHeight": "卡片高度", "CardWidth": "卡片宽度", "CardMargin": "卡片边距", "CardGroupMargin": "卡片组边距", "ShowDay_Description": "是否显示日", "ShowHour_Description": "是否显示小时", "ShowMinute_Description": "是否显示分钟", "ShowMonth_Description": "是否显示月", "ShowYear_Description": "是否显示年", "ViewMode_Description": "显示模式", "StartValue_Description": "开始时间", "OnCompletedAsync_Description": "计时结束回调方法", "Height_Description": "高度", "BackgroundColor_Description": "背景色", "FontSize_Description": "字体大小", "CardWidth_Description": "卡片宽度", "CardHeight_Description": "卡片高度", "CardColor_Description": "卡片字体颜色", "CardBackgroundColor_Description": "卡片背景颜色", "CardDividerHeight_Description": "卡片分割线高度", "CardDividerColor_Description": "卡片分割线颜色", "CardMargin_Description": "卡片间隔", "CardGroupMargin_Description": "卡片组间隔"}, "BootstrapBlazor.Server.Components.Samples.Icons.BootstrapIcons": {"Title": "Bootstrap Icons", "BaseUsageText": "Bootstrap 免费开源图标库", "Icons": "图标列表请通过 <a href=\"https://icons.getbootstrap.com\" target=\"_blank\">[传送门]</a> 查看", "P1": "通过引用扩展组件包", "P2": "后添加样式表文件链接开启图标"}, "BootstrapBlazor.Server.Components.Samples.Icons.FluentSystemIcons": {"Title": "Fluent System Icons", "BaseUsageText": "Microsoft 免费开源图标库", "Icons": "图标列表请通过 <a href=\"https://react.fluentui.dev/?path=/docs/icons-catalog--docs\" target=\"_blank\">[传送门]</a> 查看", "P1": "通过引用扩展组件包", "P2": "后添加样式表文件链接开启图标"}, "BootstrapBlazor.Server.Components.Samples.Icons.ElementIcon.ElementIcons": {"ElementIconTitle": "饿了么图标库", "ElementIconDescription": "饿了么免费开源图标库", "CopiedTooltipText": "拷贝成功", "P1": "1. 引用扩展组件包后添加包内置样式文件开启图标", "P2": "2. 开启样式隔离，编译后自动引用组件样式", "P3": "请拷贝下方样式即可", "Icons": "图标列表请通过 <a href=\"https://element.eleme.cn/#/zh-CN/component/icon\" target=\"_blank\">[传送门]</a> 查看"}, "BootstrapBlazor.Server.Components.Samples.Icons.FAIcons": {"Title": "Font Awesome Icons", "BaseUsageText": "目前 <code>BootstrapBlazor</code> 使用 <code>Font Awesome</code> 最新版本作为内置图标库，所有图标如下", "SwitchButtonTextOff": "点击拷贝", "SwitchButtonTextOn": "显示高级拷贝", "P1": "通过引用扩展组件包", "P2": "后添加样式表文件链接开启图标", "CopiedTooltipText": "拷贝成功", "Icons": "图标列表请通过 <a href=\"https://fontawesome.com/icons\" target=\"_blank\">[传送门]</a> 查看"}, "BootstrapBlazor.Server.Components.Samples.Icons.MaterialDesignIcons": {"Title": "MaterialDesign Icons", "BaseUsageText": "Material Design 图标简单、现代、友好", "Icons": "图标列表请通过 <a href=\"https://pictogrammers.com/library/mdi\" target=\"_blank\">[传送门]</a> 查看", "P1": "通过引用扩展组件包", "P2": "后添加样式表文件链接开启图标"}, "BootstrapBlazor.Server.Components.Samples.Icons.IconPark.IconParks": {"IconParkTitle": "字节跳动图标库", "IconParkDescription": "已覆盖字节跳动商业化产品系所有平台，并被12个平台作为底层代码引入使用，保证了图标样式与认知的统一性", "CopiedTooltipText": "拷贝成功", "P1": "1. 引用扩展组件包后添加包内置样式文件开启图标", "P2": "2. 开启样式隔离，编译后自动引用组件样式", "P3": "请拷贝下方样式即可", "Icons": "图标列表请通过 <a href=\"https://iconpark.oceanengine.com/official\" target=\"_blank\">[传送门]</a> 查看"}, "BootstrapBlazor.Server.Components.Samples.Icons.AntDesign.AntDesignIcons": {"AntDesignIconTitle": "蚂蚁图标库", "AntDesignIconDescription": "语义化的矢量图形", "CopiedTooltipText": "拷贝成功", "P1": "1. 引用扩展组件包后添加包内置样式文件开启图标", "P2": "2. 开启样式隔离，编译后自动引用组件样式", "P3": "请拷贝下方样式即可", "Icons": "图标列表请通过 <a href=\"https://ant.design/components/icon\" target=\"_blank\">[传送门]</a> 查看"}, "BootstrapBlazor.Server.Components.Samples.Icons.OctIcon.OctIcons": {"OctIconTitle": "Github 图标库", "OctIconDescription": "Primer is a set of guidelines, principles, and patterns for designing and building UI at GitHub.", "CopiedTooltipText": "拷贝成功", "P1": "1. 引用扩展组件包后添加包内置样式文件开启图标", "P2": "2. 开启样式隔离，编译后自动引用组件样式", "P3": "请拷贝下方样式即可", "Icons": "图标列表请通过 <a href=\"https://primer.style/foundations/icons\" target=\"_blank\">[传送门]</a> 查看"}, "BootstrapBlazor.Server.Components.Samples.Icons.UniverIcon.UniverIcons": {"UniverIconTitle": "Univer Icon 图标库", "UniverIconDescription": "Icons used by Univer", "CopiedTooltipText": "拷贝成功", "P1": "1. 引用扩展组件包后添加包内置样式文件开启图标", "P2": "2. 开启样式隔离，编译后自动引用组件样式", "P3": "请拷贝下方样式即可", "Icons": "图标列表请通过 <a href=\"https://univer.ai/en-US/icons\" target=\"_blank\">[传送门]</a> 查看"}, "BootstrapBlazor.Server.Components.Samples.BootstrapBlazorIcons": {"IconsTitle": "Icon 图标", "IconsDescription": "同时支持字体图标、矢量 Svg 图标、以及 Image 图片", "FATitle": "字体图标", "FAIntro": "通过设置 <code>Name</code> 指定图标样式即可", "SvgTitle": "矢量图标", "SvgIntro": "通过设置 <code>IsSvgSprites=\"true\"</code> <code>Url</code> 加载 <code>Svg</code> 雪碧图，通过 <code>Name</code> 指定其 <code>Id</code> 值", "ImageTitle": "图片", "ImageIntro": "通过 <code>ChildContent</code> 模板自定义显示内容", "Icons": "图标列表请通过 <a href=\"https://icons.getbootstrap.com\" target=\"_blank\">[传送门]</a> 查看"}, "BootstrapBlazor.Server.Components.Samples.DriverDotnetJs": {"DriverJsPopoverTitleText": "高亮向导组件演示", "DriverJsPopoverContentText1": "这里是菜单，点击后右侧面板出现详细示例", "DriverJsPopoverContentText2": "这里是组件介绍，通过点击下方单选框可切换图标主题", "DriverJsPopoverContentText3": "这里是示例区域，此区域内展示具体例子", "DriverJsPopoverContentText4": "这里是示例区域", "DriverJsPopoverContentText5": "点击这里开始", "DriverJsTitle": "DriverJs 高亮向导组件", "DriverJsSubtitle": "引导用户的注意力穿过整个页面", "DriverJsNormalTitle": "基础用法", "DriverJsNormalIntro": "通过简单配置点击按钮开始用户步骤指导", "DriverJsNormalDesc": "通过设置 <code>DriverJsConfig</code> 参数 <code>Animate=\"false\"</code> 关闭动画效果", "DriverJsPopoverTitle": "弹窗配置", "DriverJsPopoverIntro": "通过设置 <code>DriverJsPopover</code> 组件配置 <code>Popover</code> 相关配置信息", "DriverJsPopoverDesc": "详细参数信息请查看 <a href=\"https://driverjs.com/docs/configuration\" target=\"_blank\">[传送门]</a>", "DriverJsPopoverStyleTitle": "弹窗样式", "DriverJsPopoverStyleIntro": "通过设置 <code>PopoverClass=\"driverjs-theme\"</code> 通过自定义样式 <code>driverjs-theme</code> 设置弹窗 UI", "DriverJsDestroyTitle": "销毁回调方法", "DriverJsDestroyIntro": "销毁前回调方法 <code>OnBeforeDestroyAsync</code> 或者销毁回调方法 <code>OnDestroyedAsync</code>", "DriverJsDestroyDesc": "当用户尝试退出游览时，可以使用 <code>OnBeforeDestroyAsync</code> 回调添加销毁前逻辑，返回 <b>非空字符串</b> 时客户端弹窗二次确认是否阻止销毁；可通过设置 <code>AllowClose</code> 阻止用户退出向导", "DriverJsHighlightTitle": "高亮显示", "DriverJsHighlightIntro": "通过调用 <code>DriverJs</code> 组件实例方法 <code>Highlight</code> 使指定元素高亮聚焦显示"}, "BootstrapBlazor.Server.Components.Samples.IntersectionObservers": {"IntersectionObserverTitle": "IntersectionObserver 交叉观察器", "IntersectionObserverDescription": "通过滚动使元素可见使触发组件回调，多用于数据懒加载功能", "IntersectionObserverBaseUsage": "基础用法", "IntersectionObserverNormalIntro": "通过设置 <code>Root</code> <code>RootMargin</code> <code>Threshold</code> 参数值，监听元素与根元素交叉变化", "IntersectionObserverNormalDescription": "本例加载 100 张图片，不可见图片加载默认图片（缓存图片性能高），当滚动到可见区域时，加载真实图片", "IntersectionObserverLoadTitle": "触底加载", "IntersectionObserverLoadIntro": "滚动到底后，出现加载更多指示，通知后台加载数据", "IntersectionObserverLoadDesc1": "通过设置 <code>Threshold=\"1.0\"</code> 约束正在加载指示符完全可见时，执行加载更多操作", "IntersectionObserverLoadDesc2": "通过设置 <code>AutoUnobserve=\"false\"</code> 保证始终检测正在加载指示符可见性，防止可见后自动移除检测", "IntersectionObserverLoadDesc3": "通过设置 <code>OnLoadMoreAsync</code> 方法模拟网络延时 1s", "IntersectionObserverVisibleTitle": "触发阈值", "IntersectionObserverVisibleIntro": "通过设置 <code>Threshold</code> 调整触发条件", "IntersectionObserverVisibleDesc": "本例中设置 <code>Threshold=\"1\"</code> 即触发条件为视频完全可见时播放，否则自动暂停", "IntersectionObserverVisiblePauseLabel": "视频已暂停", "IntersectionObserverVisiblePlayLabel": "视频已开始播放", "IntersectionObserverThresholdTitle": "阈值变化观察", "IntersectionObserverThresholdIntro": "通过设置 <code>Threshold</code> 后通过回调参数 <code>IntersectionObserveEntry</code> 属性 <code>IntersectionRatio</code> 获得观察元素与根元素交叉率", "IntersectionObserverThresholdDesc": "拖动下方 <code>div</code> 滚动条，可观察数据变化", "AttributeUseElementViewport": "是否使用当前元素作为视窗", "AttributeRootMargin": "根元素边距", "AttributeThreshold": "可见性阈值", "AttributeAutoUnobserveWhenIntersection": "元素可见时是否自动取消观察", "AttributeAutoUnobserveWhenNotIntersection": "元素不可见时是否自动取消观察", "AttributeOnIntersectingAsync": "可见回调方法", "AttributeChildContent": "子组件", "LoadMoreTitle": "LoadMore 组件", "LoadMoreIntro": "通过设置 <code>LoadMore</code> 组件参数 <code>CanLoading</code> 控制加载状态，<code>OnLoadMoreAsync</code> 回调方法加载更多数据", "LoadMoreDesc": "<p>本例中通过设置 <code>CanLoading</code> 为 <code>true</code> 显示加载指示符，加载完成后设置为 <code>false</code> 显示 <b>没有更多数据</b> 提示文本</p><ul class=\"ul-demo\"><li>通过 <code>LoadingTemplate</code> 自定义加载更多的 UI</li><li>通过 <code>NoMoreTemplate</code> 自定义没有更多数据时显示的 UI</li><li>通过 <code>NoMoreText</code> 参数设置没有更多加载项时显示的指示文本</li></ui>"}, "BootstrapBlazor.Server.Components.Samples.SortableLists": {"SortableListTitle": "SortableList 拖拽组件", "SortableListDescription": "可排序的拖拽组件", "SortableListNormalTitle": "简单例子", "SortableListNormalIntro": "通过设置 <code>SortableListOption</code> 参数 <code>RootSelector</code> 指定样式选择器获得根元素，通过设置 <code>OnUpdate</code> 回调方法重新设置数据源", "SortableListGroupTitle": "分组共享拖拽", "SortableListGroupIntro": "通过设置 <code>SortableListOption</code> 参数 <code>Group</code> 指定组名，使不同组之间元素可以拖拽", "SortableListCloneTitle": "拖拽时克隆元素", "SortableListCloneIntro": "通过设置 <code>SortableListOption</code> 参数 <code>Clone</code> 设置拖拽时是否克隆当前元素", "SortableListDisableSortTitle": "禁止排序", "SortableListDisableSortIntro": "通过设置 <code>SortableListOption</code> 参数 <code>Sort</code> 设置拖拽时是否排序当前元素，本例中通过设置 <code>Putback=\"false\"</code> 禁止元素拖拽回左侧集合中", "SortableListHandlerTitle": "拖拽控制元素", "SortableListHandlerIntro": "通过设置 <code>SortableListOption</code> 参数 <code>Handle</code> 设置指定可拖拽元素，鼠标拖动图标时拖动整个元素", "SortableListFilterTitle": "条件过滤拖拽组件", "SortableListFilterIntro": "通过设置 <code>SortableListOption</code> 参数 <code>Filter</code> 设置有条件性的拖拽元素，示例中红色元素无法拖动", "SortableListNestTitle": "嵌套使用", "SortableListNestIntro": "通过嵌套使用 <code>SortableList</code> 组件即可", "SortableListMultiTitle": "多项拖拽", "SortableListMultiIntro": "通过设置 <code>SortableListOption</code> 参数 <code>MultiDrag</code> 可先选择多个项目后拖拽", "SortableListTableTitle": "表格行拖拽", "SortableListTableIntro": "通过设置 <code>SortableListOption</code> 参数 <code>RootSelector</code> 指定拖拽根元素为 <code>tbody</code> 即可对行进行拖拽操作", "SortableListClassTitle": "由于 <code>SortableList</code>为容器组件，内容均为自定义组件，无法内置样式，需要根据实际情况自行设置样式", "SortableListClassLi1": "拖动元素样式 <code>sortable-chosen</code>", "SortableListClassLi2": "拖动元素克隆项默认样式 <code>sortable-ghost</code> 可通过 <code>GhostClass</code> 自定义", "SortableListClassLi3": "拖动元素交换想项默认样式 <code>sortable-swap-highlight</code> 可通过 <code>SwapClass</code> 自定义", "SortableListSwapTitle": "交换", "SortableListSwapIntro": "通过设置 <code>SortableListOption</code> 参数 <code>Swap</code> 设置拖拽项与目标项目交换", "SortableListOnAddTitle": "多区域拖动", "SortableListOnAddIntro": "通过设置 <code>SortableEvent</code> 参数 <code>FromId</code> 获得拖动项所属 <code>SortableList</code>。本示例中三个区域内元素可以任意拖动", "AttributeSortableListOption": "SortableListOption 实例", "AttributeOnAdd": "增加元素时回调方法", "AttributeOnUpdate": "更新元素时回调方法", "AttributeOnRemove": "删除元素时回调方法"}, "BootstrapBlazor.Server.Components.Samples.WinBoxes": {"WinBoxTitle": "WinBox 窗口组件", "WinBoxDescription": "模拟 Windows 窗口风格的组件", "WinBoxNormalTitle": "基础用法", "WinBoxNormalIntro": "通过设置 <code>WinBoxOption</code> 参数值，创建并且弹出窗口", "AttributeRoot": "弹窗容器根节点", "AttributeTitle": "弹窗标题", "AttributeClass": "弹窗样式", "AttributeBackground": "弹窗背景色", "AttributeBorder": "弹窗边框", "AttributeIcon": "弹窗标题图标", "OnCreateAsync": "创建弹窗回调方法", "OnShownAsync": "弹窗可见回调方法", "OnHideAsync": "隐藏弹窗回调方法", "OnFocusAsync": "弹窗获得焦点回调方法", "OnBlurAsync": "弹窗失去焦点回调方法", "OnFullscreenAsync": "弹窗全屏回调方法", "OnRestoreAsync": "恢复弹窗回调方法", "OnMaximizeAsync": "最大化弹窗回调方法", "OnMinimizeAsync": "最小化弹窗回调方法", "OnCloseAsync": "关闭弹窗回调方法"}, "BootstrapBlazor.Server.Components.Samples.Players": {"PlayersTitle": "Player 音视频播放器", "PlayersDescription": "一个简单、可访问和可定制的媒体播放器", "PlayersTips": "<code>Player</code> 组件支持 视频，音频，<b>Youtube</b> 和 <b>Vimeo</b>", "PlayersNormalTitle": "基础用法", "PlayersNormalIntro": "通过回调方法 <code>OnInitAsync</code> 设置组件所需要的配置实例 <code>PlayerOption</code>。通过 <code>PlayerOption</code> 属性值对播放器进行设置", "PlayersHlsTitle": "HLS 支持", "PlayersHlsIntro": "通过设置 <code>IsHls=\"true\"</code> 开启 <code>Hls</code> 支持", "PlayersAudioTitle": "音频播放器", "PlayersAudioIntro": "通过设置 <code>Mode=\"PlayerMode.Audio\"</code> 播放音频文件", "PlayersYouTubeTitle": "YouTube 支持", "PlayersYouTubeIntro": "通过设置 <code>Mode=\"PlayerSources\"</code> 参数 <b>Provider=\"youtube\"</b> 播放 <code>YouTube</code> 视频", "PlayersVimeoTitle": "Vimeo 支持", "PlayersVimeoIntro": "通过设置 <code>Mode=\"PlayerSources\"</code> 参数 <b>Provider=\"vimeo\"</b> 播放 <code>Vimei</code> 视频"}, "BootstrapBlazor.Server.Data.CustomValidataModel": {"Name": "名称", "Telephone1": "联系电话1", "Telephone2": "联系电话2", "CanNotBeTheSame": "联系电话1 和 联系电话2 不能相同"}, "BootstrapBlazor.Server.Data.CustomValidateCollectionModel": {"Telephone1": "联系电话1", "Telephone2": "联系电话2"}, "BootstrapBlazor.Server.Components.Samples.IFrames": {"IFrameTitle": "IFrame 内联框架", "IFrameDescription": "允许在页面中嵌入文档、视频和交互式媒体。 通过这样做可以在主页上显示一个辅助页面。 iFrame 元素允许包含来自其他源的内容，它可以在页面的任何地方集成内容。", "IFrameNormalTitle": "基础用法", "IFrameNormalIntro": "通过 <code>Src</code>参数设置其他网站页面地址，内联其内容到本页面中", "AttributeSrc": "Frame 加载网页路径", "AttributeData": "传递的数据", "AttributeOnPostDataAsync": "Frame 加载页面传递过来的数据"}, "BootstrapBlazor.Server.Components.Samples.RDKits": {"RDKitTitle": "RDKit.js 分子图组件", "RDKitDescription": "RDKit 是一个开源的化学信息学工具包，用于分子建模和化学信息学", "RDKitNormalTitle": "基础用法", "RDKitNormalIntro": "通过 <code>Smiles</code> 设置分子式，组件画图", "RDKitSmartsTitle": "Smarts", "RDKitSmartsIntro": "通过设置 <code>Smarts</code> 值，高亮显示部分分子式", "RDKitSmartsLabel": "显示 Smarts", "RDKitSizeTitle": "尺寸", "RDKitSizeIntro": "通过设置 <code>Width</code> <code>Height</code> 值设置分子式宽高"}, "BootstrapBlazor.Server.Components.Samples.SmilesDrawers": {"SmilesDrawerTitle": "SmilesDrawer 分子图组件", "SmilesDrawerDescription": "SmilesDrawer 是一个开源的化学信息学工具包，用于分子建模和化学信息学", "SmilesDrawerNormalTitle": "基础用法", "SmilesDrawerNormalIntro": "通过 <code>Smiles</code> 设置分子式，组件画图", "SmilesDrawerSizeTitle": "尺寸", "SmilesDrawerSizeIntro": "通过设置 <code>Width</code> <code>Height</code> 值设置分子式宽高"}, "BootstrapBlazor.Server.Components.Samples.Affixs": {"AffixTitle": "Affix 固钉组件", "AffixIntro": "将页面元素钉在可视范围", "AffixNormalTitle": "基础用法", "AffixNormalIntro": "固钉默认固定在页面顶部", "AffixPositionTitle": "位置与距离", "AffixPositionIntro": "通过参数 <code>Position</code> 控制固定顶端还是底端，通过 <code>Offset</code> 值设置到顶端或者底端距离偏移量"}, "BootstrapBlazor.Server.Components.Samples.Watermarks": {"WatermarkTitle": "Watermark 水印组件", "WatermarkIntro": "在页面上添加文本或图片等水印信息", "WatermarkNormalTitle": "基础用法", "WatermarkNormalIntro": "使用 <code>Text</code> 属性设置一个字符串指定水印内容", "WatermarkDescription": "<p>全局增加水印实现方法</p><p>可以在模板页 <code>MainLayout</code> 中加水印组件并设置 <code>IsPage=\"true\"</code> 即可</p>"}, "BootstrapBlazor.Server.Data.AttributeItem": {"Name": "参数", "Description": "说明", "Type": "类型", "ValueList": "可选值", "DefaultValue": "默认值"}, "BootstrapBlazor.Server.Data.EventItem": {"Name": "参数", "Description": "说明", "Type": "类型"}, "BootstrapBlazor.Server.Components.Pages.CacheList": {"CacheListTitle": "缓存管理", "CacheListIntro": "通过 <code>ICacheManager</code> 接口方法管理组件库内部缓存", "CacheListKey": "键", "CacheListValue": "值", "CacheListExpiration": "到期时间", "CacheListAction": "操作", "CacheListRefresh": "刷新", "CacheListDelete": "删除", "CacheListDeleteAll": "清除全部", "CacheListCount": "共 {0} 个键值"}, "BootstrapBlazor.Server.Components.Samples.Typeds": {"TypedTitle": "Typed 打字机效果", "TypedIntro": "输入任意字符串，它会按照你设置的速度输入，输入的内容会退格，然后根据你设置的字符串数量开始一个新句子。", "NormalTitle": "基础用法", "NormalIntro": "通过设置 <code>Text</code> 参数设置要显示的文本", "TypedOptionsTitle": "TypedOptions", "TypedOptionsIntro": "通过设置 <code>TypedOptions</code> 参数的属性自定义打字速度、延时等设定"}, "BootstrapBlazor.Server.Components.Samples.Html2Images": {"Html2ImageTitle": "Html2Image 网页元素转成图片服务", "Html2ImageIntro": "将网页中任意区域内容转化成图片服务", "Html2ImageElementTitle": "ToPng", "Html2ImageElementIntro": "通过调用 <code>GetDataAsync</code> 方法获得 <b>base64-encoded</b> 图片", "Html2ImageButtonText": "Image", "Html2ImageDesc": "由于底层使用的是 <a href=\"https://github.com/bubkoo/html-to-image?wt.mc_id=DT-MVP-5004174\" target=\"_blank\">html-to-image</a> 实际使用过程中遇到问题请参考项目 <a href=\"https://github.com/bubkoo/html-to-image/issues?wt.mc_id=DT-MVP-5004174\" target=\"_blank\">Issue</a>"}, "BootstrapBlazor.Server.Components.Samples.UniverSheets": {"UniverSheetTitle": "UniverSheet 电子表格组件", "UniverSheetIntro": "封装开源办公套件 ​Univer​ 的核心电子表格组件，提供高性能、可扩展的在线表格解决方案", "NormalTitle": "基础用法", "NormalIntro": "通过调用实例方法 <code>PushDataAsync</code> 推送数据到电子表格", "NormalDesc1": "点击 <b>推送数据</b> 按钮主动将数据推送给表格，点击 <b>保存数据</b> 按钮获得表格数据序列化的数据", "NormalDesc2": "点击 <b>推送数据</b> 按钮主动将数据推送给表格，点击 <b>工具栏</b> 第一个小按钮主动从服务器端获取数据", "PushButtonText": "推送数据", "SaveButtonText": "保存数据", "ToastOnReadyTitle": "组件通知", "ToastOnReadyContent": "表格组件已就绪，可进行后续数据推送等操作", "PluginTitle": "自定义插件", "PluginIntro": "通过设置 <code>Plugins</code> 参数设置自己的插件"}, "BootstrapBlazor.Server.Components.Samples.Tutorials.OnlineSheet": {"ToastOnReadyTitle": "在线表格协作通知", "ToastOnReadyContent": "4 秒后表格更新其他写作人员更改内容"}, "BootstrapBlazor.Server.Components.Samples.OtpInputs": {"OtpInputsTitle": "OtpInput 密码框", "OtpInputsDescription": "基于 OTP(One-Time Password‌) 仅限单次使用且具备时效性的安全验证密码框", "OtpInputsTips": "OTP（One Time Password，简称OTP）：这是一种安全措施，用于在每次登录或交易时生成一个唯一的密码，本组件配合 <code>ITotpService</code> <code>IHotpService</code> 使用大大提高安全性", "OtpInputsNormalTitle": "基础用法", "OtpInputsNormalIntro": "通过设置 <code>Type</code> 等参数控制密码类型或者长度", "OtpInputsValidateFormTitle": "表单内使用", "OtpInputsValidateFormIntro": "密码未提供值时，点击提交按钮，密码框显示为红色，并且提示不可为空"}, "BootstrapBlazor.Server.Components.Samples.OtpServices": {"Title": "ITotpService 时间密码验证服务", "SubTitle": "实现 TOTP RFC 6238 和 HOTP RFC 4226 认证器服务", "BaseUsageTitle": "基本用法", "BaseUsageIntro": "通过调用 <code>ITotpService</code> 服务实例方法 <code>Compute</code> 得到当前时间窗口内密码, 通过调用 <code>Instance.GetRemainingSeconds</code> 方法可以显示当前密码剩余有效时间，本例中通过进度条进行动态显示"}, "BootstrapBlazor.Server.Components.Samples.Tutorials.BarCodeGenerator": {"SelectType": "选择一个类型", "Preview": "预览", "TextDesc": "使用 <code>iPhone</code> 手机相机或者扫描二维码功能扫描下方二维码后，打开 <code>Safari</code> 浏览器使用默认搜索引擎搜索当前文本", "UrlDesc": "使用 <code>iPhone</code> 手机相机或者扫描二维码功能扫描下方二维码后，打开 <code>Safari</code> 浏览器并且打开当前地址", "WiFiDesc": "使用 <code>iPhone</code> 手机相机或者扫描二维码功能扫描下方二维码后，自动加入 <code>WiFi</code> 网络", "EmailDesc": "使用 <code>iPhone</code> 手机相机或者扫描二维码功能扫描下方二维码后，打开 <code>Email</code> 应用发送邮件"}, "BootstrapBlazor.Server.Components.Samples.VideoDevices": {"VideoDeviceTitle": "IVideoDevice 视频设备服务", "VideoDeviceIntro": "通过此服务获得视频设备操作能力", "BaseUsageTitle": "基本用法", "BaseUsageIntro": "通过调用不同的 api 方法进行不同操作", "VideoDeviceRequestText": "枚举设备", "VideoDeviceOpenText": "打开设备", "VideoDeviceCloseText": "关闭设备", "VideoDeviceCaptureText": "截图", "VideoDeviceDownloadText": "下载"}, "BootstrapBlazor.Server.Components.Samples.AudioDevices": {"AudioDeviceTitle": "IAudioDevice 音频设备服务", "AudioDeviceIntro": "通过此服务获得音频设备操作能力", "BaseUsageTitle": "基本用法", "BaseUsageIntro": "通过调用不同的 api 方法进行不同操作", "AudioDeviceRequestText": "枚举设备", "AudioDeviceOpenText": "录音", "AudioDeviceCloseText": "停止", "AudioDevicePauseText": "暂停", "AudioDeviceResumeText": "恢复", "AudioDeviceDownloadText": "下载"}, "BootstrapBlazor.Server.Components.Samples.Vditors": {"VditorTitle": "Vditor Markdown 富文本编辑框", "VditorSubTitle": "Vditor 是一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式", "BaseUsageTitle": "基本用法", "BaseUsageIntro": "通过设置 <code>Value</code> 值设置组件显示的内容，通过 <code>Options</code> 参数设置组件配置信息"}, "BootstrapBlazor.Server.Components.Samples.OfficeViewers": {"OfficeViewerTitle": "Office 文档预览器", "OfficeViewerDescription": "本组件通过使用微软在线文档预览功能预览 Office 文档内容", "OfficeViewerNormalTitle": "基本用法", "OfficeViewerNormalIntro": "通过设置 <code>Url</code> 值设置预览文档地址", "OfficeViewerToastSuccessfulContent": "Office 文档加载成功"}, "BootstrapBlazor.Server.Components.Samples.Sockets.ManualReceives": {"ReceivesTitle": "手动接收示例", "ReceivesDescription": "通过调用 ReceiveAsync 接收数据并且显示", "NormalTitle": "基本用法", "NormalIntro": "连接后通过 <code>ReceiveAsync</code> 回调方法接收服务端发送来的数据，需要自行处理粘包分包的数据问题"}, "BootstrapBlazor.Server.Components.Samples.Sockets.AutoReceives": {"ReceivesTitle": "自动接收示例", "ReceivesDescription": "通过 ReceiveCallback 接收数据并且显示", "NormalTitle": "基本用法", "NormalIntro": "连接后通过 <code>ReceivedCallBack</code> 回调方法自动接收服务端发送来的时间戳数据"}, "BootstrapBlazor.Server.Components.Samples.Sockets.Adapters": {"AdaptersTitle": "Socket 数据适配器示例", "AdaptersDescription": "通过数据适配器接收数据并且显示", "NormalTitle": "基本用法", "NormalIntro": "连接后通过 <code>DataPackageAdapter</code> 数据适配器的 <code>ReceivedCallBack</code> 回调方法接收服务端发送来的时间戳数据"}, "BootstrapBlazor.Server.Components.Samples.Sockets.AutoReconnects": {"AutoReconnectsTitle": "Socket 自动重连示例", "AutoReconnectsDescription": "链路断开后自动重连示例", "NormalTitle": "基本用法", "NormalIntro": "通过设置 <code>IsAutoReconnect</code> 开启自动重连机制"}, "BootstrapBlazor.Server.Components.Samples.Sockets.DataEntities": {"DataEntityTitle": "Socket 数据转化实体类", "DataEntityDescription": "接收到通讯数据后自动转成业务需要的实体类", "NormalTitle": "基本用法", "NormalIntro": "通过 <code>DataTypeConverterAttribute</code> 标签开启数据自动转换功能"}, "BootstrapBlazor.Server.Components.Samples.NetworkMonitors": {"NetworkMonitorTitle": "NetworkMonitor 网络状态", "NetworkMonitorDescription": "使用浏览器原生 api <code>navigator.connection</code> 实时显示当前网络状态", "NormalTitle": "基本用法", "NormalIntro": "使用组件 <code>NetworkMonitorIndicator</code> 当网络状态变化时，显示不同颜色的指示灯，鼠标移动到上面时显示网络状态明细", "IndicatorLi1": "绿色：网络非常好 (4G)", "IndicatorLi2": "黄色：网络一般 (3G)", "IndicatorLi3": "红色：网络差 (2G)", "IndicatorLi4": "灰色：离线状态"}}