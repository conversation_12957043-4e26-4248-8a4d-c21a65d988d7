<Project>

  <Import Project="..\Directory.Build.props" />
  <Import Project="..\Logo.props" />
  <Import Project="..\SourceLink.targets" />

  <PropertyGroup>
    <PackageTags>Bootstrap Blazor WebAssembly wasm UI Components</PackageTags>
    <Description>Bootstrap UI components for Blazor and Razor Components</Description>
    <PackageReadmeFile>readme.md</PackageReadmeFile>
    <PackageReleaseNotes>https://github.com/dotnetcore/BootstrapBlazor/releases?wt.mc_id=DT-MVP-5004174</PackageReleaseNotes>
    <PackageProjectUrl>https://www.blazor.zone</PackageProjectUrl>
    <RepositoryType>github</RepositoryType>
    <RepositoryUrl>https://github.com/dotnetcore/BootstrapBlazor.git</RepositoryUrl>
    <PackageLicenseExpression>Apache-2.0</PackageLicenseExpression>
    <IsPackable>true</IsPackable>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFrameworks>net6.0;net7.0;net8.0;net9.0</TargetFrameworks>
  </PropertyGroup>

  <PropertyGroup>
    <NET6Version>6.0.*</NET6Version>
    <NET7Version>7.0.*</NET7Version>
    <NET8Version>8.0.*</NET8Version>
    <NET9Version>9.0.*</NET9Version>
  </PropertyGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net6.0'">
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="$(NET6Version)" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="$(NET6Version)" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="$(NET6Version)" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="$(NET8Version)" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net7.0'">
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="$(NET7Version)" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="$(NET7Version)" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="$(NET7Version)" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="$(NET8Version)" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net8.0'">
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="$(NET8Version)" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="$(NET8Version)" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net9.0'">
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="$(NET9Version)" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="$(NET9Version)" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="$(NET9Version)" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="$(NET9Version)" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="$(NET9Version)" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="$(NET9Version)" />
    <PackageReference Include="Microsoft.Extensions.Localization" Version="$(NET9Version)" />
  </ItemGroup>

  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <None Include="readme.md" Pack="true" PackagePath="" />
  </ItemGroup>

</Project>
