@namespace BootstrapBlazor.Components
@using Microsoft.AspNetCore.Components.Web.Virtualization
@typeparam TValue
@inherits SelectBase<TValue>
@attribute [JSModuleAutoLoader("./_content/BootstrapBlazor/Components/Select/Select.razor.js", JSObjectReference = true)]

@if (IsShowLabel)
{
    <BootstrapLabel required="@Required" for="@InputId" ShowLabelTooltip="ShowLabelTooltip" Value="@DisplayText" />
}
<div @attributes="AdditionalAttributes" id="@Id" class="@ClassString" data-bb-scroll-behavior="@ScrollIntoViewBehaviorString">
    <CascadingValue Value="this" IsFixed="true">
        @Options
    </CascadingValue>
    <RenderTemplate>
        <div class="dropdown-toggle" data-bs-toggle="@ToggleString" data-bs-placement="@PlacementString" data-bs-offset="@OffsetString" data-bs-custom-class="@CustomClassString">
            @if (DisplayTemplate != null)
            {
                <div id="@InputId" class="@InputClassString">
                    @DisplayTemplate(SelectedRow)
                </div>
            }
            else
            {
                <input type="text" id="@InputId" disabled="@Disabled" placeholder="@PlaceHolder" class="@InputClassString" value="@SelectedRow?.Text" @onchange="OnChange" readonly="@ReadonlyString" />
            }
            <span class="@AppendClassString"><i class="@DropdownIcon"></i></span>
        </div>
        @if (GetClearable())
        {
            <span class="@ClearClassString" @onclick="OnClearValue"><i class="@ClearIcon"></i></span>
        }
        <div class="dropdown-menu">
            @if (ShowSearch)
            {
                <div class="dropdown-menu-search">
                    <input type="text" class="search-text form-control" autocomplete="off" value="@SearchText" aria-label="search" />
                    <i class="@SearchIconString"></i>
                    <i class="@SearchLoadingIconString"></i>
                </div>
            }
            @if (IsVirtualize)
            {
                <div class="dropdown-menu-body dropdown-virtual">
                    @if (OnQueryAsync == null)
                    {
                        <Virtualize ItemSize="RowHeight" OverscanCount="OverscanCount" Items="@GetVirtualItems()" ChildContent="RenderRow" />
                    }
                    else
                    {
                        <Virtualize ItemSize="RowHeight" OverscanCount="OverscanCount" ItemsProvider="LoadItems" Placeholder="RenderPlaceHolderRow" ItemContent="RenderRow" @ref="VirtualizeElement" />
                    }
                </div>
            }
            else if (Rows.Count == 0)
            {
                <div class="dropdown-item">@NoSearchDataText</div>
            }
            else
            {
                <div class="dropdown-menu-body">
                    @foreach (var itemGroup in Rows.GroupBy(i => i.GroupName))
                    {
                        if (!string.IsNullOrEmpty(itemGroup.Key))
                        {
                            if (GroupItemTemplate != null)
                            {
                                @GroupItemTemplate(itemGroup.Key)
                            }
                            else
                            {
                                <Divider Text="@itemGroup.Key" />
                            }
                        }
                        @foreach (var item in itemGroup)
                        {
                            @RenderRow(item)
                        }
                    }
                </div>
            }
        </div>
        @if (!IsPopover)
        {
            <div class="dropdown-menu-arrow"></div>
        }
    </RenderTemplate>
</div>

@code {
    RenderFragment<SelectedItem<TValue>> RenderRow => item =>
    @<div class="@ActiveItem(item)" @onclick="() => OnClickItem(item)">
        @if (ItemTemplate != null)
        {
            @ItemTemplate(item)
        }
        else if (IsMarkupString)
        {
            @((MarkupString)item.Text) 
        }
        else
        {
            @item.Text
        }
    </div>;

    RenderFragment<PlaceholderContext> RenderPlaceHolderRow => context =>
    @<div class="dropdown-item">
        <div class="is-ph"></div>
    </div>;
}
