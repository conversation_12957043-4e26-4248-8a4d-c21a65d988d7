.input-group {
    > .datetime-picker,
    > .select,
    > .switch,
    > .bb-clearable-input,
    > .auto-complete {
        width: 1%;
        flex: 1 1 auto;
        min-width: 0;
    }

    > [data-bs-toggle] {
        flex: 1;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    > .switch {
        --bb-switch-padding: 7px .5rem;
        border: var(--bs-border-width) solid var(--bs-border-color);
        border-radius: var(--bs-border-radius);
    }

    > .segmented {
        border: 1px solid var(--bs-border-color);
    }

    > .input-group-text {
        width: var(--bb-input-group-label-width);
    }

    > .radio-list-group {
        > .btn-group {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;

            > .btn:first-child {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }
        }
    }

    > .bb-clearable-input:not(:first-child) {
        > input {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    > .bb-clearable-input:not(:last-child) {
        > input {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
    }

    > .form-label {
        display: none;
    }

    > .form-check {
        --bb-form-check-padding: 0.375rem 0.75rem;
        display: flex;
        align-items: center;
        padding: var(--bb-form-check-padding);
    }

    > .form-range {
        --bb-form-range-padding: 0.375rem 0.75rem;
        height: var(--bb-height);
        padding: var(--bb-form-range-padding);
    }

    > .form-check, .form-range {
        border: 1px solid var(--bs-border-color);
        border-radius: var(--bs-border-radius);
        flex-grow: 1;
        width: 1%;
        min-width: 0;

        &:hover {
            border: 1px solid var(--bb-border-hover-color);
        }
    }
}

.input-group-xs {
    .btn, .form-control, .input-group-text {
        font-size: .75rem;
    }
}
