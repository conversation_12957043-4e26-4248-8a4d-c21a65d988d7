@namespace BootstrapBlazor.Components
@inherits BootstrapModuleComponentBase
@attribute [BootstrapModuleAutoLoader(JSObjectReference = true)]

@if (Excluded)
{
    @Body
}
else
{
    <div @attributes="@AdditionalAttributes" id="@Id" data-bb-header-id="@HeaderId" class="@ClassString" style="@StyleString">
        @if (TabHeader != null)
        {
            TabHeader.Render(RenderTabHeader);
        }
        else
        {
            @RenderTabHeader
        }
        <div class="tabs-body">
            <CascadingValue Value="this" IsFixed="true">
                @if (IsOnlyRenderActiveTab)
                {
                    var item = TabItems.Find(i => i.IsActive);
                    if (item != null)
                    {
                        <div id="@item.Id" class="@GetTabItemClassString(item)">
                            @RenderTabItem(item)
                        </div>
                    }
                }
                else
                {
                    foreach (var item in TabItems)
                    {
                        <div @key="@item" id="@item.Id" class="@GetTabItemClassString(item)">
                            @RenderTabItem(item)
                        </div>
                    }
                }
            </CascadingValue>
        </div>
    </div>
}

@code {
    RenderFragment RenderTabHeader =>
    @<div class="tabs-header">
        <div class="@WrapClassString">
            @if (BeforeNavigatorTemplate != null)
            {
                @BeforeNavigatorTemplate(this)
            }
            @if (ShowNavigatorButtons)
            {
                <div class="nav-link-bar left">
                    <Tooltip Title="@PrevTabNavLinkTooltipText" Placement="Placement.Bottom" Trigger="hover">
                        <div class="nav-link-bar-button" @onclick="@ClickPrevTab">
                            <i class="@PreviousIcon"></i>
                        </div>
                    </Tooltip>
                </div>
            }
            <div class="tabs-nav-scroll">
                <div role="tablist" class="tabs-nav">
                    <CascadingValue Value="this" IsFixed="true">
                        @ChildContent
                    </CascadingValue>
                    <RenderTemplate>
                        @RenderTabList()
                    </RenderTemplate>
                    @if (!IsCard && !IsBorderCard && ShowActiveBar && TabStyle == TabStyle.Default)
                    {
                        <div class="tabs-active-bar"></div>
                    }
                </div>
            </div>
            @if (ButtonTemplate != null)
            {
                @ButtonTemplate(this)
            }
            @if (ShowToolbar)
            {
                <div class="tabs-nav-toolbar">
                    @if (ShowRefreshToolbarButton)
                    {
                        <TabToolbarButton class="tabs-nav-toolbar-refresh"
                                          Icon="@RefreshToolbarButtonIcon" OnClickAsync="OnRefreshAsync"
                                          TooltipText="@RefreshToolbarTooltipText"></TabToolbarButton>
                    }
                    @if (ShowFullscreenToolbarButton)
                    {
                        <div class="tabs-nav-toolbar-button tabs-nav-toolbar-fs">
                            <FullScreenButton Icon="@FullscreenToolbarButtonIcon"
                                              TooltipText="@FullscreenToolbarTooltipText"
                                              TooltipPlacement="Placement.Bottom" TooltipTrigger="hover"></FullScreenButton>
                        </div>
                    }
                    @if (ToolbarTemplate != null)
                    {
                        @ToolbarTemplate(this)
                    }
                </div>
            }
            @if (ShowNavigatorButtons)
            {
                <div class="nav-link-bar right">
                    <Tooltip Title="@NextTabNavLinkTooltipText" Placement="Placement.Bottom" Trigger="hover">
                        <div class="nav-link-bar-button" @onclick="@ClickNextTab">
                            <i class="@NextIcon"></i>
                        </div>
                    </Tooltip>
                </div>
            }
            @if (ShouldShowExtendButtons())
            {
                <div class="nav-link-bar dropdown dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <Tooltip Title="@CloseTabNavLinkTooltipText" Placement="Placement.Left" Trigger="hover">
                        <div class="nav-link-bar-button nav-link-close"><i class="@DropdownIcon"></i></div>
                    </Tooltip>
                </div>
                <div class="dropdown-menu dropdown-menu-end shadow">
                    <div class="dropdown-item" @onclick="@CloseCurrentTab"><span>@CloseCurrentTabText</span></div>
                    <div class="dropdown-item" @onclick="@OnClickCloseOtherTabs"><span>@CloseOtherTabsText</span></div>
                    <div class="dropdown-item" @onclick="@OnClickCloseAllTabs"><span>@CloseAllTabsText</span></div>
                </div>
            }
            @if (AfterNavigatorTemplate != null)
            {
                @AfterNavigatorTemplate(this)
            }
        </div>
    </div>;

    RenderFragment RenderContextMenu =>
    @<ContextMenu>
        @if (BeforeContextMenuTemplate != null)
        {
            @BeforeContextMenuTemplate(this)
        }
        <ContextMenuItem Icon="@ContextMenuRefreshIcon" Text="@Localizer["ContextRefresh"]" OnClick="OnRefresh"></ContextMenuItem>
        <ContextMenuDivider></ContextMenuDivider>
        <ContextMenuItem Icon="@ContextMenuCloseIcon" Text="@Localizer["ContextClose"]" OnClick="OnClose"></ContextMenuItem>
        <ContextMenuItem Icon="@ContextMenuCloseOtherIcon" Text="@Localizer["ContextCloseOther"]" OnClick="OnCloseOther"></ContextMenuItem>
        <ContextMenuItem Icon="@ContextMenuCloseAllIcon" Text="@Localizer["ContextCloseAll"]" OnClick="OnCloseAll"></ContextMenuItem>
        @if (ShowContextMenuFullScreen)
        {
            <ContextMenuDivider></ContextMenuDivider>
            <ContextMenuItem Icon="@ContextMenuFullScreenIcon" Text="@Localizer["ContextFullScreen"]" OnClick="OnFullScreen"></ContextMenuItem>
        }
        @if (ContextMenuTemplate != null)
        {
            @ContextMenuTemplate(this)
        }
    </ContextMenu>;

    RenderFragment<TabItem> RenderTabItem => item =>
    @<CascadingValue Value="item" IsFixed="true">
        @RenderTabItemContent(item)
    </CascadingValue>;

    RenderFragment RenderDisabledHeaderItem(TabItem item) =>
    @<div @key="@item" class="@GetItemWrapClassString(item)">
        <div role="tab" class="@GetClassString(item)"
             @oncontextmenu="e => OnContextMenu(e, item)" @oncontextmenu:preventDefault="IsPreventDefault">
            @RenderHeaderItemContent(item)
        </div>
        @if (TabStyle == TabStyle.Chrome)
        {
            <i class="tab-corner tab-corner-left"></i>
            <i class="tab-corner tab-corner-right"></i>
        }
    </div>;

    RenderFragment RenderHeaderItem(TabItem item) =>
    @<div @key="@item" class="@GetItemWrapClassString(item)" draggable="@DraggableString">
        <a href="@item.Url" role="tab" tabindex="-1" class="@GetClassString(item)"
           @oncontextmenu="e => OnContextMenu(e, item)" @oncontextmenu:preventDefault="IsPreventDefault"
           @onclick="@(() => OnClickTabItem(item))" @onclick:preventDefault="@(!ClickTabToNavigation)">
            @RenderHeaderItemContent(item)
        </a>
        @if (TabStyle == TabStyle.Chrome)
        {
            <i class="tab-corner tab-corner-left"></i>
            <i class="tab-corner tab-corner-right"></i>
        }
    </div>;

    RenderFragment RenderHeaderItemContent(TabItem item) =>
    @<div class="tabs-item-body">
        @if (!string.IsNullOrEmpty(item.Icon))
        {
            <i class="@GetIconClassString(item.Icon)"></i>
        }
        <span class="tabs-item-text">@item.Text</span>
        @if (!item.IsDisabled)
        {
            @if (ShowFullScreen && item.ShowFullScreen)
            {
                <FullScreenButton></FullScreenButton>
            }
            @if (ShowClose && item.Closable)
            {
                <span class="tabs-item-close" @onclick:stopPropagation @onclick:preventDefault @onclick="() => RemoveTab(item)">
                    <i class="@CloseIcon"></i>
                </span>
            }
        }
    </div>;
}
