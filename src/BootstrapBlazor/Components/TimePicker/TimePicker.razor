@namespace BootstrapBlazor.Components
@inherits ValidateBase<TimeSpan>

<div @attributes="@AdditionalAttributes" class="@ClassString">
    <div class="time-picker-body">
        <TimePickerCell @bind-Value="@CurrentTime" ViewMode="TimePickerCellViewMode.Hour" />
        <TimePickerCell @bind-Value="@CurrentTime" ViewMode="TimePickerCellViewMode.Minute" />
        @if (HasSeconds)
        {
            <TimePickerCell @bind-Value="@CurrentTime" ViewMode="TimePickerCellViewMode.Second" />
        }
    </div>
    <div class="time-picker-footer">
        <button type="button" class="btn time-panel-btn cancel" @onclick="@OnClickClose">@CancelButtonText</button>
        <button type="button" class="btn time-panel-btn confirm" @onclick="@OnClickConfirm">@ConfirmButtonText</button>
    </div>
</div>

@code {

}
