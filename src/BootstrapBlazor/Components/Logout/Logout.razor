@namespace BootstrapBlazor.Components
@inherits IdComponentBase

<div @attributes="AdditionalAttributes" class="@LogoutClassString">
    <a class="dropdown-toggle" data-bs-toggle="dropdown" href="#" id="@Id" aria-expanded="false">
        @if (ChildContent == null)
        {
            <img class="logout-avatar" alt="avatar" src="@ImageUrl" />
            @if (ShowUserName)
            {
                <span class="logout-text">@DisplayName</span>
            }
        }
        else
        {
            @ChildContent
        }
    </a>
    <div class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="@Id">
        <div class="dropdown-item dropdown-user">
            @if (HeaderTemplate == null)
            {
                <div class="d-flex flex-fill align-items-center">
                    <img alt="avatar" src="@ImageUrl" style="@AvatarStyleString">
                    <div class="flex-fill">
                        <div class="logout-dn">@PrefixDisplayNameText @DisplayName</div>
                        <div class="logout-un">@PrefixUserNameText @UserName</div>
                    </div>
                </div>
            }
            else
            {
                @HeaderTemplate
            }
        </div>
        @if (LinkTemplate != null)
        {
            <div class="dropdown-item">
                @LinkTemplate
            </div>
        }
    </div>
</div>
