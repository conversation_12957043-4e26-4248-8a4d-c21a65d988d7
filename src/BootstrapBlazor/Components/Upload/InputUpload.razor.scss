.upload {
    --bb-upload-body-margin-top: #{$bb-upload-body-margin-top};
    --bb-upload-body-list-max-height: #{$bb-upload-body-list-max-height};
    --bb-upload-body-list-item-padding: #{$bb-upload-body-list-item-padding};
    --bb-upload-body-list-item-body-padding: #{$bb-upload-body-list-item-body-padding};
    --bb-upload-body-list-item-hover-color: #{$bb-upload-body-list-item-hover-color};
    --bb-upload-body-list-grap: #{$bb-upload-body-list-grap};
    --bb-upload-card-width: #{$bb-upload-card-width};
    --bb-upload-card-height: #{$bb-upload-card-height};
    --bb-upload-card-shadow: #{$bb-upload-card-shadow};
    --bb-upload-card-padding: #{$bb-upload-card-padding};
    --bb-upload-card-item-width: #{$bb-upload-card-item-width};
    --bb-upload-drop-height: #{$bb-upload-drop-height};
    --bb-upload-drop-footer-font-size: #{$bb-upload-drop-footer-font-size};
    --bb-upload-drop-footer-margin-top: #{$bb-upload-drop-footer-margin-top};
    --bb-upload-item-border-radius: #{$bb-upload-item-border-radius};
}

.upload .upload-body {
    margin-block-start: var(--bb-upload-body-margin-top);
}

.upload .upload-body.is-list {
    overflow: auto;
    max-height: var(--bb-upload-body-list-max-height);
}

.upload .upload-body.is-list .upload-item {
    display: flex;
    align-items: center;
    padding: var(--bb-upload-body-list-item-padding);
    border-radius: var(--bs-border-radius);
    transition: background-color .3s linear;
    cursor: pointer;
    position: relative;
}

.upload .upload-body.is-list .upload-item.is-invalid {
    color: var(--bs-danger);
}

.upload .upload-body.is-list .upload-item:hover {
    background-color: var(--bs-secondary-bg);
}

.upload .upload-body.is-list .upload-item .upload-item-body {
    flex: 1;
    padding: var(--bb-upload-body-list-item-body-padding);
    display: flex;
    overflow: hidden;
}

.upload .upload-body.is-list .upload-item .upload-item-body span:first-child {
    max-width: calc(100% - 4rem);
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
    padding-right: 0.25rem;
}

.upload .upload-body.is-avatar .upload-item .upload-item-delete,
.upload .upload-body.is-avatar .upload-item.is-invalid .upload-item-spin,
.upload .upload-body.is-avatar .upload-item.is-valid .upload-item-spin,
.upload .upload-item.is-invalid .valid,
.upload .upload-item.is-valid .invalid,
.upload .upload-body.is-avatar .upload-item.disabled .upload-item-plus {
    display: none;
}

.upload .upload-body.is-list .loading-icon,
.upload .upload-body.is-list .valid-icon {
    color: var(--bs-success);
}

.upload .upload-body.is-list .delete-icon,
.upload .upload-body.is-list .invalid-icon {
    color: var(--bs-danger);
}

.upload .upload-body.is-list .download-icon {
    color: var(--bs-primary);
}

.upload .upload-body.is-list .cancel-icon {
    margin-left: .5rem;
    color: var(--bs-danger);
}

.upload .upload-body.is-avatar,
.upload .upload-body.is-card {
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    position: relative;
}

.upload .upload-body.is-avatar .upload-item {
    padding: 0;
    position: relative;
    border: 1px dashed var(--bs-border-color);
    border-radius: 6px;
    overflow: hidden;
    cursor: pointer;
}

.upload .upload-body.is-avatar .upload-item.is-invalid {
    border-color: var(--bs-danger);
    border-style: solid;
}

.upload .upload-body.is-avatar .upload-item.is-circle {
    border-radius: var(--bb-upload-item-border-radius);
}

.upload .upload-body.is-avatar .upload-item:not(.is-form):not(.is-valid):not(.is-invalid):hover,
.upload .upload-body.is-avatar .upload-item:not(.is-form).is-valid,
.upload .upload-body.is-card .upload-item.is-valid,
.upload .upload-body.is-card .upload-item:not(.disabled):hover {
    border-color: var(--bb-upload-body-list-item-hover-color);
}

.upload .upload-body.is-avatar .upload-item.is-valid {
    border-style: solid;
}

.upload .upload-body.is-avatar .upload-item.is-invalid .avatar {
    color: var(--bs-danger);
}

.upload .upload-body.is-avatar .upload-item .avatar {
    width: 100%;
    height: 100%;
    background-color: var(--bs-body-bg);
}

.upload .upload-body.is-avatar .upload-item .upload-item-actions,
.upload .upload-body.is-avatar .upload-item-plus,
.upload .upload-body.is-card .upload-item-plus {
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload .upload-body.is-avatar .upload-item .upload-item-actions {
    position: absolute;
    inset: 0;
}

.upload .upload-body.is-avatar .upload-item .upload-item-actions i {
    font-size: 1rem;
}

.upload .upload-body.is-avatar .upload-item:hover .upload-item-actions .upload-item-delete {
    display: block;
    color: var(--bs-danger);
}

.upload .upload-body.is-avatar .upload-item.is-invalid .upload-item-spin {
    font-size: 3em;
}

.upload .upload-body.is-card .upload-item {
    padding: var(--bb-upload-card-padding);
    box-shadow: var(--bb-upload-card-shadow);
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    width: var(--bb-upload-card-width);
    height: var(--bb-upload-card-height);
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

.upload .upload-body.is-card .upload-item .upload-item-actions {
    display: flex;
    justify-content: space-between;
}

.upload .upload-body.is-card .upload-item.is-valid .upload-item-body img,
.upload .upload-body .upload-item.is-valid .upload-item-label,
.upload .upload-body .upload-item.is-invalid .upload-item-label {
    display: block;
}

.upload .upload-body.is-card .is-invalid .upload-item-body {
    border-color: var(--bs-danger);
}

.upload .upload-body.is-card .upload-item-body {
    border-radius: var(--bs-border-radius);
    width: var(--bb-upload-card-item-width);
    height: var(--bb-upload-card-item-width);
    border: 1px solid var(--bs-border-color);
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.upload .upload-body.is-card .upload-item-body img {
    width: 100%;
    object-fit: cover;
    display: none;
}

.upload .upload-body.is-card .upload-item-size {
    margin: 1rem auto;
    text-align: center;
    font-size: 0.625rem;
    display: flex;
    justify-content: center;
}

.upload .upload-body.is-card .upload-item-size span {
    max-width: calc(100% - 4.5rem);
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    white-space: nowrap;
    padding-right: 0.25rem;
}

.upload .upload-item .upload-item-label {
    position: absolute;
    top: -2px;
    right: -14px;
    text-align: center;
    transform: rotate(45deg);
    color: #fff;
    width: 46px;
    height: 20px;
    background-color: var(--bs-success);
    display: none;
}

.upload .upload-item .upload-item-label .valid-icon {
    transform: rotate(-45deg);
    font-size: 12px;
}

.upload .upload-item.is-invalid .upload-item-label {
    background-color: var(--bs-danger);
}

.upload .progress {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
}

.upload .upload-body.is-card .progress {
    left: 1rem;
    right: 1rem;
    bottom: 56px;
}

.upload .upload-item.is-valid .progress,
.upload .upload-item.is-invalid .progress {
    display: none;
}

.upload .upload-body.is-card.is-single .upload-item {
    margin: 0;
}

.upload-buttons i {
    width: 16px;
    text-align: center;
}

.upload-buttons i:not(:first-child) {
    margin-left: 4px;
}

.btn-group .upload:not(:last-child) > button {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .upload:not(:first-child) > button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group .upload .upload-body {
    display: none;
}

.upload.is-drop {

    &.dropping {
        .upload-drop-body {
            border-color: var(--bs-success);

            * {
                pointer-events: none;
            }
        }

        &.disabled {
            .upload-drop-body {
                border-color: var(--bs-danger);
            }
        }
    }

    &.disabled {
        .upload-drop-body {
            border-color: var(--bs-border-color);
        }
    }

    .upload-drop-body {
        border: 1px dashed var(--bs-primary);
        border-radius: var(--bs-border-radius);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: var(--bb-upload-drop-height);
        cursor: pointer;

        .upload-drop-icon > i {
            font-size: 3em;
            color: var(--bs-secondary);
        }

        .upload-drop-text {
            margin-block-start: 1rem;
        }

        em {
            color: var(--bs-primary);
            font-style: normal;
        }
    }

    .upload-drop-footer {
        font-size: var(--bb-upload-drop-footer-font-size);
        margin-block-start: var(--bb-upload-drop-footer-margin-top);
    }

    .upload-drop-list {
        margin: 10px 0 0;
        padding: 0;
        list-style: none;
        position: relative;

        .upload-drop-process {
            position: relative;
        }
    }
}
