@namespace BootstrapBlazor.Components
@typeparam TValue
@inherits FileListUploadBase<TValue>

@if (IsShowLabel)
{
    <BootstrapLabel required="@Required" ShowLabelTooltip="ShowLabelTooltip" Value="@DisplayText" />
}
<div @attributes="@AdditionalAttributes" class="@ClassString" id="@Id">
    <Button class="@BrowserButtonClassString" IsDisabled="@CheckStatus()" Size="Size" Icon="@BrowserButtonIcon" Text="@BrowserButtonText" Color="@BrowserButtonColor">
        @ChildContent
    </Button>
    @if (ShowUploadFileList)
    {
        <UploadPreviewList Items="Files" IsDisabled="@IsDisabled" ShowProgress="@ShowProgress"
                           OnGetFileFormat="@OnGetFileFormat" OnCancel="OnCancel" CancelIcon="@CancelIcon" LoadingIcon="@LoadingIcon"
                           InvalidStatusIcon="@InvalidStatusIcon" ValidStatusIcon="@ValidStatusIcon"
                           ShowDownloadButton="@ShowDownloadButton" DownloadIcon="@DownloadIcon" OnDownload="@OnDownload"
                           DeleteIcon="@DeleteIcon" OnDelete="@OnFileDelete">
        </UploadPreviewList>
    }
    <InputFile AdditionalAttributes="@GetUploadAdditionalAttributes()" OnChange="OnFileChange" />
</div>
