@namespace BootstrapBlazor.Components
@inherits FilterBase

@if (IsHeaderRow)
{
    <Select Items="@_lookup.Lookup" ShowLabel="false" SkipValidate="true"
            LookupService="@_lookup.LookupService" StringComparison="@_lookup.LookupStringComparison"
            LookupServiceKey="@_lookup.LookupServiceKey" LookupServiceData="@_lookup.LookupServiceData"
            @bind-Value="@_value" ShowSearch="@_isShowSearch"
            OnSelectedItemChanged="_ => OnFilterAsync()" IsPopover="true"></Select>
}
else
{
    <Select Items="@_lookup.Lookup" ShowLabel="false" SkipValidate="true"
            LookupService="@_lookup.LookupService" StringComparison="@_lookup.LookupStringComparison"
            LookupServiceKey="@_lookup.LookupServiceKey" LookupServiceData="@_lookup.LookupServiceData"
            @bind-Value="@_value" ShowSearch="@_isShowSearch"></Select>
}
