@namespace BootstrapBlazor.Components
@inherits ComponentBase

@if (IsHeaderRow)
{
    @RenderFilter
}
else
{
    <div class="card filter-item">
        <div class="card-header"><span>@Title</span></div>
        <div class="card-body">
            @RenderFilter
        </div>
        <div class="card-footer">
            <div class="d-flex flex-fill">
                @if (ShowMoreButton)
                {
                    <Button Color="Color.None" OnClick="OnClickPlus" Icon="@PlusIcon" IsDisabled="@(Count == 1)"></Button>
                    <Button Color="Color.None" OnClick="OnClickMinus" Icon="@MinusIcon" IsDisabled="@(Count == 0)"></Button>
                }
            </div>
            <Button Color="Color.None" class="filter-dismiss" OnClick="OnClickReset" Text="@ClearButtonText"></Button>
            <Button Color="Color.None" class="filter-dismiss" OnClick="OnClickConfirm" Text="@FilterButtonText"></Button>
        </div>
    </div>
}

@code {
    RenderFragment RenderFilter =>
    @<CascadingValue IsFixed="false" Value="@FilterContext">
        @ChildContent
    </CascadingValue>;
}
