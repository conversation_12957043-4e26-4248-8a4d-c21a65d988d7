@namespace BootstrapBlazor.Components
@inherits FilterBase
@typeparam TType

@if (IsHeaderRow)
{
    <div class="@FilterRowClassString">
        <MultiSelect class="is-filter" Items="@Items" @bind-Value="_value1" OnValueChanged="_ => OnFilterAsync()"
                     ShowLabel="false" SkipValidate="true" IsPopover="true"></MultiSelect>
    </div>
}
else
{
    <MultiSelect Items="@Items" @bind-Value="@_value1" IsPopover="true" ShowLabel="false" SkipValidate="true"></MultiSelect>
}
