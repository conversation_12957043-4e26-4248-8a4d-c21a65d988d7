@namespace BootstrapBlazor.Components
@inherits FilterBase

@if (IsHeaderRow)
{
    <div class="@FilterRowClassString">
        <BootstrapInput @bind-Value="@_value1" ShowLabel="false" SkipValidate="true"></BootstrapInput>
        <FilterButton Items="Items" @bind-Value="_action1" OnSelectedItemChanged="_ => OnFilterAsync()" OnClearFilter="OnClearFilter" />
    </div>
}
else
{
    <Select Items="@Items" @bind-Value="@_action1" ShowLabel="false" SkipValidate="true"></Select>
    <BootstrapInput @bind-Value="@_value1" ShowLabel="false" SkipValidate="true"></BootstrapInput>
    @if (Count > 0)
    {
        <FilterLogicItem @bind-Logic="Logic"></FilterLogicItem>
        <Select Items="@Items" @bind-Value="@_action2" ShowLabel="false" SkipValidate="true"></Select>
        <BootstrapInput @bind-Value="@_value2" ShowLabel="false" SkipValidate="true"></BootstrapInput>
    }
}
