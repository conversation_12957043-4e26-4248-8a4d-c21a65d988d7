.filter-item {
    --bb-filter-item-min-width: #{$bb-filter-item-min-width};
    --bb-filter-item-btn-hover-color: #{$bb-filter-item-btn-hover-color};
    --bb-filter-item-btn-hover-border-color: #{$bb-filter-item-btn-hover-border-color};
    min-width: var(--bb-filter-item-min-width);

    .card-body {
        > *:not(:first-child) {
            margin-block-start: 0.5rem;
        }
    }

    .card-footer {
        display: flex;
        white-space: nowrap;

        .btn:not(:first-child) {
            margin-left: 0.5rem;
        }

        .btn {
            line-height: 12px;
            border: 1px solid var(--bs-border-color);
            transition: border-color .3s linear, color .3s linear;
        }

        .btn:not(.disabled):not(:disabled):hover {
            border-color: var(--bb-filter-item-btn-hover-border-color);
            color: var(--bb-filter-item-btn-hover-color);
        }
    }
}

.filter-icon .filter-item {
    display: none;
}

.filter-row {
    --bb-filter-row-input-min-width: #{$bb-filter-row-input-min-width};
    display: flex;

    .btn-ban {
        display: none;
        padding-left: 0;
    }

    input {
        min-width: var(--bb-filter-row-input-min-width);
    }

    &.active .btn-ban {
        display: block;
    }

    .btn-filter::after {
        content: none;
    }
}
