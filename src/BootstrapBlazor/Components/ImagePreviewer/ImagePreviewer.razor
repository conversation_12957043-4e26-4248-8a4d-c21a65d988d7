@namespace BootstrapBlazor.Components
@inherits BootstrapModuleComponentBase
@attribute [BootstrapModuleAutoLoader]

<div tabindex="-1" class="bb-previewer collapse active" id="@Id" style="z-index: @ZIndex;">
    <div class="bb-viewer-mask"></div>
    <div class="bb-viewer-canvas">
        <img src="@GetFirstImageUrl()" class="bb-viewer-img" style="transform: scale(1) rotate(0deg); margin: 0;" />
    </div>
    <span class="bb-viewer-btn bb-viewer-close">
        <span></span>
    </span>
    @if (ShowButtons)
    {
        <span class="bb-viewer-btn bb-viewer-prev">
            <i class="@PreviousIcon"></i>
        </span>
        <span class="bb-viewer-btn bb-viewer-next">
            <i class="@NextIcon"></i>
        </span>
    }
    <div class="bb-viewer-btn bb-viewer-actions">
        <div class="bb-viewer-actions-inner">
            <i class="@MinusIconString"></i>
            <i class="@PlusIconString"></i>
            <i class="bb-viewer-actions-divider"></i>
            <span class="bb-viewer-full-screen"></span>
            <i class="bb-viewer-actions-divider"></i>
            <i class="@RotateLeftIconString"></i>
            <i class="@RotateRightIconString"></i>
        </div>
    </div>
</div>
