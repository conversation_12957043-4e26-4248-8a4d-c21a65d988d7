.select,
.popover-dropdown {
    --bb-dropdown-link-pre-active-bg: #{$bb-dropdown-link-pre-active-bg};
}

.select {
    --bb-select-focus-shadow: #{$bb-select-focus-shadow};
    --bb-select-padding-right: #{$bb-select-padding-right};
    --bb-select-padding: #{$bb-select-padding};
    --bb-select-append-width: #{$bb-select-append-width};
    --bb-select-append-color: #{$bb-select-append-color};
}

.select:not(.cascade) .dropdown-menu {
    width: 100%;
}

.cascade,
.select {
    --bb-select-dropdown-menu-margin-top: 8px;
}

.cascade .dropdown-menu,
.selec .dropdown-menu {
    margin-block-start: var(--bb-select-dropdown-menu-margin-top) !important;
}

.select .form-select {
    background-image: none;
    background-color: var(--bs-body-bg);
    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    padding: var(--bb-select-padding);
    padding-inline-end: var(--bb-select-padding-right);
    cursor: pointer;
}

.select .form-select:disabled {
    background-color: var(--bs-secondary-bg);
}

.dropdown-item {
    cursor: pointer;
}

.dropdown-item.preActive {
    background-color: var(--bb-dropdown-link-pre-active-bg);
}

.dropdown-menu-arrow {
    width: 0;
    height: 0;
    border-width: 0 6px 6px;
    border-style: solid;
    border-color: transparent transparent rgba(0,0,0,.15);
    position: absolute;
    left: 20px;
    margin-block-start: 4px;
    z-index: 1001;
    display: none;
}

.dropdown-menu-arrow:after {
    content: " ";
    width: 0;
    height: 0;
    border-width: 0 6px 6px;
    border-style: solid;
    border-color: transparent transparent var(--bs-body-bg);
    position: absolute;
    top: 1px;
    left: -6px;
}

[data-bs-theme='dark'] .dropdown-menu-arrow:after {
    content: none;
}

.show > .dropdown-menu,
.show > .dropdown-menu-arrow {
    display: block;
}

.form-select:focus {
    box-shadow: var(--bb-select-focus-shadow);
    border-color: var(--bb-border-focus-color);
}

.form-select:not(:disabled):hover {
    border-color: var(--bb-border-hover-color);
}

.form-select.show + .form-select-append i {
    transform: rotate(0);
}

.dropdown-menu[data-popper-placement="bottom-start"].show + .dropdown-menu-arrow,
.dropdown-menu[data-bs-popper="none"].show + .dropdown-menu-arrow {
    display: block;
}

.form-select-append {
    position: absolute;
    height: 100%;
    width: var(--bb-select-append-width);
    right: 0;
    top: 0;
    color: var(--bb-select-append-color);
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-select-append i {
    transition: all .3s;
    transform: rotate(180deg);
}

.show > .form-select-append i {
    transform: rotate(0);
}

.select,
.auto-complete {
    .clear-icon {
        position: absolute;
        height: 100%;
        width: var(--bb-select-append-width);
        right: 0;
        top: 0;
        color: var(--bb-select-append-color);
        align-items: center;
        justify-content: center;
        cursor: pointer;
        display: none;
    }

    &:hover .clear-icon {
        display: flex;
    }

    &.is-clearable:not(.disabled):hover .form-select-append {
        display: none;
    }
}

.form-select.is-valid:focus,
.was-validated .form-select:valid:focus,
.form-select.is-invalid:focus,
.was-validated .form-select:invalid:focus {
    box-shadow: none;
}

.form-select.is-valid:not([multiple]):not([size]),
.form-select.is-valid:not([multiple])[size="1"],
.was-validated .form-select:valid:not([multiple]):not([size]),
.was-validated .form-select:valid:not([multiple])[size="1"],
.form-select.is-invalid:not([multiple]):not([size]),
.form-select.is-invalid:not([multiple])[size="1"],
.was-validated .form-select:invalid:not([multiple]):not([size]),
.was-validated .form-select:invalid:not([multiple])[size="1"] {
    background-position: right -1rem center, center right 1.5rem;
    padding-inline-end: var(--bb-select-padding-right);
}

.arrow-danger {
    border-color: transparent transparent var(--bs-danger);
}

.arrow-success {
    border-color: transparent transparent var(--bs-success);
}

.arrow-primary {
    border-color: transparent transparent var(--bs-primary);
}

.arrow-warning {
    border-color: transparent transparent var(--bs-warning);
}

.arrow-info {
    border-color: transparent transparent var(--bs-info);
}

.dropdown-menu {
    --bb-select-search-padding: #{$bb-select-search-padding};
    --bb-select-search-margin-bottom: #{$bb-select-search-margin-bottom};
    --bb-select-search-padding-right: #{$bb-select-search-padding-right};
    --bb-select-search-border-color: #{$bb-select-search-border-color};
    --bb-select-search-icon-color: #{$bb-select-search-icon-color};
    --bb-select-search-icon-right: #{$bb-select-search-icon-right};
    --bb-select-search-icon-top: #{$bb-select-search-icon-top};
    --bb-select-search-height: #{$bb-select-search-height};

    .dropdown-menu-search {
        padding: var(--bb-select-search-padding);
        position: relative;
        border-block-end: var(--bs-border-width) solid var(--bb-select-search-border-color);
        margin-block-end: var(--bb-select-search-margin-bottom);

        &.l {
            .search-icon {
                display: none;
            }

            .searching-icon {
                display: block;
            }
        }

        .searching-icon {
            display: none;
        }
    }

    .dropdown-menu-body {
        max-height: var(--bb-dropdown-max-height);
        overflow: auto;
    }

    .search-text {
        padding-inline-end: var(--bb-select-search-padding-right);
    }

    .icon {
        position: absolute;
        right: var(--bb-select-search-icon-right);
        top: var(--bb-select-search-icon-top);
        color: var(--bb-select-search-icon-color);
        width: 1rem;
        height: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.select:not(.multi-select) .dropdown-toggle {
    position: relative;
}

.select .dropdown-toggle:after,
.btn-popover-confirm.dropdown-toggle:after {
    content: none;
}
