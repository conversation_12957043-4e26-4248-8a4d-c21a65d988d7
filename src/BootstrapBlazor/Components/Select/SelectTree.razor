@namespace BootstrapBlazor.Components
@typeparam TValue
@inherits PopoverSelectBase<TValue>
@attribute [BootstrapModuleAutoLoader("Select/SelectTree.razor.js")]

@if (IsShowLabel)
{
    <BootstrapLabel required="@Required" for="@InputId" ShowLabelTooltip="ShowLabelTooltip" Value="@DisplayText" />
}
<div @attributes="AdditionalAttributes" id="@Id" class="@ClassName">
    <div class="dropdown-toggle" data-bs-toggle="@ToggleString" data-bs-auto-close="outside" data-bs-offset="@OffsetString" data-bs-placement="@PlacementString" data-bs-custom-class="@SelectTreeCustomClassString">
        @if (IsEditable)
        {
            <input type="text" id="@InputId" disabled="@Disabled" placeholder="@PlaceHolder" class="@InputClassName" value="@CurrentValueAsString" @onchange="@OnChange" />
        }
        else
        {
            <input type="text" id="@InputId" readonly disabled="@Disabled" placeholder="@PlaceHolder" class="@InputClassName" value="@SelectedItem?.Text" />
        }
        <span class="@AppendClassName"><i class="@DropdownIcon"></i></span>
    </div>
    <div class="dropdown-menu">
        <TreeView TItem="TValue" Items="@Items" ShowIcon="ShowIcon"
                  OnTreeItemClick="OnItemClick" ModelEqualityComparer="@ModelEqualityComparer"
                  ShowSearch="ShowSearch" ShowResetSearchButton="ShowResetSearchButton"
                  CanExpandWhenDisabled="CanExpandWhenDisabled"
                  CustomKeyAttribute="@CustomKeyAttribute" OnExpandNodeAsync="@OnExpandNodeAsync" />
    </div>
    @if (!IsPopover)
    {
        <div class="dropdown-menu-arrow"></div>
    }
</div>
