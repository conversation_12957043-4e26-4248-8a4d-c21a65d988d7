[data-bs-theme='dark'] .layout {
    --bb-layout-header-background: #{$bb-layout-header-background-dark};
    --bb-layout-header-border-color: #{$bb-layout-header-border-color-dark};
    --bb-layout-banner-border-color: #{$bb-layout-banner-border-color-dark};
    --bb-layout-sidebar-banner-background: #{$bb-layout-sidebar-banner-background-dark};
    --bb-layout-headerbar-background: #{$bb-layout-headerbar-background-dark};
    --bb-layout-menu-item-hover-bg: #{$bb-layout-menu-item-hover-bg-dark};
    --bb-layout-menu-user-banner-background: #{$bb-layout-menu-user-banner-background-dark};
    --bb-layout-menu-user-border-color: #{$bb-layout-menu-user-border-color-dark};
    --bb-layout-logo-border-color: #{$bb-layout-logo-border-color-dark};
    --bb-layout-logo-bg: #{$bb-layout-logo-bg-dark};
}

.layout {
    --bb-layout-header-height: #{$bb-layout-header-height};
    --bb-layout-header-background: #{$bb-layout-header-background};
    --bb-layout-header-color: #{$bb-layout-header-color};
    --bb-layout-header-border-color: #{$bb-layout-header-border-color};
    --bb-layout-headerbar-color: #{$bb-layout-header-color};
    --bb-layout-headerbar-background: #{$bb-layout-headerbar-background};
    --bb-layout-headerbar-border-color: #{$bb-layout-headerbar-border-color};
    --bb-layout-headerbar-padding: #{$bb-layout-headerbar-padding};
    --bb-layout-footer-background: #{$bb-layout-footer-background};
    --bb-layout-footer-color: #{$bb-layout-footer-color};
    --bb-layout-footer-height: #{$bb-layout-footer-height};
    --bb-layout-sidebar-width: #{$bb-layout-sidebar-width};
    --bb-layout-sidebar-collapse-width: #{$bb-layout-sidebar-collapse-width};
    --bb-layout-sidebar-banner-background: #{$bb-layout-sidebar-banner-background};
    --bb-layout-sidebar-background: #{$bb-layout-sidebar-background};
    --bb-layout-sidebar-color: #{$bb-layout-sidebar-color};
    --bb-layout-title-color: #{$bb-layout-title-color};
    --bb-layout-title-margin-left: #{$bb-layout-title-margin-left};
    --bb-layout-banner-font-size: #{$bb-layout-banner-font-size};
    --bb-layout-banner-logo-width: #{$bb-layout-banner-logo-width};
    --bb-layout-logo-border-color: #{$bb-layout-logo-border-color};
    --bb-layout-logo-bg: #{$bb-layout-logo-bg};
    --bb-layout-banner-border-color: #{$bb-layout-banner-border-color};
    --bb-layout-menu-user-banner-background: #{$bb-layout-menu-user-banner-background};
    --bb-layout-menu-user-border-color: #{$bb-layout-menu-user-border-color};
    --bb-layout-menu-item-hover-bg: #{$bb-layout-menu-item-hover-bg};
    display: flex;
    min-height: var(--bb-layout-height, 100%);
    width: 100%;
    flex-direction: column;
    position: relative;

    &.is-page {
        --bb-layout-height: 100vh;

        .layout-main {
            min-height: calc(var(--bb-layout-height) - var(--bb-layout-header-height) - var(--bb-layout-footer-height));

            > .tabs {
                margin: -1rem;
                border: none;
                border-radius: unset;
                min-height: calc(100% + 2rem);
            }
        }
    }

    .layout-banner {
        display: flex;
        align-items: center;
        padding: 0 0.625rem;
        height: var(--bb-layout-header-height);
        font-size: var(--bb-layout-banner-font-size);
        border-bottom: 1px solid var(--bb-layout-banner-border-color);
        background-color: var(--bb-layout-sidebar-banner-background);

        .layout-title {
            display: inline-block;
            color: var(--bb-layout-title-color);
            overflow: hidden;
            white-space: nowrap;
            flex: 1 1 auto;
            opacity: 1;
            transition: opacity .3s linear;
        }

        .layout-logo {
            width: var(--bb-layout-banner-logo-width);
            border-radius: var(--bs-border-radius);
            background-color: var(--bb-layout-logo-bg);
            border: 1px solid var(--bb-layout-logo-border-color);

            + .layout-title {
                margin-left: var(--bb-layout-title-margin-left);
            }
        }
    }

    .has-sidebar {
        position: relative;
        flex: 1;

        .layout-banner {
            display: none;
        }

        .layout-menu {
            height: 100%;
        }

        .layout-main {
            width: 1%;
            min-width: 0;
        }
    }

    .layout-menu {
        overflow-x: hidden;
        padding: 0.5rem 0;
        height: calc(100% - var(--bb-layout-header-height));

        .menu {
            --bb-menu-item-hover-bg: var(--bb-layout-menu-item-hover-bg);
            --bb-menu-item-hover-color: var(--bb-layout-header-color);
        }
    }

    .layout-side {
        background-color: var(--bb-layout-sidebar-background);
        color: var(--bb-layout-sidebar-color);
        transition: transform .3s linear;
        transform: translateX(-100%);
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 5;
    }

    .layout-main {
        padding: 1rem;
        position: relative;
        flex: 1;

        > .tabs:fullscreen {
            --bb-layout-header-height: 0px;
            --bb-layout-footer-height: 0px;
        }

        .tabs.tabs-border-card {
            box-shadow: none;
        }
    }

    .layout-header {
        background-color: var(--bb-layout-header-background);
        height: var(--bb-layout-header-height);
        color: var(--bb-layout-header-color);
        display: flex;
        align-items: center;
        padding: 0 1rem;
        position: sticky;
        z-index: 1035;
        border-bottom: 1px solid var(--bb-layout-header-border-color);

        &.is-fixed {
            top: 0;
        }

        .widget {
            --bb-widget-toggle-color: var(--bb-layout-header-color);

            .progress {
                height: 7px;
            }
        }

        .logout-avatar {
            border-radius: 50%;
        }

        .dropdown-logout {
            --bb-logout-text-color: var(--bb-layout-header-color);
            --bb-logout-user-bg: var(--bb-layout-menu-user-banner-background);
            --bb-logout-menu-border-color: var(--bb-layout-menu-user-border-color);

            .dropdown-user img {
                border-radius: 50%;
            }
        }

        .layout-header-bar {
            padding: var(--bb-layout-headerbar-padding);
            color: var(--bb-layout-headerbar-color);
            background-color: var(--bb-layout-headerbar-background);
            border: var(--bs-border-width) solid var(--bb-layout-headerbar-border-color);
            border-radius: var(--bs-border-radius);
            cursor: pointer;

            .fa-bars {
                transition: transform .3s linear;
            }
        }

        > .tabs {
            height: 100%;
            display: flex;
            justify-content: flex-end;
            flex: 1;
            width: 1%;
            min-width: 0;
            margin-bottom: -2px;
        }

        > .tabs.tabs-chrome {
            .tabs-header {
                --bb-tabs-item-height: 47px;
                --bb-tabs-header-color: #fff;
                --bb-tabs-chrome-item-height: 43px;
                --bs-border-color: var(--bb-layout-headerbar-background);
                --bb-tabs-header-bg-color: var(--bb-layout-headerbar-background);

                .tabs-item-close:hover {
                    background-color: transparent;
                }

                .tabs-item-wrap:not(.active) .tabs-item {
                    color: var(--bb-tabs-header-color);
                }

                .tabs-nav-toolbar .btn {
                    --bs-btn-color: var(--bb-tabs-header-color);
                }

                .nav-link-bar {
                    --bs-body-color: var(--bb-tabs-header-color);
                }
            }
        }
    }

    &:not(.has-footer) {
        --bb-layout-footer-height: 0px;
    }

    &.is-collapsed {
        .fa-bars {
            transform: rotate(90deg);
        }

        .layout-side {
            transform: translateX(0);
        }

        .layout-footer {
            display: none;
        }

        .has-sidebar {
            .layout-main {
                overflow: hidden;
                height: calc(var(--bb-layout-height) - var(--bb-layout-header-height));
            }
        }
    }

    .layout-footer {
        height: var(--bb-layout-footer-height);
        color: var(--bb-layout-footer-color);
        background-color: var(--bb-layout-footer-background);
        display: flex;
        align-items: center;
        padding: 0 1rem;
        border-top: 1px solid var(--bs-border-color);

        &.is-fixed {
            z-index: 1035;
            position: sticky;
            bottom: 0;
        }
    }

    &.is-fixed-tab {
        .layout-menu,
        .layout-main > .tabs {
            height: calc(var(--bb-layout-height) - var(--bb-layout-header-height) - var(--bb-layout-footer-height));

            > .tabs-body {
                overflow: auto;
            }
        }
    }

    .layout-right {
        display: flex;
        flex-flow: column;
        flex: 1 1 auto;
        width: 1%;
        min-width: 0;
    }
}

.has-sidebar {
    flex-direction: row;
    display: flex;
}

@media (max-width: 767.99px) {
    .layout.is-collapsed .layout-right {
        overflow: hidden;
        height: var(--bb-layout-height);
    }
}

@media(min-width: 768px) {
    .layout {
        .layout-side {
            position: relative;
            width: var(--bb-layout-sidebar-width);
            transform: translateX(0);
            flex-shrink: 0;

            .layout-menu {
                border-right: 1px solid var(--bs-border-color);
            }
        }

        &:not(.drag) .layout-side {
            transition: width .3s linear;
        }

        &.has-sidebar {
            .layout-side {
                &.is-fixed-header {
                    position: sticky;
                    top: 0;
                    height: var(--bb-layout-height);
                }

                .layout-menu {
                    height: calc(var(--bb-layout-height) - var(--bb-layout-header-height));
                }
            }
        }

        &.is-collapsed {
            .layout-side {
                &:not(:hover) {
                    --bb-layout-sidebar-width: var(--bb-layout-sidebar-collapse-width);

                    .menu.is-vertical {
                        --bb-layout-sidebar-width: var(--bb-layout-sidebar-collapse-width);
                    }
                }

                &:hover {
                    .layout-title,
                    .menu.is-vertical .menu-text {
                        opacity: 1;
                    }
                }
            }

            .layout-main {
                display: block;
            }

            .layout-footer {
                display: flex;
            }

            .layout-title,
            .menu.is-vertical .menu-text {
                opacity: 0;
            }
        }
    }
}
