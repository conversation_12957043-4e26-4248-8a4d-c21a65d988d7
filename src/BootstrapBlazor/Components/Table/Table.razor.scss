.table-container {
    --bb-table-td-padding-x: .5rem;
    --bb-table-td-padding-y: .5rem;
    --bb-table-cardview-label-width: 30%;
    --bb-table-header-hover-bg: #e9ecef;
    --bb-table-header-icon-color: #ddd;
    --bb-table-header-icon-active-color: #409eff;
    --bb-table-header-icon-hover-bg: #ddd;
    --bb-table-header-icon-hover-color: #606266;
    --bb-table-header-min-height: 37px;
    --bb-table-footer-font-weight: blod;
    --bb-table-card-row-padding: .75rem .5rem;
    --bb-table-columnlist-max-height: var(--bb-dropdown-max-height);
    --bs-table-striped-bg: rgba(0, 0, 0, .05);
    --bs-table-hover-bg: rgba(0, 0, 0, .075);
    --bb-table-search-body-margin: 1rem;
    --bb-table-copy-column-margin-right: .5rem;
    --bb-table-column-fixed-border-color: rgba(var(--bs-body-color-rgb), .18);
    --bb-loader-bg: rgba(var(--bs-body-color-rgb), .2);
    --bb-table-column-resizer-color: #ddd;
    --bb-table-column-resizer-hover-color: #ddd;
    --bb-table-row-active-bg: rgba(var(--bs-body-color-rgb), .08);
    --bb-table-row-hover-bg: rgba(var(--bs-body-color-rgb), .1);
    --bb-table-pagination-color: var(--bs-body-color);
    --bb-table-pagination-active-color: var(--bs-body-color);
    --bb-table-pagination-active-bg: rgba(var(--bs-body-color-rgb), .1);
    --bb-table-pagination-active-border-color: rgba(var(--bs-body-color-rgb), .15);
    position: relative;
    height: 100%;

    .nav-pages {
        margin-block-start: .5rem;

        .pagination {
            --bs-pagination-color: var(--bb-table-pagination-color);
            --bs-pagination-active-bg: var(--bb-table-pagination-active-bg);
            --bs-pagination-active-color: var(--bb-table-pagination-active-color);
            --bs-pagination-active-border-color: var(--bb-table-pagination-active-border-color);
        }
    }

    .table-card {
        &.table-fixed {
            overflow: auto;
        }
    }
}

.table-container .table:not(.table-excel) .switch {
    --bb-switch-padding: 0;
}

.table-container .table > :not(caption) > * > * {
    padding: var(--bb-table-td-padding-y) var(--bb-table-td-padding-x);
}

.table-excel {
    --bb-border-hover-color: transparent;
    --bb-border-focus-color: transparent;
}

[data-bs-theme='dark'] .table,
.table-dark {
    --bb-table-header-hover-bg: #343a40;
    --bb-table-header-icon-hover-bg: #6c757d;
    --bb-table-header-icon-hover-color: #495057;
}

.table-light {
    --bb-table-header-icon-color: #c0c4cc;
    --bb-table-header-icon-hover-color: #495057;
}

.table-sm {
    --bb-table-td-padding-x: .25rem;
    --bb-table-td-padding-y: .25rem;
}

.filter-header th {
    --bb-table-td-padding-x: 0;
    --bb-table-td-padding-y: 0;
}

.filter-header th:hover {
    --bb-table-header-hover-bg: transparent;
}

.table-wrapper {
    border-radius: var(--bs-border-radius);
    border: 1px solid var(--bs-border-color);
}

.table-wrapper thead tr:first-child {
    border-top: none;
}

.table-wrapper tbody tr:last-child {
    border-bottom: none;
}

.table-wrapper th:first-child,
.table-wrapper td:first-child,
.table-wrapper th.fixed-scroll {
    border-left-width: 0;
}

.table-wrapper th:last-child,
.table-wrapper td:last-child,
.table-wrapper .table-fixed-header th:nth-last-of-type(2):not(.border-resize) {
    border-right-width: 0;
}

.table-wrapper tr:last-child td {
    border-bottom-width: 0;
}

.table-wrapper .table-fixed-body td:last-child {
    border-right-width: 1px;
}

.table {
    margin-block-end: 0;
}

.table thead th, .table tbody td {
    border-top: none;
}

.table thead th {
    white-space: nowrap;
    vertical-align: top;
}

.table thead th.sortable .table-text {
    cursor: pointer;
}

.table thead th.sortable .table-text,
.table thead th.filterable .table-text {
    padding-right: 1rem;
}

.table thead th.sortable:hover,
.table thead th.filterable:hover {
    background-color: var(--bb-table-header-hover-bg);
}

.table thead th .table-cell {
    position: relative;
}

.table thead th .table-text {
    flex: 1;
}

.table thead th .filter-icon,
.table thead th .sort-icon,
.table thead th .toolbox-icon {
    position: absolute;
    right: calc(0px - var(--bb-table-td-padding-x));
    top: calc(0px - var(--bb-table-td-padding-y));
    bottom: calc(0px - var(--bb-table-td-padding-y));
}

.table thead th .filter-icon > i,
.table thead th .sort-icon,
.table thead th .toolbox-icon {
    width: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bb-table-header-icon-color);
    cursor: pointer;
}

.table thead th .filter-icon > i {
    height: 100%;
}

.table thead th .filter-icon > i:hover,
.table thead th .sort-icon:hover,
.table thead th .toolbox-icon:hover {
    background-color: var(--bb-table-header-icon-hover-bg);
    color: var(--bb-table-header-icon-hover-color);
}

.table thead th .filter-icon > i.active {
    color: var(--bb-table-header-icon-active-color);
}

.table thead th.sortable.filterable .filter-icon,
.table thead th.sortable .toolbox-icon,
.table thead th.filterable .toolbox-icon {
    right: calc(1.5rem - var(--bb-table-td-padding-x));
}

.table thead th.sortable.filterable.toolbox .toolbox-icon {
    right: calc(3rem - var(--bb-table-td-padding-x));
}

.table thead th.sortable.filterable .table-text {
    padding-right: 2.5rem;
    overflow: hidden;
    text-overflow: ellipsis;
}

.table .table-footer {
    font-weight: var(--bb-table-footer-font-weight);
    position: relative;

    &:before {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        height: 2px;
        background-color: var(--bs-border-color);
    }

    &.table-footer-fixed {
        position: sticky;
        z-index: 2;
        bottom: 0;

        > tr > td {
            border-bottom-width: 0;
        }
    }

    > tr:last-child {
        border-bottom: none;
    }
}

.table td, .table th {
    min-width: 0;
    text-overflow: ellipsis;
}

.table .is-editform .table-cell {
    overflow: hidden;
}

.is-clickable tbody tr,
.is-clickable .table-row {
    cursor: pointer;
}

.table-cell {
    display: flex;
    align-items: center;
    word-break: break-all;
}

.table-cell.center {
    justify-content: center;
}

.table-cell.center > * {
    text-align: center;
}

.table-cell.center > .switch {
    justify-content: center;
}

.table-cell.end {
    justify-content: right;
}

.table-cell.end > * {
    text-align: right;
}

.table-cell.end > .switch {
    justify-content: flex-end;
}

.table-cell .btn-group .btn {
    white-space: nowrap;
}

.table-cell > .form-control,
.table-cell > .select,
.table-cell > .datetime-picker {
    --bs-border-color: transparent;
}

.table-hover tbody tr.is-detail:hover,
.table-hover tbody tr.is-editform,
.table-hover tbody tr.is-editform:hover {
    --bs-table-accent-bg: unset;
}

.table tbody tr.is-master td:first-child {
    padding-left: 0;
    padding-right: 0;
}

.table-toolbar:after {
    content: "";
    display: block;
    clear: both;
}

.table-toolbar .table-toolbar-button {
    margin-block-end: .5rem;
}

.table-toolbar .dropdown-menu {
    max-height: var(--bb-table-columnlist-max-height);
    overflow: auto;
}

.table-toolbar .dropdown-menu .dropdown-item span {
    margin-left: .5rem;
}

.table-toolbar .form-check.is-label {
    display: flex;
}

.btn-gear .dropdown-menu {
    min-width: initial;
    padding: 0;
}

.btn-gear .dropdown-menu .dropdown-item {
    padding: 6px 12px;
    display: table-cell;

    &:not(.disabled) {
        color: #504d4d;
    }

    &.disabled {
        pointer-events: auto;
    }
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-primary {
    background-color: var(--bs-primary);
    color: #fff;
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-secondary {
    background-color: var(--bs-secondary);
    color: #fff;
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-success {
    background-color: var(--bs-success);
    color: #fff;
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-danger {
    background-color: var(--bs-danger);
    color: #fff;
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-warning {
    background-color: var(--bs-warning);
    color: #fff;
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-info {
    background-color: var(--bs-info);
    color: #fff;
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-dark {
    background-color: var(--bs-dark);
    color: #fff;
}

.table-toolbar-button .btn-gear .dropdown-menu .dropdown-item-btn-light {
    background-color: var(--bs-light);
    color: #212529;
}

.btn-gear .dropdown-menu .dropdown-item:not(:first-child) {
    border-inline-start: solid 1px #aeb2b7;
}

.table-column-right {
    margin-inline-start: 0.3125rem;
}

.search-input-tooltip {
    font-size: 0.75rem;
}

.search-input-tooltip kbd {
    display: inline-block;
    background: #17a2b8;
    padding: 1px 6px;
}

.table-layout-fixed {
    table-layout: fixed;
}

.table-fixed {
    height: 100%;
    overflow: hidden;

    .table-fixed-body {
        overflow-x: auto;
        overflow-y: scroll;
    }
}

.table-fixed-header {
    overflow: hidden;
    border-top-left-radius: var(--bs-border-radius);
    border-top-right-radius: var(--bs-border-radius);
}

.table-scroll {
    overflow: auto;
}

.table-fixed-body > .table > tbody > tr > td,
.table-fixed-header > .table > thead > tr > th {
    overflow: hidden;
}

td > .table-cell {
    overflow: hidden;
}

.table tbody td .table-cell:not(.is-wrap) {
    white-space: nowrap;
}

.table-cell.is-ellips {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-cell.is-ellips.is-resizable {
    position: absolute;
    left: 12px;
    right: 12px;
}

.table-loading,
.table-loader,
.form-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    border: var(--bs-border-width) solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    padding: 2rem;
    flex: 1;
}

.table-loader,
.form-loader {
    display: none;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: var(--bb-loader-bg);
    z-index: 5;
}

.table-loader.show,
.form-loader.show {
    display: flex;
}

.table tbody tr.active:not(.is-edit),
.table-row.active {
    --bs-table-bg-type: var(--bb-table-row-active-bg);
}


.table-striped > tbody > tr.active:not(.is-edit):nth-of-type(odd) > * {
    --bs-table-bg-type: var(--bb-table-row-active-bg);
}

tr.active:not(.is-edit):hover {
    --bs-table-hover-bg: var(--bb-table-row-hover-bg);
}

.table-hover > tbody > tr.is-detail:hover,
.table-hover > tbody > tr.is-edit.active,
.table-excel > tbody > tr:hover {
    --bs-table-accent-bg: none;
}

.table-row {
    padding: var(--bb-table-card-row-padding);
}

.table-row:not(:last-child) {
    border-bottom: var(--bs-border-width) solid var(--bs-border-color);
}

.table-row .table-cell:not(:last-child) {
    padding-bottom: 0.5rem;
}

.table-row .table-cell label:not(.form-check) {
    font-weight: bold;
    margin-block-end: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-inline-end: 6px;
    flex-basis: var(--bb-table-cardview-label-width);
    flex-shrink: 0;
}

.table-row .table-cell > span {
    display: inherit;
}

.table-row.table-footer {
    display: flex;
}

.table-row.table-footer .table-cell {
    padding-bottom: 0;
}

.table-row.table-footer .table-cell:first-child {
    font-weight: bold;
    width: var(--bb-table-cardview-label-width);
    margin-block-end: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-striped .table-row:nth-of-type(odd) {
    background-color: var(--bs-table-striped-bg);
}

.table-striped .table-row:hover {
    background-color: var(--bb-table-row-hover-bg);
}

.table-fixed-column {
    .table {
        table-layout: fixed;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table-bordered > :not(caption) > * > * {
        border-width: 1px var(--bs-border-width);
    }
}

.table-fixed-column .table .fixed,
.table-fixed-column .table .fixed-scroll {
    background-color: var(--bs-table-bg);
}

.table-fixed-column .table th,
.table-fixed-column .table td {
    border-left-width: 0;
    border-top-width: 0;
}

.table-fixed-column .table .table-light .fixed,
.table-fixed-column .table .table-light .fixed-scroll,
.table-fixed-column .table .table-dark .fixed,
.table-fixed-column .table .table-dark .fixed-scroll {
    background-color: var(--bs-table-bg);
}

.table-fixed-column .table .fixed {
    position: sticky;
    z-index: 2;
    overflow: unset;
}

.table-fixed-column .table .fixed.fr {
    border-right: 1px solid var(--bb-table-column-fixed-border-color);
}

.table-fixed-column .table .fixed.fr:after {
    box-shadow: inset 10px 0 8px -8px rgb(159 159 159 / 22%);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 30px;
    transform: translateX(100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none;
}

.table-fixed-column .table .fixed-right.fl {
    border-left: 1px solid var(--bb-table-column-fixed-border-color);
}

.table-fixed-column .table .fixed.fl:after {
    box-shadow: inset -10px 0 8px -8px rgb(159 159 159 / 22%);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 30px;
    transform: translateX(-100%);
    transition: box-shadow .3s;
    content: "";
    pointer-events: none;
}

.table-fixed-column .fixed-scroll {
    position: sticky;
    right: 0;
    background-color: var(--bs-table-bg);
}

.table .is-bar {
    padding: 0;
    justify-content: center;
}

.table .is-bar .fa-caret-right {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: background-color .3s linear, transform .3s linear;
}

.table .is-bar .fa-caret-right:hover {
    background-color: #ddd;
}

.table .is-detail {
    display: none;
}

.table .is-detail.show {
    display: table-row;
}

.table .table-cell .table-container,
.table .table-cell .tabs,
.table .table-cell form {
    flex: 1;
}

.table-resize thead th,
.table-resize tbody td {
    position: relative;
}

.col-resizer {
    width: .25rem;
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    cursor: col-resize;
    border-right: 1px solid var(--bb-table-column-resizer-color);
    transition: background-color .3s linear;

    &:hover {
        border-radius: 2px;
        background-color: var(--bb-table-column-resizer-hover-color);
    }
}

.table-resize .border-resize {
    border-right: 1px solid #409eff;
}

.table .table-cell.is-tree {
    cursor: pointer;
}

.table-cell.is-incell {
    margin: -7px -6px;
}

.table-cell.is-incell .switch {
    padding: 7px 6px;
}

.table-cell.is-incell .select {
    width: 100%;
}

.table-cell .is-node .is-tree {
    transition: transform .3s linear;
}

.table-sm .table-cell.is-incell {
    margin: -.25rem;
}

.table-sm .table-cell.is-incell .form-control {
    height: calc(1.5em + .75rem - 3px);
}

.table-sm .table-cell.is-incell .switch {
    height: 30px;
    padding-left: 4px;
}

.table-bordered thead tr:last-child > th {
    border-bottom-width: 2px;
    border-bottom-color: rgba(var(--bs-body-color-rgb), 0.125);
}

.table-striped > tbody > tr.is-master:nth-of-type(4n+1) {
    --bs-table-accent-bg: var(--bs-table-striped-bg);
    color: var(--bs-table-striped-color);
}

.table-wrapper .empty-text {
    padding: 1rem;
}

form .table .table-cell > .form-label {
    display: none;
}

form .table .table-cell > textarea {
    width: 100%;
}

.table-search {
    margin-block-end: .5rem;

    .card-header {
        --bs-card-cap-padding-y: 0;
        min-height: var(--bb-table-header-min-height);

        .card-title {
            display: none;
        }
    }
}

.table-toolbar-template {
    margin-block-end: .5rem;
    min-height: 35px;
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-start;
}

@media (min-width: 576px) {
    .table-search .card-header .card-title {
        display: initial;
    }
}

.table-search .card-header .input-group {
    width: auto;
    flex-wrap: nowrap;
}

.table-search .card-header .input-group .table-toolbar-search,
.table-search .card-header .input-group .btn {
    height: calc(var(--bb-table-header-min-height) - 4px);
}

.table-search .card-header [aria-expanded="true"] + .input-group,
.table-search .card-header [aria-expanded="false"] + .input-group + .table-search-buttons {
    display: none;
}

.table-excel-toolbar {
    display: none;
    position: absolute;
}

.table-excel.table > :not(caption):not(thead) > * > * {
    padding: 0;
}

.table-excel.table > :not(caption):not(thead) > * > .table-column-button {
    padding: 6px 12px;
}

.table-excel > tbody .is-bar {
    padding: .5rem 0;
}

.table-excel > tbody > tr > td > .table-cell > .form-check {
    padding: 6px 8px;
}

.table-excel > tbody > tr > td > .col-line-no {
    padding: 7px 0;
}

.table-excel .active > td > .table-cell .form-control,
.table-excel .active > td > .table-cell .form-select {
    background-color: transparent;
}

.table-excel .table-cell .select {
    width: 100%;
}

.table-excel .table-cell > .disabled {
    background-color: var(--bs-secondary-bg);
    width: 100%;
    padding: .375rem .1875rem;
}

.table-excel .datetime-picker-input {
    padding-left: 2rem;
}

.table-excel .datetime-picker-input-icon {
    line-height: 32px;
}

.table-cell.text-center,
.table-cell.text-center .switch {
    justify-content: center;
}

.table-cell.text-center input {
    text-align: center;
}

.table-cell.text-end,
.table-cell.text-end .switch {
    justify-content: end;
}

.table-cell.text-end input {
    text-align: right;
}

.table-cell > .progress {
    flex: 1;
}

.is-ph {
    height: 12.5px;
    background-color: #e9ecef;
    border-radius: var(--bs-border-radius);
    margin: 5px 0;
}

.table-cell .is-dbcell {
    display: flex;
    cursor: pointer;
    position: relative;
}

.table-cell .is-dbcell:hover:before {
    content: "";
    position: absolute;
    bottom: -3px;
    height: 1px;
    width: 100%;
    background-color: var(--bs-primary);
}

.table-cell .is-node {
    width: 18px;
}

.table-cell .tag {
    line-height: 22px;
}

.table-cell .is-color {
    width: 28px;
    height: 21px;
    border-radius: 0.25rem;
}

.form-footer {
    margin-block-start: .5rem;
}

.modal-body .form-footer {
    margin: 1rem -1rem -.5rem -1rem;
    padding: .5rem 1rem 0 1rem;
    border-top: 1px solid var(--bs-border-color);
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.modal-body .form-footer button:not(:last-child) {
    margin-inline-end: .25rem;
}

.modal-dialog-table.modal-dialog-scrollable .modal-body {
    display: flex;
    flex-direction: column;
}

.modal-dialog-table.modal-dialog-scrollable .modal-body > form {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex: 1;
    margin: -1rem;
}

.modal-dialog-table.modal-dialog-scrollable .modal-body .form-body {
    overflow-y: auto;
    overflow-x: hidden;
    padding: 1rem;
}

.modal-dialog-table.modal-dialog-scrollable .modal-body .form-footer {
    margin: 0;
    padding: 1rem;
}

.table-wrap thead th .table-cell .table-text {
    white-space: pre-wrap;
}

.table-page-info {
    display: flex;
    align-items: center;
}

.table-page-info .select {
    width: 120px;
}

.col-copy {
    cursor: pointer;
    margin-inline-end: var(--bb-table-copy-column-margin-right);
}

.table-drag-over {
    animation: drag-column 1s linear infinite;
}

.table-dragging th[draggable] * {
    pointer-events: none;
}

.table-drag {
    background-color: #ddd !important;
}

@keyframes drag-column {
    50% {
        border-bottom-color: var(--bs-primary);
    }
}
