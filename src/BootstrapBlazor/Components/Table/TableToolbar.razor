@namespace BootstrapBlazor.Components
@typeparam TItem

<div class="float-start table-toolbar-button">
    <CascadingValue Value="this" IsFixed="true">
        @ChildContent
    </CascadingValue>
    <RenderTemplate>
        @if (_buttons.Count > 0)
        {
            <div class="@ToolbarClassString">
                @foreach (var button in _buttons)
                {
                    @if (button is TableToolbarButton<TItem> { IsShow: true } b)
                    {
                        <Button AdditionalAttributes="b.AdditionalAttributes" Size="b.Size"
                                Color="@b.Color" Icon="@b.Icon" Text="@b.Text"
                                OnClickWithoutRender="() => OnToolbarButtonClick(b)"
                                TooltipText="@b.TooltipText" TooltipPlacement="@b.TooltipPlacement" TooltipTrigger="@b.TooltipTrigger"
                                IsAsync="@b.IsAsync" IsDisabled="GetDisabled(b)" IsKeepDisabled="@b.<PERSON>KeepDisabled"
                                IsBlock="@b.IsBlock" IsOutline="@b.IsOutline"></Button>
                    }
                    else if (button is TableToolbarPopConfirmButton<TItem> { IsShow: true } pb)
                    {
                        <PopConfirmButton AdditionalAttributes="pb.AdditionalAttributes" CustomClass="@pb.CustomClass"
                                          Color="@pb.Color" Icon="@pb.Icon" Text="@pb.Text" Size="pb.Size" ShowShadow="@pb.ShowShadow"
                                          IsAsync="@pb.IsAsync" IsDisabled="GetDisabled(pb)" IsBlock="@pb.IsBlock" IsOutline="@pb.IsOutline"
                                          OnBeforeClick="@pb.OnBeforeClick" OnClose="@pb.OnClose" OnConfirm="() => OnConfirm(pb)"
                                          ConfirmIcon="@pb.ConfirmIcon" ConfirmButtonColor="@pb.ConfirmButtonColor"
                                          ConfirmButtonText="@pb.ConfirmButtonText" CloseButtonColor="@pb.CloseButtonColor"
                                          Placement="@pb.Placement" Trigger="@pb.Trigger"
                                          CloseButtonText="@pb.CloseButtonText" Content="@pb.Content">
                        </PopConfirmButton>
                    }
                    else if (button is TableToolbarComponent<TItem> { IsShow: true } cb)
                    {
                        <CascadingValue Value="OnGetSelectedRows" IsFixed="true">
                            @cb.ChildContent
                        </CascadingValue>
                    }
                }
            </div>
            @if (IsAutoCollapsedToolbarButton)
            {
                <div class="btn-gear btn-group d-sm-none">
                    <button class="btn btn-secondary dropdown-toggle" data-bs-toggle="dropdown" type="button">
                        <i class="@GearIcon"></i>
                    </button>
                    <div class="dropdown-menu">
                        @foreach (var button in _buttons)
                        {
                            @switch (button)
                            {
                                case TableToolbarButton<TItem> { IsShow: true } b:
                                {
                                    <DynamicElement class="@GetItemClass(b)" TriggerClick="!GetDisabled(b)" OnClick="() => OnToolbarButtonClick(b)">
                                        @if (string.IsNullOrEmpty(b.Icon))
                                        {
                                            <span>@b.Text</span>
                                        }
                                        else
                                        {
                                            <i class="@b.Icon"></i>
                                        }
                                    </DynamicElement>
                                    break;
                                }
                                case TableToolbarPopConfirmButton<TItem> { IsShow: true } pb:
                                {
                                    <DynamicElement class="@GetItemClass(pb)" TriggerClick="!GetDisabled(pb)" OnClick="pb.OnConfirm">
                                        @if (string.IsNullOrEmpty(pb.Icon))
                                        {
                                            <span>@pb.Text</span>
                                        }
                                        else
                                        {
                                            <i class="@pb.Icon"></i>
                                        }
                                    </DynamicElement>
                                    break;
                                }
                            }
                        }
                    </div>
                </div>
            }
        }
    </RenderTemplate>
</div>
