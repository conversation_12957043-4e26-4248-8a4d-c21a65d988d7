@namespace BootstrapBlazor.Components
@inherits BootstrapComponentBase

<nav @attributes="@AdditionalAttributes" class="@ClassString">
    @foreach (var item in Items)
    {
        @Render(item)
    }
    @ChildContent
</nav>

@code {
    RenderFragment<NavLink> Render => item =>
    @<NavLink @key="item" AdditionalAttributes="@item.AdditionalAttributes" ActiveClass="@item.ActiveClass" Match="@item.Match">
        @item.ChildContent
    </NavLink>;
}
