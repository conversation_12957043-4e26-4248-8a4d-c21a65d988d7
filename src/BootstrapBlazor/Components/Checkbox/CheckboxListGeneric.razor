@namespace BootstrapBlazor.Components
@typeparam TValue
@inherits ValidateBase<List<TValue>>

@if (IsShowLabel)
{
    <BootstrapLabel required="@Required" for="@Id" ShowLabelTooltip="ShowLabelTooltip" Value="@DisplayText" />
}

@if (IsButton)
{
    <div @attributes="@AdditionalAttributes" class="@ButtonClassString">
        <div class="@ButtonGroupClassString" role="group">
            @foreach (var item in Items)
            {
                <DynamicElement TagName="span" TriggerClick="!IsDisabled" OnClick="() => OnClick(item)" class="@GetButtonItemClassString(item)">
                    @item.Text
                </DynamicElement>
            }
        </div>
    </div>
}
else
{
    <div @attributes="@AdditionalAttributes" id="@Id" class="@ClassString" tabindex="0" hidefocus="true">
        @foreach (var item in Items)
        {
            <div @key="item" class="@CheckboxItemClassString">
                <Checkbox TValue="bool" IsDisabled="GetDisabledState(item)"
                          ShowAfterLabel="true" ShowLabel="false" ShowLabelTooltip="ShowLabelTooltip"
                          DisplayText="@item.Text" OnBeforeStateChanged="_onBeforeStateChangedCallback!"
                          Value="@item.Active" OnStateChanged="@((_, v) => OnStateChanged(item, v))"
                          ChildContent="GetChildContent(item)"></Checkbox>
            </div>
        }
    </div>
}
