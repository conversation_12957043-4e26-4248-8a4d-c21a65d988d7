@namespace BootstrapBlazor.Components
@inherits BootstrapComponentBase

@if (Item.IsDisabled)
{
    <div class="nav-link disabled">
        <i class="@IconString"></i>
        <span class="text">@Item.Text</span>
    </div>
}
else
{
    <NavLink @attributes="@AdditionalAttributes" @onclick:preventDefault="@PreventDefault" class="@ClassString" href="@HrefString" target="@TargetString" Match="@ItemMatch" style="@StyleClassString" aria-expanded="@AriaExpandedString">
        <div class="flex-fill">
            <i class="@IconString"></i>
            <span class="menu-text">@Item.Text</span>
        </div>
        @if (Item.Template != null)
        {
            <div class="widget" @onclick:stopPropagation @onclick:preventDefault>
                @Item.Template
            </div>
        }
        <i class="@MenuArrowClassString" />
    </NavLink>
}
