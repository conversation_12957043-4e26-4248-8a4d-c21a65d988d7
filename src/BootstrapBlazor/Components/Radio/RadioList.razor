@namespace BootstrapBlazor.Components
@typeparam TValue
@inherits CheckboxList<TValue>

@if (IsShowLabel)
{
    <BootstrapLabel required="@Required" ShowLabelTooltip="ShowLabelTooltip" Value="@DisplayText" />
}
@if (IsButton)
{
    <div @attributes="@AdditionalAttributes" class="radio-list-group">
        <div class="@ButtonClassString" role="group">
            @foreach (var item in Items)
            {
                <DynamicElement TagName="span" TriggerClick="!IsDisabled" OnClick="() => OnClick(item)" class="@GetButtonItemClassString(item)">
                    @item.Text
                </DynamicElement>
            }
        </div>
    </div>
}
else
{
    <div @attributes="@AdditionalAttributes" class="@ClassString" role="group">
        @foreach (var item in Items)
        {
            <Radio TValue="SelectedItem" Value="@item" Color="@Color" GroupName="@GroupName" IsDisabled="@GetDisabledState(item)" ShowAfterLabel="true" ShowLabel="false" DisplayText="@item.Text" State="@CheckState(item)" OnClick="OnClick" ChildContent="GetChildContent(item)"></Radio>
        }
    </div>
}
