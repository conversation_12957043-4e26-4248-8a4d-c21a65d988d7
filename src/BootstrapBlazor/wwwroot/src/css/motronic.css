:root, [data-bs-theme='light'] {
    --bs-gray: #7E8299;
    --bs-gray-dark: #3F4254;
    --bs-gray-100: #F5F8FA;
    --bs-gray-200: #EFF2F5;
    --bs-gray-300: #E4E6EF;
    --bs-gray-400: #B5B5C3;
    --bs-gray-500: #A1A5B7;
    --bs-gray-600: #7E8299;
    --bs-gray-700: #5E6278;
    --bs-gray-800: #3F4254;
    --bs-gray-900: #181C32;
    --bs-light: #F5F8FA;
    --bs-primary: #009EF7;
    --bs-secondary: #E4E6EF;
    --bs-success: #50CD89;
    --bs-info: #7239EA;
    --bs-warning: #FFC700;
    --bs-danger: #F1416C;
    --bs-dark: #181C32;
    --bs-light-rgb: 245,248,250;
    --bs-primary-rgb: 0,158,247;
    --bs-secondary-rgb: 228,230,239;
    --bs-success-rgb: 80,205,137;
    --bs-info-rgb: 114,57,234;
    --bs-warning-rgb: 255,199,0;
    --bs-danger-rgb: 241,65,108;
    --bs-dark-rgb: 24,28,50;
    --bs-white-rgb: 255,255,255;
    --bs-black-rgb: 0,0,0;
    --bs-body-rgb: 24,28,50;
    --bs-body-color: #181C32;
    --bs-border-radius: .475rem;
    --bb-disabled-bg: #f5f8fa;
    --bs-border-color: var(--bs-secondary);
    --bb-border-focus-color: #b5b5c3;
    --bb-border-hover-color: #b5b5c3;
    --bs-primary-text-emphasis: #005f94;
    --bs-secondary-text-emphasis: #5b5c60;
    --bs-success-text-emphasis: #205237;
    --bs-info-text-emphasis: #44228c;
    --bs-warning-text-emphasis: #665000;
    --bs-danger-text-emphasis: #912741;
    --bs-light-text-emphasis: #495057;
    --bs-dark-text-emphasis: #0e111e;
    --bs-primary-bg-subtle: #ccecfd;
    --bs-secondary-bg-subtle: #fafafc;
    --bs-success-bg-subtle: #dcf5e7;
    --bs-info-bg-subtle: #e3d7fb;
    --bs-warning-bg-subtle: #fff4cc;
    --bs-danger-bg-subtle: #fcd9e2;
    --bs-light-bg-subtle: #fcfcfd;
    --bs-dark-bg-subtle: #d1d2d6;
    --bs-primary-border-subtle: #b3e2fd;
    --bs-secondary-border-subtle: #f7f8fa;
    --bs-success-border-subtle: #cbf0dc;
    --bs-info-border-subtle: #7239ea;
    --bs-warning-border-subtle: #ffeeb3;
    --bs-danger-border-subtle: #fbc6d3;
    --bs-light-border-subtle: #e9ecef;
    --bs-dark-border-subtle: #babbc2;
}

[data-bs-theme='dark'] {
    --bs-primary-text-emphasis: #6ea8fe;
    --bs-secondary-text-emphasis: #a7acb1;
    --bs-success-text-emphasis: #75b798;
    --bs-info-text-emphasis: #6edff6;
    --bs-warning-text-emphasis: #ffda6a;
    --bs-danger-text-emphasis: #ea868f;
    --bs-light-text-emphasis: #f8f9fa;
    --bs-dark-text-emphasis: #dee2e6;
    --bs-primary-bg-subtle: #031633;
    --bs-secondary-bg-subtle: #161719;
    --bs-success-bg-subtle: #051b11;
    --bs-info-bg-subtle: #032830;
    --bs-warning-bg-subtle: #332701;
    --bs-danger-bg-subtle: #2c0b0e;
    --bs-light-bg-subtle: #343a40;
    --bs-dark-bg-subtle: #1a1d20;
    --bs-primary-border-subtle: #084298;
    --bs-secondary-border-subtle: #41464b;
    --bs-success-border-subtle: #0f5132;
    --bs-info-border-subtle: #44228c;
    --bs-warning-border-subtle: #997404;
    --bs-danger-border-subtle: #842029;
    --bs-light-border-subtle: #495057;
    --bs-dark-border-subtle: #343a40;
    --bs-body-color: #dee2e6;
    --bs-body-color-rgb: 222, 226, 230;
    --bs-body-bg: #212529;
    --bs-body-bg-rgb: 33, 37, 41;
    --bs-border-color: #495057;
}

.table {
    --bs-table-striped-bg: rgba(245, 248, 250, 0.75);
    --bs-table-hover-bg: #F5F8FA;
}

[data-bs-theme='dark'] .table {
    --bs-table-striped-bg: rgba(var(--bs-emphasis-color-rgb),.05);
    --bs-table-hover-bg: rgba(var(--bs-emphasis-color-rgb), 0.075);
}

.table-filter .card-header {
    border-bottom: 1px solid var(--bs-secondary);
    background-color: var(--bs-secondary);
}

.table-filter .card-arrow:after {
    border-bottom-color: var(--bs-secondary);
}

.picker-panel-footer {
    border-bottom-left-radius: var(--bs-border-radius);
    border-bottom-right-radius: var(--bs-border-radius);
}

.table-filter .card-arrow:before {
    border-bottom-color: var(--bs-secondary);
}

.btn-primary {
    --bs-btn-hover-bg: #0095e8;
    --bs-btn-bg: var(--bs-primary);
    --bs-btn-border-color: var(--bs-primary);
    --bs-btn-disabled-bg: var(--bs-primary);
    --bs-btn-disabled-border-color: var(--bs-primary);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-secondary {
    --bs-btn-color: #3F4254;
    --bs-btn-hover-bg: #B5B5C3;
    --bs-btn-hover-color: var(--bs-btn-color);
    --bs-btn-active-color: var(--bs-btn-color);
    --bs-btn-disabled-color: var(--bs-btn-color);
    --bs-btn-bg: var(--bs-secondary);
    --bs-btn-border-color: var(--bs-secondary);
    --bs-btn-disabled-bg: var(--bs-secondary);
    --bs-btn-disabled-border-color: var(--bs-secondary);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-success {
    --bs-btn-hover-bg: #47be7d;
    --bs-btn-bg: var(--bs-success);
    --bs-btn-border-color: var(--bs-success);
    --bs-btn-disabled-bg: var(--bs-success);
    --bs-btn-disabled-border-color: var(--bs-success);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-info {
    --bs-btn-color: #fff;
    --bs-btn-hover-bg: #5014d0;
    --bs-btn-hover-color: var(--bs-btn-color);
    --bs-btn-active-color: var(--bs-btn-color);
    --bs-btn-disabled-color: var(--bs-btn-color);
    --bs-btn-bg: var(--bs-info);
    --bs-btn-border-color: var(--bs-info);
    --bs-btn-disabled-bg: var(--bs-info);
    --bs-btn-disabled-border-color: var(--bs-info);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-warning {
    --bs-btn-color: #fff;
    --bs-btn-hover-bg: #f1bc00;
    --bs-btn-hover-color: var(--bs-btn-color);
    --bs-btn-active-color: var(--bs-btn-color);
    --bs-btn-disabled-color: var(--bs-btn-color);
    --bs-btn-bg: var(--bs-warning);
    --bs-btn-border-color: var(--bs-warning);
    --bs-btn-disabled-bg: var(--bs-warning);
    --bs-btn-disabled-border-color: var(--bs-warning);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-danger {
    --bs-btn-hover-bg: #d9214e;
    --bs-btn-bg: var(--bs-danger);
    --bs-btn-border-color: var(--bs-danger);
    --bs-btn-disabled-bg: var(--bs-danger);
    --bs-btn-disabled-border-color: var(--bs-danger);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-dark {
    --bs-btn-hover-bg: #131628;
    --bs-btn-bg: var(--bs-dark);
    --bs-btn-border-color: var(--bs-dark);
    --bs-btn-disabled-bg: var(--bs-dark);
    --bs-btn-disabled-border-color: var(--bs-dark);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-light {
    --bs-btn-color: #212529;
    --bs-btn-hover-color: var(--bs-btn-color);
    --bs-btn-active-color: var(--bs-btn-color);
    --bs-btn-disabled-color: var(--bs-btn-color);
    --bs-btn-bg: var(--bs-light);
    --bs-btn-border-color: var(--bs-light);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-link {
    text-decoration: none;
    --bs-btn-color: var(--bs-primary);
    --bs-btn-hover-color: #0095e8;
}

.btn-outline-primary {
    --bs-btn-hover-bg: #0095e8;
    --bs-btn-color: var(--bs-primary);
    --bs-btn-border-color: var(--bs-primary);
    --bs-btn-disabled-color: var(--bs-btn-color);
    --bs-btn-disabled-border-color: var(--bs-btn-color);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-secondary {
    --bs-btn-hover-bg: #B5B5C3;
    --bs-btn-color: #B5B5C3;
    --bs-btn-border-color: var(--bs-secondary);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-success {
    --bs-btn-hover-bg: #47be7d;
    --bs-btn-color: var(--bs-success);
    --bs-btn-border-color: var(--bs-success);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-info {
    --bs-btn-hover-color: #fff;
    --bs-btn-hover-bg: #5014d0;
    --bs-btn-color: var(--bs-info);
    --bs-btn-border-color: var(--bs-info);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-warning {
    --bs-btn-hover-bg: #f1bc00;
    --bs-btn-color: var(--bs-warning);
    --bs-btn-border-color: var(--bs-warning);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-danger {
    --bs-btn-hover-bg: #d9214e;
    --bs-btn-color: var(--bs-danger);
    --bs-btn-border-color: var(--bs-danger);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-dark {
    --bs-btn-hover-bg: #131628;
    --bs-btn-color: var(--bs-dark);
    --bs-btn-border-color: var(--bs-dark);
    --bs-btn-hover-border-color: var(--bs-btn-hover-bg);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-link {
    --bs-btn-hover-bg: #fff;
    --bs-btn-border-color: #e9ecef;
    --bs-btn-color: var(--bs-primary);
    --bs-btn-hover-color: var(--bs-primary);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-active-bg: var(--bs-btn-hover-bg);
    --bs-btn-active-border-color: var(--bs-btn-hover-bg);
}

.btn-outline-light {
    --bs-light: #dcdfe6;
    --bs-btn-color: var(--bs-light);
    --bs-btn-border-color: var(--bs-light);
    --bs-btn-disabled-color: var(--bs-btn-border-color);
    --bs-btn-disabled-border-color: var(--bs-btn-border-color);
    --bs-btn-hover-border-color: #B5B5C3;
    --bs-btn-active-border-color: #B5B5C3;
}

.modal-backdrop {
    --bs-backdrop-opacity: 0.3;
}

.bb-mask {
    --bb-mask-opacity: 0.3;
}

.modal-content {
    box-shadow: 0 0.25rem 0.5rem #0000001a;
}

.form-control.is-valid, .was-validated .form-control:valid {
    border-color: var(--bs-success);
}

    .was-validated .form-control:valid:focus, .form-control.is-valid:focus, .was-validated .custom-select:valid:focus, .custom-select.is-valid:focus {
        border-color: var(--bs-success);
        box-shadow: none;
    }

.form-control.is-invalid, .was-validated .form-control:invalid {
    border-color: var(--bs-danger);
}

    .was-validated .form-control:invalid:focus, .form-control.is-invalid:focus, .was-validated .custom-select:invalid:focus, .custom-select.is-invalid:focus {
        border-color: var(--bs-danger);
        box-shadow: none;
    }

.table-filter .filter-item {
    border-color: var(--bs-secondary);
}

.dropdown-menu {
    --bs-dropdown-item-padding-y: 6px;
    --bs-dropdown-item-padding-x: 20px;
}

.tree-view {
    --bb-tree-item-hover-color: var(--bs-primary);
}

.btn-xs, .btn-group-xs > .btn {
    --bs-btn-border-radius: .375rem;
}
