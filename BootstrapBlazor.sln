
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31912.275
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BootstrapBlazor", "src\BootstrapBlazor\BootstrapBlazor.csproj", "{D0AE3016-4878-4807-A04C-C33CBEC7B092}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{A2182155-43ED-44C1-BF6F-1B70EBD2DFFE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{7C1D79F1-87BC-42C1-BD5A-CDE4044AC1BD}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "scripts", "scripts", "{A627F6CC-94FC-4E48-B3CC-F0EA16700527}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "linux", "linux", "{EA765165-0542-41C8-93F2-85787FEDEDFF}"
	ProjectSection(SolutionItems) = preProject
		scripts\linux\ba.blazor.service = scripts\linux\ba.blazor.service
		scripts\linux\deploy-blazor.sh = scripts\linux\deploy-blazor.sh
		scripts\linux\deploy.sh = scripts\linux\deploy.sh
		scripts\linux\nginx.conf = scripts\linux\nginx.conf
		scripts\linux\remove.sh = scripts\linux\remove.sh
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "windows", "windows", "{4A5226E2-8EBD-4DEA-A1F5-2DF374655FA9}"
	ProjectSection(SolutionItems) = preProject
		scripts\windows\pack.cmd = scripts\windows\pack.cmd
		scripts\windows\push.cmd = scripts\windows\push.cmd
		scripts\windows\push.ps1 = scripts\windows\push.ps1
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "wasm", "wasm", "{B84D315E-967D-4FBF-9B72-1F3128155CB0}"
	ProjectSection(SolutionItems) = preProject
		scripts\wasm\sync.cmd = scripts\wasm\sync.cmd
		scripts\wasm\sync.sh = scripts\wasm\sync.sh
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UnitTest", "test\UnitTest\UnitTest.csproj", "{190F25CF-C6F9-4964-97E9-F6A912D527AE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BootstrapBlazor.Server", "src\BootstrapBlazor.Server\BootstrapBlazor.Server.csproj", "{1ED371F3-2B28-4B2D-91B8-0C00DA42CB65}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UnitTest.Localization", "test\UnitTest.Localization\UnitTest.Localization.csproj", "{99B55645-0E89-43F8-938F-2304B486AD2A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "localization", "localization", "{1BA43FCA-FA64-449E-A1F1-9C2C4A1D2D3F}"
	ProjectSection(SolutionItems) = preProject
		localization\de.json = localization\de.json
		localization\es.json = localization\es.json
		localization\pt.json = localization\pt.json
		localization\ru-RU.json = localization\ru-RU.json
		localization\th-TH.json = localization\th-TH.json
		localization\zh-TW.json = localization\zh-TW.json
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UnitTestDocs", "test\UnitTestDocs\UnitTestDocs.csproj", "{6D73FED6-0086-460B-84FA-1FA78176BF59}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UniTest.Sass", "test\UniTest.Sass\UniTest.Sass.csproj", "{D8AEAFE7-10AF-4A5B-BC67-FE740A2CA1DF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "configuration", "configuration", "{7037C6AF-5E54-40D4-AB44-69DB177C0C23}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		.gitignore = .gitignore
		exclusion.dic = exclusion.dic
		Framework.props = Framework.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		README.zh-CN.md = README.zh-CN.md
		docs\Dialog使用指南.md = docs\Dialog使用指南.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "cert", "cert", "{C075C6C8-B9CB-4AC0-9BDF-B2002B4AB99C}"
	ProjectSection(SolutionItems) = preProject
		scripts\linux\cert\blazor.zone.cer = scripts\linux\cert\blazor.zone.cer
		scripts\linux\cert\blazor.zone.key = scripts\linux\cert\blazor.zone.key
		scripts\linux\cert\www.blazor.zone.cer = scripts\linux\cert\www.blazor.zone.cer
		scripts\linux\cert\www.blazor.zone.key = scripts\linux\cert\www.blazor.zone.key
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{9BAF50BE-141D-4429-93A9-942F373D1F68}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "UnitTest.Benchmarks", "tools\Benchmarks\UnitTest.Benchmarks.csproj", "{3E6D8D0E-5A36-4CFD-8612-7D64E3FFE7B1}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D0AE3016-4878-4807-A04C-C33CBEC7B092}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D0AE3016-4878-4807-A04C-C33CBEC7B092}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D0AE3016-4878-4807-A04C-C33CBEC7B092}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D0AE3016-4878-4807-A04C-C33CBEC7B092}.Release|Any CPU.Build.0 = Release|Any CPU
		{190F25CF-C6F9-4964-97E9-F6A912D527AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{190F25CF-C6F9-4964-97E9-F6A912D527AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{190F25CF-C6F9-4964-97E9-F6A912D527AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{190F25CF-C6F9-4964-97E9-F6A912D527AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{1ED371F3-2B28-4B2D-91B8-0C00DA42CB65}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1ED371F3-2B28-4B2D-91B8-0C00DA42CB65}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1ED371F3-2B28-4B2D-91B8-0C00DA42CB65}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1ED371F3-2B28-4B2D-91B8-0C00DA42CB65}.Release|Any CPU.Build.0 = Release|Any CPU
		{99B55645-0E89-43F8-938F-2304B486AD2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{99B55645-0E89-43F8-938F-2304B486AD2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{99B55645-0E89-43F8-938F-2304B486AD2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{99B55645-0E89-43F8-938F-2304B486AD2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{6D73FED6-0086-460B-84FA-1FA78176BF59}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6D73FED6-0086-460B-84FA-1FA78176BF59}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6D73FED6-0086-460B-84FA-1FA78176BF59}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6D73FED6-0086-460B-84FA-1FA78176BF59}.Release|Any CPU.Build.0 = Release|Any CPU
		{D8AEAFE7-10AF-4A5B-BC67-FE740A2CA1DF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D8AEAFE7-10AF-4A5B-BC67-FE740A2CA1DF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D8AEAFE7-10AF-4A5B-BC67-FE740A2CA1DF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D8AEAFE7-10AF-4A5B-BC67-FE740A2CA1DF}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E6D8D0E-5A36-4CFD-8612-7D64E3FFE7B1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E6D8D0E-5A36-4CFD-8612-7D64E3FFE7B1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E6D8D0E-5A36-4CFD-8612-7D64E3FFE7B1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E6D8D0E-5A36-4CFD-8612-7D64E3FFE7B1}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{D0AE3016-4878-4807-A04C-C33CBEC7B092} = {A2182155-43ED-44C1-BF6F-1B70EBD2DFFE}
		{EA765165-0542-41C8-93F2-85787FEDEDFF} = {A627F6CC-94FC-4E48-B3CC-F0EA16700527}
		{4A5226E2-8EBD-4DEA-A1F5-2DF374655FA9} = {A627F6CC-94FC-4E48-B3CC-F0EA16700527}
		{B84D315E-967D-4FBF-9B72-1F3128155CB0} = {A627F6CC-94FC-4E48-B3CC-F0EA16700527}
		{190F25CF-C6F9-4964-97E9-F6A912D527AE} = {7C1D79F1-87BC-42C1-BD5A-CDE4044AC1BD}
		{1ED371F3-2B28-4B2D-91B8-0C00DA42CB65} = {A2182155-43ED-44C1-BF6F-1B70EBD2DFFE}
		{99B55645-0E89-43F8-938F-2304B486AD2A} = {7C1D79F1-87BC-42C1-BD5A-CDE4044AC1BD}
		{6D73FED6-0086-460B-84FA-1FA78176BF59} = {7C1D79F1-87BC-42C1-BD5A-CDE4044AC1BD}
		{D8AEAFE7-10AF-4A5B-BC67-FE740A2CA1DF} = {7C1D79F1-87BC-42C1-BD5A-CDE4044AC1BD}
		{C075C6C8-B9CB-4AC0-9BDF-B2002B4AB99C} = {EA765165-0542-41C8-93F2-85787FEDEDFF}
		{3E6D8D0E-5A36-4CFD-8612-7D64E3FFE7B1} = {9BAF50BE-141D-4429-93A9-942F373D1F68}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0DCB0756-34FA-4FD0-AE1D-D3F08B5B3A6B}
	EndGlobalSection
EndGlobal
