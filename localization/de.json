{"BootstrapBlazor.Components.AutoComplete": {"NoDataTip": "<PERSON><PERSON>", "PlaceHolder": "<PERSON><PERSON> e<PERSON>ben"}, "BootstrapBlazor.Components.Captcha": {"HeaderText": "<PERSON><PERSON>", "BarText": "Schieben Sie nach rechts und befüllen Sie das Puzzle", "FailedText": "Laden fehlgeschlagen", "LoadText": "Lade ...", "TryText": "<PERSON><PERSON><PERSON> versuchen"}, "BootstrapBlazor.Components.Camera": {"DeviceLabel": "<PERSON><PERSON><PERSON>", "InitDevicesString": "Initialisiere ...", "PlayText": "Abspielen", "StopText": "Stoppen", "PhotoText": "Photo", "FrontText": "Vor", "BackText": "Zurück", "NotFoundDevicesString": "<PERSON>in <PERSON>ät gefunden"}, "BootstrapBlazor.Components.Calendar": {"PreviousYear": "Vorheriges Jahr", "PreviousMonth": "<PERSON><PERSON><PERSON><PERSON>", "Today": "<PERSON><PERSON>", "NextMonth": "Nächster Monat", "NextYear": "Nächstes Jahr", "PreviousWeek": "<PERSON><PERSON><PERSON><PERSON>", "WeekText": "<PERSON><PERSON><PERSON>", "NextWeek": "Nächste Woche", "WeekHeaderText": "", "WeekLists": "So,<PERSON>,<PERSON>,<PERSON>,Do,Fr,Sa", "WeekNumberText": "{0} <PERSON><PERSON><PERSON>(n)", "Months": "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,März,April,Mai,Juni,Juli,August,September,Oktober,November,Dezember", "Title": "{0} {1}"}, "BootstrapBlazor.Components.Cascader": {"PlaceHolder": "Bitte auswählen ..."}, "BootstrapBlazor.Components.Console": {"HeaderText": "Monitor", "LightTitle": "Light", "ClearButtonText": "<PERSON><PERSON>", "AutoScrollText": "Automatisches Scrollen"}, "BootstrapBlazor.Components.DateTimePicker": {"DatePlaceHolder": "Da<PERSON> ausw<PERSON>en", "TimePlaceHolder": "Zeit auswählen", "DateTimePlaceHolderText": "Bitte auswählen ...", "DatePlaceHolderText": "Bitte auswählen ...", "TimeFormat": "hh\\:mm\\:ss", "DateFormat": "dd\\.MM\\.yyyy", "DateTimeFormat": "dd\\.MM\\.yyyy HH\\:mm\\:ss", "AiraPrevYearLabel": "Vorheriges Jahr", "AiraNextYearLabel": "Nächstes Jahr", "AiraPrevMonthLabel": "<PERSON><PERSON><PERSON><PERSON>", "AiraNextMonthLabel": "Nächster Monat", "ClearButtonText": "<PERSON><PERSON>", "NowButtonText": "Jetzt", "ConfirmButtonText": "Ok", "CancelButtonText": "Abbrechen", "YearText": "{0}", "MonthText": "{0}", "YearPeriodText": "{0} - {1}", "Months": "<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,März,April,Mai,Juni,Juli,August,September,Oktober,November,Dezember", "MonthLists": "Jan,Feb,Mar,Apr,Mai,Jun,Jul,Aug,Sep,Okt,Nov,Dez", "WeekLists": "So,<PERSON>,<PERSON>,<PERSON>,Do,Fr,Sa", "GenericTypeErroMessage": "DateTimePicker unterstützt nur DateTime oder Nullable<DateTime>", "Today": "<PERSON><PERSON>", "Yesterday": "Gestern", "Week": "<PERSON>e Woche zuvor"}, "BootstrapBlazor.Components.DateTimeRange": {"SeparateText": "<PERSON><PERSON>", "StartPlaceHolderText": "Startdatum", "EndPlaceHolderText": "Enddatum", "ClearButtonText": "<PERSON><PERSON>", "TodayButtonText": "<PERSON><PERSON>", "ConfirmButtonText": "Ok", "DateTimeFormat": "dd\\.MM\\.yyyy HH\\:mm\\:ss", "DateFormat": "dd\\.MM\\.yyyy", "Last7Days": "Letzte 7 Tage", "Last30Days": "Letzte 30 Tage", "ThisMonth": "<PERSON><PERSON>", "LastMonth": "Letzter Monat"}, "BootstrapBlazor.Components.BootstrapInputNumber": {"ParsingErrorMessage": "<PERSON> {0} <PERSON><PERSON> muss eine <PERSON> sein."}, "BootstrapBlazor.Components.ResultDialogOption": {"ButtonYesText": "<PERSON>a", "ButtonNoText": "<PERSON><PERSON>", "ButtonCloseText": "Abbrechen"}, "BootstrapBlazor.Components.DropdownList": {"PlaceHolder": "Bitte auswählen ..."}, "BootstrapBlazor.Components.Editor": {"PlaceHolder": "Klicken zum Bearbeiten"}, "BootstrapBlazor.Components.EditorForm": {"ModelInvalidOperationExceptionMessage": "Validationsform Model entspricht nicht {0} Model", "PlaceHolderText": "Bitte auswählen ..."}, "BootstrapBlazor.Components.Empty": {"Text": "<PERSON><PERSON>"}, "BootstrapBlazor.Components.EqualToValidator": {"ErrorMessage": "Bitte geben Sie denselben Wert nochmals ein"}, "BootstrapBlazor.Components.ErrorLogger": {"ToastTitle": "An<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "BootstrapBlazor.Components.GoTop": {"TooltipText": "Nach oben"}, "BootstrapBlazor.Components.Layout": {"TooltipText": "<PERSON><PERSON><PERSON>, um Sidebar auf- bzw. zuzuklappen"}, "BootstrapBlazor.Components.Logout": {"PrefixDisplayNameText": "<PERSON><PERSON><PERSON><PERSON>", "PrefixUserNameText": "Benutzername:"}, "BootstrapBlazor.Components.LogoutLink": {"Text": "Ausloggen"}, "BootstrapBlazor.Components.Menu": {"InvalidOperationExceptionMessage": "Sidemenu-Komponente kann nicht unabhängig verwendet werden. Bitte verwenden Sie die Menu Komponente, um IsVertical = true zu setzen"}, "BootstrapBlazor.Components.ModalDialog": {"CloseButtonText": "Schließen", "SaveButtonText": "Speichern", "PrintButtonText": "<PERSON><PERSON><PERSON>", "ExportPdfButtonText": "PDF exportieren"}, "BootstrapBlazor.Components.MultiSelect": {"PlaceHolder": "<PERSON><PERSON><PERSON>, um auszuwählen ...", "SelectAllText": "Alle", "ReverseSelectText": "Umkehren", "ClearText": "<PERSON><PERSON>", "MinErrorMessage": "Wählen Sie wenigstens {0} Elemente", "MaxErrorMessage": "<PERSON><PERSON> können maximal {0} Elemente selektiert werden", "NoSearchDataText": "<PERSON><PERSON>"}, "BootstrapBlazor.Components.Pagination": {"GotoNavigatorLabelText": "<PERSON><PERSON>"}, "BootstrapBlazor.Components.PopConfirmButton": {"CloseButtonText": "Abbrechen", "ConfirmButtonText": "Ok", "Content": "Wollen Sie diese Aktion wirklich durchführen?"}, "BootstrapBlazor.Components.PrintButton": {"Text": "<PERSON><PERSON><PERSON>"}, "BootstrapBlazor.Components.Repeater": {"EmptyText": "<PERSON><PERSON>"}, "BootstrapBlazor.Components.Search": {"SearchButtonText": "<PERSON><PERSON>", "NoDataTip": "<PERSON><PERSON>äge gefunden"}, "BootstrapBlazor.Components.Select": {"PlaceHolder": "Zum Auswählen klicken ...", "NoSearchDataText": "<PERSON><PERSON>"}, "BootstrapBlazor.Components.SelectTree": {"PlaceHolder": "Zum Auswählen klicken ..."}, "BootstrapBlazor.Components.SelectTreeView": {"PlaceHolder": "Zum Auswählen klicken ..."}, "BootstrapBlazor.Components.StringLengthValidator": {"ErrorMessage": "<PERSON>te geben Sie einen Wert kleiner oder gleich {{0}} ein"}, "BootstrapBlazor.Components.SweetAlert": {"CloseButtonText": "Schließen", "CancelButtonText": "Abbrechen", "ConfirmButtonText": "Bestätigen"}, "BootstrapBlazor.Components.Switch": {"OnInnerText": "An", "OffInnerText": "Aus"}, "BootstrapBlazor.Components.Tab": {"CloseCurrentTabText": "Abbrechen", "CloseOtherTabsText": "Andere schließen", "CloseAllTabsText": "Alle schließen", "NotFoundTabText": "Nicht gefunden", "RefreshToolbarTooltipText": "Aktualisieren", "FullscreenToolbarTooltipText": "Vollbild", "PrevTabNavLinkTooltipText": "<PERSON><PERSON><PERSON><PERSON>", "NextTabNavLinkTooltipText": "Nächster Tab", "CloseTabNavLinkTooltipText": "Schließen", "ContextRefresh": "Aktualisieren", "ContextClose": "Schließen", "ContextCloseOther": "Andere Tabs schließen", "ContextCloseAll": "Alle Tabs schließen", "ContextFullScreen": "Vollbild"}, "BootstrapBlazor.Components.Table": {"AddButtonText": "Hinzufügen", "EditButtonText": "<PERSON><PERSON><PERSON>", "UpdateButtonText": "Ändern", "DeleteButtonText": "Löschen", "CancelButtonText": "Abbrechen", "SaveButtonText": "Speichern", "CloseButtonText": "Schließen", "CancelDeleteButtonText": "Abbrechen", "ConfirmDeleteButtonText": "Löschen", "ConfirmDeleteContentText": "Sind <PERSON> sicher, dass alle ausgwählten Zeilen gelöscht werden sollen?", "RefreshButtonText": "Aktualisieren", "CardViewButtonText": "<PERSON><PERSON><PERSON>", "ColumnButtonTitleText": "Spalten anzeigen/verbergen", "ColumnButtonText": "Spalten", "ExportButtonText": "Export", "SearchPlaceholderText": "<PERSON><PERSON>", "SearchButtonText": "<PERSON><PERSON>", "ResetSearchButtonText": "Z<PERSON>ücksetzen", "AdvanceButtonText": "Erweiterte Suche", "AdvancedSortModalTitle": "<PERSON><PERSON><PERSON><PERSON>", "AdvancedSortButtonText": "Erweitertes Sortieren", "CheckboxDisplayText": "Alle", "EditModalTitle": "<PERSON><PERSON><PERSON>", "AddModalTitle": "<PERSON>eu", "LineNoText": "<PERSON><PERSON><PERSON>", "ColumnButtonTemplateHeaderText": "Aktionen", "SearchTooltip": "<div class='search-input-tooltip'>Bitte eingeben ...</br><kbd>Enter</kbd> Suche <kbd>ESC</kbd> <PERSON><PERSON></div>", "SearchModalTitle": "<PERSON><PERSON>", "AddButtonToastTitle": "<PERSON><PERSON> hi<PERSON><PERSON>", "AddButtonToastContent": "Daten konnten nicht hinzugefügt werden.", "EditButtonToastTitle": "Daten bearbeiten", "EditButtonToastNotSelectContent": "Daten konnten nicht gespeichert werden.", "EditButtonToastReadonlyContent": "Die makierten Daten konnten nicht bearbeitet werden", "EditButtonToastMoreSelectContent": "<PERSON>ur eine Zeile kann bearbeitet werden", "EditButtonToastNoSaveMethodContent": "Daten konnten nicht bearbeitet werden", "SaveButtonToastTitle": "Daten speichern", "SaveButtonToastContent": "Speichern fehlgeschlagen", "SaveButtonToastResultContent": "Daten {0} wur<PERSON> g<PERSON>, Fenster schließt nach {1}s", "SuccessText": "Erfolgreich", "FailText": "Fehlgeschlagen", "DeleteButtonToastTitle": "Daten löschen", "DeleteButtonToastContent": "Bitte markieren Sie die zu löschenden Zeilen, <PERSON><PERSON> schließt nach {0}s", "DeleteButtonToastResultContent": "<PERSON><PERSON><PERSON> {0}, <PERSON><PERSON> schließt nach {1}s", "DeleteButtonToastCanNotDeleteContent": "Die makierten Daten sind nicht l<PERSON>, <PERSON><PERSON> schließt nach {0}s", "DataServiceInvalidOperationText": "Wert für Eigenschaft 'DataService' in 'BootstrapBlazor.Components.Table`1[[{0}]]' kann nicht bereitgestellt werden. Es ist kein Service vom Typ 'BootstrapBlazor.Components.IDataService`1[{0}]' registriert.", "NotSetOnTreeExpandErrorMessage": "OnTreeExpand-Parameter ist nicht gesetzt", "UnsetText": "Aufsteigend", "SortAscText": "Absteigend", "SortDescText": "<PERSON><PERSON>", "EmptyText": "<PERSON><PERSON>", "ExportToastTitle": "Daten exportieren", "ExportToastContent": "Daten exportieren {0}, automatisches Schließen in {1} Sekunden", "ExportToastInProgressContent": "Daten exportieren, automatisches Schließen in {0} Sekunden", "ExportCsvDropdownItemText": "MS-Csv", "ExportExcelDropdownItemText": "MS-Excel", "ExportPdfDropdownItemText": "Pdf", "PageInfoText": "{0} - {1} Insgesamt {2}", "PageItemsText": "{0}/Seite", "CopyColumnTooltipText": "Ganze Spalte in die Zwischenablage kopieren", "CopyColumnCopiedTooltipText": "Kopiert!", "ColumnWidthTooltipPrefix": "Breite: ", "ColumnToolboxTitle": "Werkzeuge", "AlignLeftText": "Links", "AlignLeftTooltipText": "<PERSON><PERSON><PERSON>, um den Text in dieser Spalte links auszurichten", "AlignCenterText": "<PERSON><PERSON><PERSON>", "AlignCenterTooltipText": "<PERSON><PERSON><PERSON>, um den Text in dieser Spalte zentriert auszurichten", "AlignRightText": "<PERSON><PERSON><PERSON>", "AlignRightTooltipText": "<PERSON><PERSON><PERSON>, um den Text in dieser Spalte rechts auszurichten"}, "BootstrapBlazor.Components.EditDialog": {"CloseButtonText": "Schließen", "SaveButtonText": "Speichern"}, "BootstrapBlazor.Components.TableColumnFilter": {"Title": "Filter", "ClearButtonText": "<PERSON><PERSON>", "FilterButtonText": "Filtern", "BoolFilter.AllText": "Alle", "BoolFilter.TrueText": "<PERSON><PERSON><PERSON>", "BoolFilter.FalseText": "<PERSON>wa<PERSON>", "GreaterThanOrEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON> oder gleich", "LessThanOrEqual": "<PERSON>er oder gleich", "GreaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LessThan": "<PERSON><PERSON>", "Equal": "<PERSON><PERSON><PERSON>", "NotEqual": "<PERSON><PERSON><PERSON>", "Contains": "Beinhaltet", "NotContains": "Beinhaltet nicht", "EnumFilter.AllText": "Alle", "NotSupportedMessage": "Nicht unterstützter Filtertyp. Bitte passen Sie den Filter mit FilterTemplate an", "MultiFilterSearchPlaceHolderText": "<PERSON>te e<PERSON>ben ...", "MultiFilterSelectAllText": "Alle auswählen"}, "BootstrapBlazor.Components.FilterLogicItem": {"And": "Und", "Or": "<PERSON><PERSON>"}, "BootstrapBlazor.Components.SearchDialog": {"ResetButtonText": "Z<PERSON>ücksetzen", "QueryButtonText": "Anfrage"}, "BootstrapBlazor.Components.SwitchButton": {"OnText": "An", "OffText": "Aus"}, "BootstrapBlazor.Components.Timer": {"PauseText": "Pause", "ResumeText": "Fortsetzen", "CancelText": "Abbrechen", "StarText": "Star"}, "BootstrapBlazor.Components.Toggle": {"OnText": "Aufklappen", "OffText": "Zuklappen"}, "BootstrapBlazor.Components.Transfer": {"LeftPanelText": "Alle", "RightPanelText": "<PERSON><PERSON><PERSON>", "MinErrorMessage": "Bitte wählen Sie mindestens {0} Elemente aus", "MaxErrorMessage": "Es können bis zu {0} Elemente ausgewählt werden"}, "BootstrapBlazor.Components.TransferPanel": {"SearchPlaceHolderString": "<PERSON>te e<PERSON>ben ...", "Text": "Liste"}, "BootstrapBlazor.Components.Tree": {"NotSetOnTreeExpandErrorMessage": "OnExpandNodeAsync-Parameter nicht gesetzt"}, "BootstrapBlazor.Components.TreeView": {"NotSetOnTreeExpandErrorMessage": "OnExpandNodeAsync-Parameter nicht gesetzt", "ToolbarEditTitle": "Knoten bearbeiten", "ToolbarEditLabelText": "Umbenennen"}, "BootstrapBlazor.Components.UploadBase": {"DeleteButtonText": "Löschen", "BrowserButtonText": "Browser", "FileExtensions": "Datei muss folgende Endung haben: {0}", "FileSizeValidation": "Dateig<PERSON><PERSON><PERSON> muss kleiner sein als {0}", "DropUploadText": "<PERSON><PERSON> hierher ziehen oder <em>klicken, um hochzuladen</em>"}, "BootstrapBlazor.Components.Handwritten": {"SaveButtonText": "Speichern", "ClearButtonText": "<PERSON><PERSON>"}, "BootstrapBlazor.Components.SignaturePad": {"SignAboveLabel": "<PERSON><PERSON> un<PERSON>iben", "ClearBtnTitle": "Löschen", "SignatureAlertText": "Bitte geben Si<PERSON> zu<PERSON>t eine Unterschrift an", "ChangeColorBtnTitle": "Farbe ändern", "UndoBtnTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CloseBtnTitle": "Schließen", "SaveBase64BtnTitle": "OK", "SavePNGBtnTitle": "PNG", "SaveJPGBtnTitle": "JPG", "SaveSVGBtnTitle": "SVG"}, "BootstrapBlazor.Components.NullableBoolItemsAttribute": {"NullValueDisplayText": "Bitte auswählen ...", "TrueValueDisplayText": "<PERSON><PERSON><PERSON>", "FalseValueDisplayText": "<PERSON><PERSON><PERSON>"}, "BootstrapBlazor.Components.InsertRowMode": {"Last": "Letzte", "First": "<PERSON><PERSON><PERSON>"}, "BootstrapBlazor.Components.IconDialog": {"LabelText": "Symbol", "LabelFullText": "Html", "ButtonText": "<PERSON><PERSON><PERSON>", "DialogHeaderText": "Ausgewähltes Symbol", "CopiedTooltipText": "<PERSON><PERSON><PERSON>"}, "BootstrapBlazor.Components.Splitting": {"Text": "Laden ..."}, "BootstrapBlazor.Components.QueryBuilder": {"And": "und", "Or": "oder", "GreaterThanOrEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON> oder gleich", "LessThanOrEqual": "<PERSON>er oder gleich", "GreaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LessThan": "<PERSON><PERSON>", "Equal": "<PERSON><PERSON><PERSON>", "NotEqual": "<PERSON><PERSON><PERSON>", "Contains": "Beinhaltet", "NotContains": "Beinhaltet nicht", "GroupText": "Gruppe", "ItemText": "Element"}, "BootstrapBlazor.Components.TableAdvancedSortDialog": {"AscText": "Aufsteigend", "DescText": "Absteigend"}, "BootstrapBlazor.Components.ClockPicker": {"AMText": "Vormittag", "PMText": "Nachmittag"}, "BootstrapBlazor.Components.ThemeProvider": {"AutoModeText": "Auto", "DarkModeText": "<PERSON><PERSON><PERSON>", "LightModeText": "Hell"}, "BootstrapBlazor.Components.ValidateBase": {"DefaultRequiredErrorMessage": "{0} ist erforderlich."}}