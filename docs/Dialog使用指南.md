# BootstrapBlazor Dialog 组件使用指南

## 概述

Dialog 组件是 BootstrapBlazor 组件库中的弹窗组件，提供了丰富的弹窗功能，包括普通弹窗、模态弹窗、编辑弹窗、搜索弹窗等多种类型。

## 基础配置

### 1. 服务注入

在组件中注入 DialogService：

```csharp
[Inject]
[NotNull]
private DialogService? DialogService { get; set; }
```

或者使用 `@inject` 指令：

```razor
@inject DialogService DialogService
```

### 2. 基本使用

最简单的弹窗调用：

```csharp
await DialogService.Show(new DialogOption()
{
    Title = "弹窗标题",
    BodyTemplate = builder => builder.AddContent(0, "弹窗内容")
});
```

## DialogOption 核心配置

### 基础属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| Title | string | null | 弹窗标题 |
| Size | Size | ExtraExtraLarge | 弹窗大小 |
| FullScreenSize | FullScreenSize | None | 全屏显示的断点 |
| IsCentered | bool | true | 是否垂直居中 |
| IsScrolling | bool | false | 是否允许滚动 |
| ShowFooter | bool | true | 是否显示底部按钮区域 |
| ShowCloseButton | bool | true | 是否显示关闭按钮 |
| ShowSaveButton | bool | false | 是否显示保存按钮 |
| ShowHeaderCloseButton | bool | true | 是否显示标题栏关闭按钮 |

### 交互属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| IsKeyboard | bool | true | 是否支持键盘 ESC 关闭 |
| IsBackdrop | bool | true | 是否点击背景关闭 |
| IsDraggable | bool | false | 是否可拖拽 |
| ShowResize | bool | false | 是否显示调整大小功能 |
| ShowMaximizeButton | bool | false | 是否显示最大化按钮 |

### 内容配置

| 属性 | 类型 | 说明 |
|------|------|------|
| BodyTemplate | RenderFragment | 弹窗主体内容模板 |
| HeaderTemplate | RenderFragment | 自定义标题模板 |
| FooterTemplate | RenderFragment | 自定义底部模板 |
| Component | BootstrapDynamicComponent | 动态组件 |
| BodyContext | object | 传递给弹窗内容的上下文数据 |

## 常用弹窗类型

### 1. 基础弹窗

```csharp
private Task ShowBasicDialog() => DialogService.Show(new DialogOption()
{
    Title = "基础弹窗",
    BodyTemplate = builder => builder.AddContent(0, "这是弹窗内容")
});
```

### 2. 组件弹窗

```csharp
private Task ShowComponentDialog() => DialogService.Show(new DialogOption()
{
    Title = "组件弹窗",
    Component = BootstrapDynamicComponent.CreateComponent<MyComponent>()
});
```

### 3. 带参数的组件弹窗

```csharp
private Task ShowParameterDialog() => DialogService.Show(new DialogOption()
{
    Title = "带参数弹窗",
    Component = BootstrapDynamicComponent.CreateComponent<MyComponent>(new Dictionary<string, object?>
    {
        [nameof(MyComponent.Parameter1)] = "参数值",
        [nameof(MyComponent.Parameter2)] = 123
    })
});
```

### 4. 传递上下文数据

```csharp
private Task ShowContextDialog() => DialogService.Show(new DialogOption()
{
    Title = "上下文弹窗",
    BodyContext = "传递的数据",
    BodyTemplate = builder =>
    {
        builder.OpenComponent<MyComponent>(0);
        builder.CloseComponent();
    }
});
```

在组件中接收上下文数据：

```csharp
[CascadingParameter]
private object? BodyContext { get; set; }
```

### 5. 不显示默认按钮

```csharp
private Task ShowNoFooterDialog() => DialogService.Show(new DialogOption()
{
    Title = "无底部按钮",
    ShowFooter = false,
    Component = BootstrapDynamicComponent.CreateComponent<MyComponent>()
});
```

## 扩展方法使用

### 1. 模态弹窗 (ShowModal)

用于需要返回值的弹窗：

```csharp
private async Task ShowModalDialog()
{
    var result = await DialogService.ShowModal<MyResultDialog>(new ResultDialogOption()
    {
        Title = "模态弹窗",
        ComponentParameters = new Dictionary<string, object>
        {
            [nameof(MyResultDialog.Data)] = someData
        }
    });
    
    // 根据返回值处理后续逻辑
    if (result == DialogResult.Yes)
    {
        // 用户点击了确定
    }
}
```

### 2. 编辑弹窗 (ShowEditDialog)

```csharp
private async Task ShowEditDialog()
{
    var option = new EditDialogOption<MyModel>()
    {
        Title = "编辑数据",
        Model = myModel,
        OnSaveAsync = async (context, changedType) =>
        {
            // 保存逻辑
            return true; // 返回 true 关闭弹窗
        }
    };
    
    await DialogService.ShowEditDialog(option);
}
```

### 3. 搜索弹窗 (ShowSearchDialog)

```csharp
private async Task ShowSearchDialog()
{
    var option = new SearchDialogOption<MyModel>()
    {
        Title = "搜索",
        Model = new MyModel(),
        OnSearchClick = async () =>
        {
            // 搜索逻辑
        },
        OnResetSearchClick = async () =>
        {
            // 重置逻辑
        }
    };
    
    await DialogService.ShowSearchDialog(option);
}
```

### 4. 保存弹窗 (ShowSaveDialog)

```csharp
private async Task ShowSaveDialog()
{
    await DialogService.ShowSaveDialog<MyComponent>(
        title: "保存数据",
        saveCallback: async () =>
        {
            // 保存逻辑
            return true; // 返回 true 关闭弹窗
        },
        parametersFactory: parameters =>
        {
            parameters.Add("Data", myData);
        }
    );
}
```

### 5. 简化的泛型弹窗

```csharp
// 直接显示组件，不显示底部按钮
private Task ShowGenericDialog() => DialogService.Show<MyComponent>("弹窗标题");

// 带参数的泛型弹窗
private Task ShowGenericDialogWithParams() => DialogService.Show<MyComponent>(
    "弹窗标题", 
    new Dictionary<string, object?> { ["Parameter"] = "value" }
);
```

## 弹窗内组件开发

### 1. 关闭弹窗的方法

#### 方法一：使用 DialogCloseButton

```razor
<DialogCloseButton Text="关闭" />
```

#### 方法二：使用级联参数

```csharp
[CascadingParameter]
private Func<Task>? OnCloseAsync { get; set; }

private async Task CloseDialog()
{
    if (OnCloseAsync != null)
    {
        await OnCloseAsync();
    }
}
```

#### 方法三：使用 Modal 实例

```csharp
[CascadingParameter]
[NotNull]
private Modal? Modal { get; set; }

private Task CloseDialog() => Modal.Close();
```

### 2. 模态弹窗返回值设置

对于实现了 `IResultDialog` 接口的组件：

```csharp
[CascadingParameter(Name = "ResultDialogContext")]
private Func<DialogResult, Task>? SetResultAsync { get; set; }

private async Task OnSave()
{
    // 业务逻辑处理
    
    if (SetResultAsync != null)
    {
        await SetResultAsync(DialogResult.Yes);
    }
}
```

### 3. 获取表单实例

在编辑弹窗中获取表单实例进行验证：

```csharp
[CascadingParameter]
private ValidateForm? ValidateForm { get; set; }

private async Task OnValidate()
{
    if (ValidateForm != null)
    {
        var isValid = await ValidateForm.ValidateAsync();
        // 根据验证结果处理
    }
}
```

## 高级功能

### 1. 自定义标题栏

```csharp
private Task ShowCustomHeaderDialog() => DialogService.Show(new DialogOption()
{
    HeaderTemplate = builder =>
    {
        builder.OpenComponent<MyCustomHeader>(0);
        builder.CloseComponent();
    },
    BodyTemplate = builder => builder.AddContent(0, "内容")
});
```

### 2. 标题栏工具栏

```csharp
private Task ShowHeaderToolbarDialog() => DialogService.Show(new DialogOption()
{
    Title = "带工具栏的弹窗",
    HeaderToolbarTemplate = builder =>
    {
        builder.OpenComponent<Button>(0);
        builder.AddAttribute(1, nameof(Button.Icon), "fa-solid fa-print");
        builder.AddAttribute(2, nameof(Button.OnClick), EventCallback.Factory.Create(this, OnPrint));
        builder.CloseComponent();
    }
});
```

### 3. 多层弹窗

组件库支持多层弹窗，后打开的弹窗会覆盖在前面的弹窗之上：

```csharp
private async Task ShowMultipleDialogs()
{
    // 第一层弹窗
    await DialogService.Show(new DialogOption()
    {
        Title = "第一层弹窗",
        Component = BootstrapDynamicComponent.CreateComponent<FirstDialog>()
    });
}

// 在 FirstDialog 中再打开第二层弹窗
private async Task ShowSecondDialog()
{
    await DialogService.Show(new DialogOption()
    {
        Title = "第二层弹窗",
        Component = BootstrapDynamicComponent.CreateComponent<SecondDialog>()
    });
}
```

### 4. 打印功能

```csharp
private Task ShowPrintDialog() => DialogService.Show(new DialogOption()
{
    Title = "打印预览",
    ShowPrintButton = true,
    ShowPrintButtonInHeader = true,
    ShowFooter = false,
    Component = BootstrapDynamicComponent.CreateComponent<PrintContent>()
});
```

### 5. 全屏显示

```csharp
private Task ShowFullScreenDialog() => DialogService.Show(new DialogOption()
{
    Title = "全屏弹窗",
    FullScreenSize = FullScreenSize.ExtraLarge, // 在 XL 断点以下全屏显示
    BodyTemplate = builder => builder.AddContent(0, "全屏内容")
});
```

## 最佳实践

### 1. 业务逻辑处理建议

- **方案一（WinForm 风格）**：在弹窗外部根据返回值处理业务逻辑
- **方案二（推荐）**：将业务逻辑放在弹窗内部组件中处理，提高代码内聚性

### 2. 错误处理

```csharp
private async Task ShowErrorDialog(Exception ex)
{
    await DialogService.ShowExceptionDialog(ex);
}
```

### 3. 键盘和背景交互控制

```csharp
private Task ShowControlledDialog() => DialogService.Show(new DialogOption()
{
    Title = "受控弹窗",
    IsKeyboard = false,    // 禁用 ESC 关闭
    IsBackdrop = false,    // 禁用点击背景关闭
    ShowHeaderCloseButton = false, // 隐藏标题栏关闭按钮
    Component = BootstrapDynamicComponent.CreateComponent<MyComponent>()
});
```

### 4. 响应式设计

```csharp
private Task ShowResponsiveDialog() => DialogService.Show(new DialogOption()
{
    Title = "响应式弹窗",
    Size = Size.Large,
    FullScreenSize = FullScreenSize.Medium, // 在 MD 断点以下全屏
    IsCentered = true,
    Component = BootstrapDynamicComponent.CreateComponent<MyComponent>()
});
```

## 注意事项

1. **服务注入**：确保在 `Program.cs` 中注册了 BootstrapBlazor 服务
2. **根组件**：确保在 `MainLayout.razor` 中包含了 `<BootstrapBlazorRoot>` 组件
3. **内存管理**：弹窗关闭后会自动清理资源，无需手动处理
4. **异步操作**：所有弹窗操作都是异步的，需要使用 `await` 关键字
5. **级联参数**：弹窗内组件可以通过级联参数获取弹窗实例和关闭方法
6. **多层弹窗**：支持多层弹窗，但要注意用户体验和性能影响

## 总结

BootstrapBlazor 的 Dialog 组件提供了完整的弹窗解决方案，从简单的消息提示到复杂的业务表单编辑都能很好地支持。通过合理使用各种配置选项和扩展方法，可以满足大部分业务场景的需求。建议在实际项目中根据具体需求选择合适的弹窗类型和配置方式。
