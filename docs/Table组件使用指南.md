# BootstrapBlazor Table 组件使用指南

## 概述

Table 组件是 BootstrapBlazor 组件库中最强大和功能丰富的数据展示组件之一。它提供了完整的数据表格解决方案，包括数据展示、分页、排序、过滤、搜索、编辑、导出、虚拟滚动、树形结构等功能。

## 基础配置

### 1. 基本引用

```razor
@using BootstrapBlazor.Components
```

### 2. 最简单的表格

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 核心属性配置

### 基础属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| TItem | 泛型 | - | 数据项类型 |
| Items | IEnumerable<TItem> | null | 静态数据源 |
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | null | 异步查询数据回调 |
| IsPagination | bool | false | 是否启用分页 |
| PageItemsSource | int[] | [10, 20, 50, 100] | 分页大小选项 |
| IsStriped | bool | false | 是否显示斑马纹 |
| IsBordered | bool | false | 是否显示边框 |
| IsMultipleSelect | bool | false | 是否支持多选 |

### 工具栏属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ShowToolbar | bool | false | 是否显示工具栏 |
| ShowDefaultButtons | bool | true | 是否显示默认按钮 |
| ShowAddButton | bool | true | 是否显示新增按钮 |
| ShowEditButton | bool | true | 是否显示编辑按钮 |
| ShowDeleteButton | bool | true | 是否显示删除按钮 |
| ShowExtendButtons | bool | false | 是否显示扩展按钮 |
| ShowRefresh | bool | true | 是否显示刷新按钮 |
| ShowColumnList | bool | false | 是否显示列显示/隐藏功能 |

### 搜索功能属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ShowSearch | bool | false | 是否显示搜索功能 |
| ShowSearchText | bool | true | 是否显示搜索文本框 |
| SearchMode | SearchMode | SearchMode.Popup | 搜索模式（弹窗/顶部） |
| SearchModel | object | null | 搜索模型 |
| CustomerSearchModel | object | null | 自定义搜索模型 |
| AutoSearchOnInput | bool | false | 是否输入时自动搜索 |

## 常用功能示例

### 1. 基础数据表格

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private List<Foo> Items = new();

    protected override void OnInitialized()
    {
        Items = GetData(); // 获取数据的方法
    }
}
```

### 2. 分页表格

```razor
<Table TItem="Foo" IsPagination="true" PageItemsSource="@PageItemsSource"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private int[] PageItemsSource = [10, 20, 50];

    private async Task<QueryData<Foo>> OnQueryAsync(QueryPageOptions options)
    {
        // 根据查询条件获取数据
        var items = await GetDataAsync(options);
        return new QueryData<Foo>()
        {
            Items = items,
            TotalCount = await GetTotalCountAsync(options),
            IsSorted = true,
            IsFiltered = true,
            IsSearch = true
        };
    }
}
```

### 3. 带工具栏的可编辑表格

```razor
<Table TItem="Foo" IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
       OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync"
       OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private async Task<bool> OnAddAsync()
    {
        // 新增逻辑
        return true;
    }

    private async Task<bool> OnSaveAsync(Foo item, ItemChangedType changedType)
    {
        // 保存逻辑
        return true;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<Foo> items)
    {
        // 删除逻辑
        return true;
    }
}
```

### 4. 搜索功能

```razor
<Table TItem="Foo" IsPagination="true" ShowToolbar="true" ShowSearch="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" Filterable="true" />
        <TableColumn @bind-Field="@context.Name" Searchable="true" />
        <TableColumn @bind-Field="@context.Address" Searchable="true" />
    </TableColumns>
</Table>
```

### 5. 排序和过滤

```razor
<Table TItem="Foo" IsPagination="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180"
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Name"
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Address"
                     Sortable="true" Filterable="true" />
    </TableColumns>
</Table>
```

## TableColumn 列配置

### 基础列属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| Field | Expression<Func<TItem, object>> | - | 绑定字段表达式 |
| Text | string | null | 列标题文本 |
| Width | int | 0 | 列宽度 |
| Fixed | bool | false | 是否固定列 |
| Visible | bool | true | 是否可见 |
| Readonly | bool | false | 是否只读 |
| Align | Alignment | Alignment.None | 对齐方式 |

### 功能属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| Sortable | bool | false | 是否可排序 |
| Filterable | bool | false | 是否可过滤 |
| Searchable | bool | false | 是否可搜索 |
| ShowCopyColumn | bool | false | 是否显示复制按钮 |
| TextWrap | bool | false | 是否自动换行 |
| TextEllipsis | bool | false | 是否显示省略号 |

### 格式化属性

| 属性 | 类型 | 说明 |
|------|------|------|
| FormatString | string | 格式化字符串 |
| Formatter | Func<object?, Task<string>> | 自定义格式化方法 |
| Template | RenderFragment<TableColumnContext<TItem, TType>> | 自定义模板 |

## 高级功能

### 1. 自定义列模板

```razor
<TableColumn @bind-Field="@context.Name" Width="220">
    <Template Context="value">
        <div class="d-flex">
            <div>
                <img src="@GetAvatarUrl(value.Row.Id)" class="bb-avatar" />
            </div>
            <div class="ps-2">
                <div>@value.Value</div>
                <div class="bb-sub">@value.Row.Address</div>
            </div>
        </div>
    </Template>
</TableColumn>
```

### 2. 详情行模板

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
    <DetailRowTemplate>
        <div>详细信息：@context.Education</div>
    </DetailRowTemplate>
</Table>
```

### 3. 虚拟滚动

```razor
<Table TItem="Foo" class="table-virtualize-demo"
       IsBordered="true" IsStriped="true"
       Items="Items" ScrollMode="ScrollMode.Virtual"
       ShowFooter="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 4. 树形表格

```razor
<Table TItem="TreeFoo" IsBordered="true" IsStriped="true"
       Items="@TreeItems" IsTree="true"
       TreeNodeConverter="@TreeNodeConverter"
       OnTreeExpand="@OnTreeExpand">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Width="360" />
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private static TreeFoo TreeNodeConverter(TreeFoo foo)
    {
        return new TreeFoo(foo) { Text = foo.Name };
    }
}
```

### 5. 固定表头和表尾

```razor
<Table TItem="Foo" ShowFooter="true" IsFixedHeader="true"
       IsFixedFooter="true" Height="400"
       IsPagination="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Count" />
    </TableColumns>
    <FooterTemplate>
        <tr>
            <td colspan="3">
                <div style="text-align: right;">
                    <span>总计：@Items.Sum(x => x.Count)</span>
                </div>
            </td>
        </tr>
    </FooterTemplate>
</Table>
```

## 数据服务集成

### 使用 DataService

```razor
<Table TItem="Foo" DataService="@CustomerDataService"
       IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" ShowExtendButtons="true"
       AutoGenerateColumns="true">
</Table>

@code {
    [Inject]
    [NotNull]
    private IDataService<Foo>? CustomerDataService { get; set; }
}
```

在 `Program.cs` 中注册服务：

```csharp
builder.Services.AddTableDemoDataService();
```

## 导出功能

### 安装导出包

```bash
dotnet add package BootstrapBlazor.TableExport
```

### 配置导出服务

```csharp
// Program.cs
builder.Services.AddBootstrapBlazorTableExportService();
```

### 使用导出功能

```razor
<Table TItem="Foo" IsPagination="true" ShowToolbar="true"
       ShowExportButton="true" ShowExportCsvButton="true"
       ShowExportPdfButton="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 性能优化

### 1. 骨架屏加载

```razor
<Table TItem="Foo" ShowSkeleton="true" OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

### 2. 加载状态

```razor
<Table TItem="Foo" ShowLoading="true" ShowLoadingInFirstRender="false"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

### 3. 虚拟滚动优化

```razor
<Table TItem="Foo" ScrollMode="ScrollMode.Virtual"
       RowHeight="50" OverscanCount="5"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

## 事件处理

### 常用事件

| 事件 | 类型 | 说明 |
|------|------|------|
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | 查询数据事件 |
| OnAddAsync | Func<Task<bool>> | 新增数据事件 |
| OnSaveAsync | Func<TItem, ItemChangedType, Task<bool>> | 保存数据事件 |
| OnDeleteAsync | Func<IEnumerable<TItem>, Task<bool>> | 删除数据事件 |
| OnClickRowCallback | Func<TItem, Task> | 行点击事件 |
| OnDoubleClickRowCallback | Func<TItem, Task> | 行双击事件 |
| OnSelectedRowsChanged | Func<IEnumerable<TItem>, Task> | 选中行变化事件 |

### 事件使用示例

```razor
<Table TItem="Foo" OnClickRowCallback="@OnRowClick"
       OnDoubleClickRowCallback="@OnRowDoubleClick"
       OnSelectedRowsChanged="@OnSelectionChanged">
    <!-- 表格列定义 -->
</Table>

@code {
    private Task OnRowClick(Foo item)
    {
        // 处理行点击
        return Task.CompletedTask;
    }

    private Task OnRowDoubleClick(Foo item)
    {
        // 处理行双击
        return Task.CompletedTask;
    }

    private Task OnSelectionChanged(IEnumerable<Foo> selectedItems)
    {
        // 处理选中项变化
        return Task.CompletedTask;
    }
}
```

## 高级搜索功能

### 1. 自定义搜索模板

```razor
<Table TItem="Foo" ShowSearch="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Searchable="true">
            <SearchTemplate Context="model">
                <div class="col-12 col-sm-6">
                    <Select Items="SearchItems" @bind-Value="@model!.Name"
                            ShowLabel="true" DisplayText="姓名"></Select>
                </div>
            </SearchTemplate>
        </TableColumn>
        <TableColumn @bind-Field="@context.Address" Searchable="true" />
    </TableColumns>
</Table>

@code {
    private List<SelectedItem> SearchItems = new()
    {
        new("张三", "张三"),
        new("李四", "李四"),
        new("王五", "王五")
    };
}
```

### 2. 顶部搜索模式

```razor
<Table TItem="Foo" SearchMode="SearchMode.Top" ShowSearch="true"
       CustomerSearchModel="@CustomerSearchModel" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Searchable="true" />
        <TableColumn @bind-Field="@context.Address" Searchable="true" />
    </TableColumns>
</Table>

@code {
    private Foo CustomerSearchModel = new();
}
```

### 3. 高级过滤功能

```razor
<Table TItem="Foo" ShowFilterHeader="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180"
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Name" Width="100"
                     Sortable="true" Filterable="true">
            <FilterTemplate>
                <StringFilter />
            </FilterTemplate>
        </TableColumn>
        <TableColumn @bind-Field="@context.Count" Width="100"
                     Sortable="true" Filterable="true">
            <FilterTemplate>
                <NumberFilter />
            </FilterTemplate>
        </TableColumn>
    </TableColumns>
</Table>
```

### 4. 多条件过滤

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Filterable="true">
            <FilterTemplate>
                <MultiFilter>
                    <FilterKeyValueAction Text="包含" Value="Contains" />
                    <FilterKeyValueAction Text="等于" Value="Equal" />
                    <FilterKeyValueAction Text="不等于" Value="NotEqual" />
                </MultiFilter>
            </FilterTemplate>
        </TableColumn>
    </TableColumns>
</Table>
```

## 编辑功能详解

### 1. 行内编辑

```razor
<Table TItem="Foo" IsStriped="true" IsBordered="true"
       ShowToolbar="true" ClickToSelect="true"
       DoubleClickToEdit="true" OnQueryAsync="@OnQueryAsync"
       OnSaveAsync="@OnSaveAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 2. 弹窗编辑

```razor
<Table TItem="Foo" ShowToolbar="true" ShowExtendButtons="true"
       EditDialogShowMaximizeButton="true"
       AddModalTitle="新增数据" EditModalTitle="编辑数据"
       OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync"
       OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 3. 自定义编辑模板

```razor
<TableColumn @bind-Field="@context.Hobby">
    <EditTemplate Context="value">
        <Select @bind-Value="@value!.Value" Items="@Hobbies" />
    </EditTemplate>
</TableColumn>

@code {
    private List<SelectedItem> Hobbies = new()
    {
        new("游泳", "游泳"),
        new("数学", "数学"),
        new("音乐", "音乐"),
        new("美术", "美术")
    };
}
```

## 数据验证

### 1. 使用数据注解

```csharp
public class Foo
{
    [Required(ErrorMessage = "姓名不能为空")]
    [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
    public string Name { get; set; } = "";

    [Required(ErrorMessage = "地址不能为空")]
    public string Address { get; set; } = "";

    [Range(0, 200, ErrorMessage = "年龄必须在0-200之间")]
    public int Age { get; set; }

    [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    public string Email { get; set; } = "";
}
```

### 2. 自定义验证规则

```razor
<TableColumn @bind-Field="@context.Name">
    <ValidateTemplate Context="value">
        <RequiredValidator />
        <StringLengthValidator Length="50" />
    </ValidateTemplate>
</TableColumn>
```

## 国际化支持

### 1. 配置本地化

```csharp
// Program.cs
builder.Services.AddBootstrapBlazor(localizationConfigure: options =>
{
    options.ResourcesPath = "Resources";
    options.SupportedUICultures = new[] { "zh-CN", "en-US" };
});
```

### 2. 使用本地化资源

```razor
@inject IStringLocalizer<Foo> LocalizerFoo

<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name"
                     Text="@LocalizerFoo[nameof(Foo.Name)]" />
        <TableColumn @bind-Field="@context.Address"
                     Text="@LocalizerFoo[nameof(Foo.Address)]" />
    </TableColumns>
</Table>
```

## 响应式设计

### 1. 响应式列显示

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address"
                     @bind-Visible="@IsDesktop" />
        <TableColumn @bind-Field="@context.Count"
                     @bind-Visible="@IsTablet" />
    </TableColumns>
</Table>

@code {
    private bool IsDesktop => true; // 根据屏幕尺寸判断
    private bool IsTablet => true;  // 根据屏幕尺寸判断
}
```

### 2. 移动端优化

```razor
<Table TItem="Foo" RenderMode="@GetRenderMode()" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private TableRenderMode GetRenderMode()
    {
        // 根据设备类型返回不同的渲染模式
        return IsMobile ? TableRenderMode.CardView : TableRenderMode.Table;
    }

    private bool IsMobile => false; // 实际项目中需要检测设备类型
}
```

## 数据追踪功能

### 1. 启用数据追踪

```razor
<Table TItem="Foo" IsTracking="true" @bind-Items="Items"
       ShowToastAfterSaveOrDeleteModel="false"
       IsBordered="true" IsMultipleSelect="true"
       ShowToolbar="true" ShowExtendButtons="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private List<Foo> Items = new();

    // 数据变化会自动追踪，无需手动处理
}
```

## 查找服务集成

### 1. 配置查找服务

```csharp
// Program.cs
builder.Services.AddSingleton<ILookupService, DemoLookupService>();
```

### 2. 使用查找服务

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Complete"
                     LookupServiceKey="Foo.Complete" />
    </TableColumns>
</Table>
```

## 工具栏自定义

### 1. 自定义工具栏按钮

```razor
<Table TItem="Foo" ShowToolbar="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <!-- 列定义 -->
    </TableColumns>
    <TableToolbarTemplate>
        <TableToolbarButton TItem="Foo" Color="Color.Primary"
                            Icon="fa-solid fa-plus" Text="自定义新增"
                            OnClick="@OnCustomAdd" />
        <TableToolbarButton TItem="Foo" Color="Color.Success"
                            Icon="fa-solid fa-download" Text="导出Excel"
                            OnClick="@OnExportExcel" />
    </TableToolbarTemplate>
</Table>

@code {
    private Task OnCustomAdd()
    {
        // 自定义新增逻辑
        return Task.CompletedTask;
    }

    private Task OnExportExcel()
    {
        // 导出Excel逻辑
        return Task.CompletedTask;
    }
}
```

### 2. 扩展按钮模板

```razor
<Table TItem="Foo" ShowExtendButtons="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <!-- 列定义 -->
    </TableColumns>
    <ExtendButtonColumnTemplate Context="item">
        <Button Size="Size.ExtraSmall" Icon="fa-solid fa-eye"
                Text="查看" OnClick="() => OnView(item)" />
        <Button Size="Size.ExtraSmall" Icon="fa-solid fa-copy"
                Text="复制" OnClick="() => OnCopy(item)" />
    </ExtendButtonColumnTemplate>
</Table>

@code {
    private Task OnView(Foo item)
    {
        // 查看详情逻辑
        return Task.CompletedTask;
    }

    private Task OnCopy(Foo item)
    {
        // 复制数据逻辑
        return Task.CompletedTask;
    }
}
```

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .table | 基础表格样式 |
| .table-striped | 斑马纹样式 |
| .table-bordered | 边框样式 |
| .table-hover | 悬停效果 |
| .table-sm | 紧凑样式 |

### 自定义样式

```css
.custom-table {
    --bs-table-bg: #f8f9fa;
    --bs-table-hover-bg: #e9ecef;
}

.custom-table .table-cell {
    padding: 0.75rem;
    vertical-align: middle;
}
```

### 表头样式

```razor
<Table TItem="Foo" HeaderStyle="TableHeaderStyle.Dark"
       IsBordered="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 常见问题和解决方案

### 1. 数据不刷新问题

**问题**：修改数据后表格不刷新

**解决方案**：
```csharp
// 方法1：使用 StateHasChanged
private async Task OnDataChanged()
{
    // 修改数据
    await ModifyDataAsync();

    // 通知组件刷新
    StateHasChanged();
}

// 方法2：使用表格的刷新方法
[NotNull]
private Table<Foo>? TableRef { get; set; }

private async Task RefreshTable()
{
    await TableRef.QueryAsync();
}
```

### 2. 分页数据丢失问题

**问题**：切换页面后选中的数据丢失

**解决方案**：
```razor
<Table TItem="Foo" IsKeepSelectedRows="true"
       @bind-SelectedRows="SelectedItems">
    <!-- 表格配置 -->
</Table>

@code {
    private List<Foo> SelectedItems = new();
}
```

### 3. 大数据量性能问题

**解决方案**：
```razor
<!-- 使用虚拟滚动 -->
<Table TItem="Foo" ScrollMode="ScrollMode.Virtual"
       RowHeight="50" OverscanCount="10">
    <!-- 表格配置 -->
</Table>

<!-- 或者使用服务端分页 -->
<Table TItem="Foo" IsPagination="true"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格配置 -->
</Table>
```

### 4. 列宽自适应问题

**解决方案**：
```razor
<Table TItem="Foo" AllowResizing="true"
       ExtendButtonColumnWidth="130">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Width="200" />
        <TableColumn @bind-Field="@context.Address" /> <!-- 自动宽度 -->
    </TableColumns>
</Table>
```

### 5. 文本换行问题

**解决方案**：
```razor
<Table TItem="Foo" HeaderTextWrap="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" TextWrap="true" />
        <TableColumn @bind-Field="@context.Address"
                     TextEllipsis="true" ShowTooltipWhenOverflow="true" />
    </TableColumns>
</Table>
```

## 最佳实践

### 1. 数据模型设计

```csharp
public class Foo : ITableSearchModel
{
    [Key]
    [AutoGenerateColumn(Ignore = true)]
    public int Id { get; set; }

    [Display(Name = "姓名")]
    [Required(ErrorMessage = "姓名不能为空")]
    public string Name { get; set; } = "";

    [Display(Name = "地址")]
    [AutoGenerateColumn(Order = 10, Filterable = true, Searchable = true)]
    public string Address { get; set; } = "";

    [Display(Name = "创建时间")]
    [AutoGenerateColumn(Width = 180, FormatString = "yyyy-MM-dd HH:mm:ss")]
    public DateTime DateTime { get; set; }

    // 实现搜索接口
    public IEnumerable<IFilterAction> GetSearches()
    {
        var ret = new List<IFilterAction>
        {
            new SearchFilterAction(nameof(Name), Name),
            new SearchFilterAction(nameof(Address), Address)
        };
        return ret;
    }

    // 实现重置接口
    public void Reset()
    {
        Name = "";
        Address = "";
    }
}
```

### 2. 服务层设计

```csharp
public class FooService : IDataService<Foo>
{
    public async Task<QueryData<Foo>> QueryAsync(QueryPageOptions options)
    {
        var items = await GetDataAsync(options);
        return new QueryData<Foo>()
        {
            Items = items,
            TotalCount = await GetTotalCountAsync(options),
            IsSorted = true,
            IsFiltered = true,
            IsSearch = true
        };
    }

    public async Task<bool> AddAsync(Foo model)
    {
        // 新增逻辑
        return await SaveToDatabase(model);
    }

    public async Task<bool> SaveAsync(Foo model, ItemChangedType changedType)
    {
        // 保存逻辑
        return await UpdateDatabase(model);
    }

    public async Task<bool> DeleteAsync(IEnumerable<Foo> models)
    {
        // 删除逻辑
        return await DeleteFromDatabase(models);
    }
}
```

### 3. 组件封装

```razor
@typeparam TItem where TItem : class, new()

<Table TItem="TItem" @attributes="AdditionalAttributes"
       IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" ShowExtendButtons="true"
       OnQueryAsync="OnQueryAsync" OnAddAsync="OnAddAsync"
       OnSaveAsync="OnSaveAsync" OnDeleteAsync="OnDeleteAsync">
    <TableColumns>
        @ChildContent
    </TableColumns>
</Table>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public Func<QueryPageOptions, Task<QueryData<TItem>>>? OnQueryAsync { get; set; }
    [Parameter] public Func<Task<bool>>? OnAddAsync { get; set; }
    [Parameter] public Func<TItem, ItemChangedType, Task<bool>>? OnSaveAsync { get; set; }
    [Parameter] public Func<IEnumerable<TItem>, Task<bool>>? OnDeleteAsync { get; set; }
    [Parameter(CaptureUnmatchedValues = true)]
    public Dictionary<string, object>? AdditionalAttributes { get; set; }
}
```

### 4. 性能优化建议

```csharp
// 1. 使用异步方法
private async Task<QueryData<Foo>> OnQueryAsync(QueryPageOptions options)
{
    // 使用异步数据库查询
    var items = await _dbContext.Foos
        .Where(BuildWhereExpression(options))
        .OrderBy(BuildOrderExpression(options))
        .Skip((options.PageIndex - 1) * options.PageItems)
        .Take(options.PageItems)
        .ToListAsync();

    return new QueryData<Foo>
    {
        Items = items,
        TotalCount = await GetTotalCountAsync(options)
    };
}

// 2. 合理使用缓存
private readonly IMemoryCache _cache;

private async Task<List<Foo>> GetCachedDataAsync(string cacheKey)
{
    if (!_cache.TryGetValue(cacheKey, out List<Foo> items))
    {
        items = await LoadDataFromDatabaseAsync();
        _cache.Set(cacheKey, items, TimeSpan.FromMinutes(5));
    }
    return items;
}
```

## 注意事项

1. **泛型类型**：TItem 必须是引用类型（class）
2. **数据绑定**：使用 `@bind-Field` 进行双向绑定
3. **异步操作**：所有数据操作都应该是异步的
4. **内存管理**：大数据量时建议使用虚拟滚动
5. **响应式设计**：考虑移动端的显示效果
6. **性能优化**：合理使用分页和过滤功能
7. **错误处理**：在数据操作中添加适当的错误处理
8. **用户体验**：使用加载状态和骨架屏提升用户体验

## 扩展功能

### 1. 自动生成列

```razor
<Table TItem="Foo" AutoGenerateColumns="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <!-- 只需要自定义特殊列 -->
        <TableColumn @bind-Field="@context.Hobby" Items="@Hobbies" />
    </TableColumns>
</Table>
```

### 2. 列拖拽排序

```razor
<Table TItem="Foo" AllowDragColumn="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 3. 行拖拽排序

```razor
<Table TItem="Foo" AllowDragRow="true"
       OnQueryAsync="@OnQueryAsync" OnDragRowAsync="@OnDragRowAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private async Task<bool> OnDragRowAsync(Foo source, Foo target)
    {
        // 处理行拖拽逻辑
        return true;
    }
}
```

## 总结

BootstrapBlazor 的 Table 组件是一个功能完整、性能优秀的数据表格解决方案。它支持从简单的数据展示到复杂的数据管理场景，通过丰富的配置选项和扩展功能，可以满足绝大多数业务需求。

**主要特性总结：**
- 🎯 **功能丰富**：支持分页、排序、过滤、搜索、编辑、导出等完整功能
- 🚀 **性能优秀**：支持虚拟滚动、骨架屏、懒加载等性能优化
- 🎨 **高度可定制**：支持自定义模板、样式、工具栏等
- 📱 **响应式设计**：完美适配各种屏幕尺寸
- 🌍 **国际化支持**：内置多语言支持
- 🔧 **易于集成**：提供完整的数据服务接口
- 🛡️ **数据验证**：内置数据验证和错误处理
- 🎪 **交互丰富**：支持行内编辑、弹窗编辑、拖拽等交互方式

**使用建议：**
1. 根据数据量选择合适的渲染模式（普通表格 vs 虚拟滚动）
2. 合理使用分页功能，避免一次性加载大量数据
3. 充分利用搜索和过滤功能提升用户体验
4. 在移动端考虑使用卡片视图模式
5. 使用数据服务接口实现业务逻辑的解耦
6. 添加适当的加载状态和错误处理
7. 根据业务需求选择合适的编辑模式

通过合理的架构设计和最佳实践，可以构建出高质量、高性能的数据管理界面，为用户提供优秀的使用体验。

## 高级功能补充

### 1. Excel 模式

Excel 模式提供类似 Excel 的编辑体验，支持键盘导航和单元格编辑：

```razor
<Table TItem="Foo" IsExcel="true" EnableKeyboardNavigationCell="true"
       IsPagination="true" ShowToolbar="true" ShowExtendButtons="true"
       OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync"
       OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" Readonly="true" />
        <TableColumn @bind-Field="@context.Count" Align="Alignment.Right" />
    </TableColumns>
</Table>
```

**Excel 模式特性：**
- 支持键盘方向键导航
- 单元格双击编辑
- Tab 键切换到下一个可编辑单元格
- Enter 键确认编辑并移动到下一行

### 2. 卡片视图模式

卡片视图适合移动端和响应式布局：

```razor
<Table TItem="Foo" RenderMode="TableRenderMode.CardView"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
    </TableColumns>
</Table>

<!-- 自动切换模式 -->
<Table TItem="Foo" RenderMode="TableRenderMode.Auto"
       ShowCardView="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

**渲染模式选项：**
- `TableRenderMode.Table` - 标准表格模式
- `TableRenderMode.CardView` - 卡片视图模式
- `TableRenderMode.Auto` - 自动切换模式

### 3. 多表头功能

支持复杂的多层表头结构：

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync"
       IsBordered="true" IsStriped="true"
       ShowMultiFilterHeader="false">
    <MultiHeaderTemplate>
        <tr>
            <th rowspan="2"><div class="table-cell">时间</div></th>
            <th colspan="2"><div class="table-cell">基本信息</div></th>
            <th rowspan="2"><div class="table-cell">统计</div></th>
        </tr>
        <tr>
            <th><div class="table-cell">姓名</div></th>
            <th><div class="table-cell">地址</div></th>
        </tr>
    </MultiHeaderTemplate>
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
        <TableColumn @bind-Field="@context.Count" />
    </TableColumns>
</Table>
```

### 4. 固定列功能

支持左侧和右侧固定列：

```razor
<Table TItem="Foo" IsBordered="true" IsStriped="true"
       IsFixedHeader="true" FixedExtendButtonsColumn="true"
       FixedMultipleColumn="true" Height="400"
       ScrollWidth="7" ScrollHoverWidth="10"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Width="120" Fixed="true" />
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Address" Width="1400" />
        <TableColumn @bind-Field="@context.Education" Width="100" />
        <TableColumn @bind-Field="@context.Count" Width="100" Fixed="true" />
    </TableColumns>
</Table>
```

**固定列相关属性：**
- `FixedExtendButtonsColumn` - 固定扩展按钮列
- `FixedMultipleColumn` - 固定多选列
- `ScrollWidth` - 滚动条宽度
- `ScrollHoverWidth` - 滚动条悬停宽度

### 5. 列拖拽排序

支持拖拽改变列的顺序：

```razor
<Table TItem="Foo" AllowDragColumn="true"
       ClientTableName="table-drag-demo"
       OnDragColumnEndAsync="@OnDragColumnEndAsync"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="120" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
        <TableColumn @bind-Field="@context.Count" />
    </TableColumns>
</Table>

@code {
    private async Task OnDragColumnEndAsync(string tableName, IEnumerable<ITableColumn> columns)
    {
        // 保存列顺序到数据库或本地存储
        var columnOrder = columns.Select(c => c.GetFieldName()).ToList();
        await SaveColumnOrderAsync(tableName, columnOrder);
    }
}
```

### 6. 自动刷新功能

支持定时自动刷新数据：

```razor
<Table TItem="Foo" IsAutoRefresh="true" AutoRefreshInterval="5000"
       IsPagination="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private bool _isAutoRefresh = true;

    private void ToggleAutoRefresh()
    {
        _isAutoRefresh = !_isAutoRefresh;
    }
}
```

### 7. 列宽调整和自适应

支持手动调整列宽和自动适应内容：

```razor
<Table TItem="Foo" AllowResizing="true"
       OnResizeColumnAsync="@OnResizeColumnAsync"
       OnAutoFitContentAsync="@OnAutoFitContentAsync"
       ColumnMinWidth="80" ShowColumnWidthTooltip="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" Width="300" />
    </TableColumns>
</Table>

@code {
    private async Task OnResizeColumnAsync(string fieldName, float width)
    {
        // 保存列宽设置
        await SaveColumnWidthAsync(fieldName, width);
    }

    private async Task<float> OnAutoFitContentAsync(string fieldName)
    {
        // 计算并返回最适合的列宽
        return await CalculateOptimalWidthAsync(fieldName);
    }
}
```

### 8. 单元格事件处理

支持单元格级别的事件处理：

```razor
<Table TItem="Foo" OnDoubleClickCellCallback="@OnDoubleClickCell"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private async Task OnDoubleClickCell(string fieldName, Foo item, object? value)
    {
        // 处理单元格双击事件
        await ShowCellEditDialog(fieldName, item, value);
    }
}
```

### 9. 行渲染回调

支持在渲染每行前进行自定义处理：

```razor
<Table TItem="Foo" OnBeforeRenderRow="@OnBeforeRenderRow"
       OnAfterRenderCallback="@OnAfterRenderCallback"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private void OnBeforeRenderRow(Foo item)
    {
        // 在渲染行之前进行处理，如设置样式类等
        if (item.IsImportant)
        {
            // 可以设置特殊标记用于CSS样式
        }
    }

    private async Task OnAfterRenderCallback(Table<Foo> table, bool firstRender)
    {
        if (firstRender)
        {
            // 首次渲染完成后的处理
            await InitializeTableAsync();
        }
    }
}
```

### 10. 按钮状态控制

支持动态控制工具栏按钮的启用/禁用状态：

```razor
<Table TItem="Foo" ShowToolbar="true" ShowExtendButtons="true"
       DisableAddButtonCallback="@DisableAddButton"
       DisableEditButtonCallback="@DisableEditButton"
       DisableDeleteButtonCallback="@DisableDeleteButton"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private bool DisableAddButton(List<Foo> selectedItems)
    {
        // 根据业务逻辑决定是否禁用新增按钮
        return !CurrentUser.HasAddPermission;
    }

    private bool DisableEditButton(List<Foo> selectedItems)
    {
        // 没有选中项或选中多项时禁用编辑按钮
        return selectedItems.Count != 1;
    }

    private bool DisableDeleteButton(List<Foo> selectedItems)
    {
        // 没有选中项时禁用删除按钮
        return selectedItems.Count == 0;
    }
}
```

### 11. 滚动控制

支持自动滚动和滚动位置控制：

```razor
<Table TItem="Foo" AutoScrollLastSelectedRowToView="true"
       AutoScrollVerticalAlign="ScrollToViewAlign.Center"
       IsAutoScrollTopWhenClickPage="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 12. 编辑模式配置

支持多种编辑模式：

```razor
<!-- 弹窗编辑模式 -->
<Table TItem="Foo" EditMode="EditMode.Popup"
       EditDialogSize="Size.ExtraLarge"
       EditDialogIsDraggable="true"
       EditDialogShowMaximizeButton="true"
       OnQueryAsync="@OnQueryAsync">
    <!-- 列定义 -->
</Table>

<!-- 抽屉编辑模式 -->
<Table TItem="Foo" EditMode="EditMode.Drawer"
       OnQueryAsync="@OnQueryAsync">
    <!-- 列定义 -->
</Table>

<!-- 行内编辑模式 -->
<Table TItem="Foo" EditMode="EditMode.InCell"
       DoubleClickToEdit="true"
       OnQueryAsync="@OnQueryAsync">
    <!-- 列定义 -->
</Table>
```

## 完整属性参考表

### Table 组件核心属性

| 属性分类 | 属性名 | 类型 | 默认值 | 说明 |
|---------|--------|------|--------|------|
| **基础配置** | TItem | 泛型 | - | 数据项类型 |
| | Items | IEnumerable<TItem> | null | 静态数据源 |
| | OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | null | 异步查询回调 |
| | DataService | IDataService<TItem> | null | 数据服务 |
| **外观样式** | IsStriped | bool | false | 斑马纹样式 |
| | IsBordered | bool | false | 边框样式 |
| | HeaderStyle | TableHeaderStyle | None | 表头样式 |
| | TableSize | TableSize | Normal | 表格大小 |
| | Height | int? | null | 表格高度 |
| **功能开关** | IsPagination | bool | false | 分页功能 |
| | IsMultipleSelect | bool | false | 多选功能 |
| | ShowToolbar | bool | false | 工具栏 |
| | ShowSearch | bool | false | 搜索功能 |
| | ShowFooter | bool | false | 表尾 |
| | IsExcel | bool | false | Excel模式 |
| **高级功能** | IsFixedHeader | bool | false | 固定表头 |
| | IsFixedFooter | bool | false | 固定表尾 |
| | AllowResizing | bool | false | 列宽调整 |
| | AllowDragColumn | bool | false | 列拖拽 |
| | IsAutoRefresh | bool | false | 自动刷新 |
| | AutoRefreshInterval | int | 2000 | 刷新间隔(毫秒) |
| **渲染模式** | RenderMode | TableRenderMode | Auto | 渲染模式 |
| | ScrollMode | ScrollMode | None | 滚动模式 |
| | ShowCardView | bool | false | 显示卡片视图切换 |
| **编辑配置** | EditMode | EditMode | Popup | 编辑模式 |
| | DoubleClickToEdit | bool | false | 双击编辑 |
| | ClickToSelect | bool | false | 点击选中 |
| **性能优化** | ShowSkeleton | bool | false | 骨架屏 |
| | ShowLoading | bool | false | 加载状态 |
| | ShowLoadingInFirstRender | bool | true | 首次渲染加载 |
| | RowHeight | float | 39.5f | 虚拟滚动行高 |
| | OverscanCount | int | 5 | 虚拟滚动预渲染数量 |

### 13. 上下文菜单支持

Table组件支持右键上下文菜单功能：

```razor
<ContextMenuZone>
    <Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
        <TableColumns>
            <TableColumn @bind-Field="@context.DateTime" Width="180" />
            <TableColumn @bind-Field="@context.Name" />
            <TableColumn @bind-Field="@context.Address" />
        </TableColumns>
    </Table>

    <ContextMenu>
        <ContextMenuItem Icon="fa-solid fa-edit" Text="编辑"
                         OnClick="@OnEditContextMenu" />
        <ContextMenuItem Icon="fa-solid fa-copy" Text="复制"
                         OnClick="@OnCopyContextMenu" />
        <ContextMenuDivider />
        <ContextMenuItem Icon="fa-solid fa-trash" Text="删除"
                         OnClick="@OnDeleteContextMenu" />
    </ContextMenu>
</ContextMenuZone>

@code {
    private async Task OnEditContextMenu(ContextMenuItem item, object? context)
    {
        if (context is Foo foo)
        {
            await EditItemAsync(foo);
        }
    }

    private async Task OnCopyContextMenu(ContextMenuItem item, object? context)
    {
        if (context is Foo foo)
        {
            await CopyItemAsync(foo);
        }
    }

    private async Task OnDeleteContextMenu(ContextMenuItem item, object? context)
    {
        if (context is Foo foo)
        {
            await DeleteItemAsync(foo);
        }
    }
}
```

### 14. 触摸设备支持

Table组件对触摸设备提供了良好的支持：

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync"
       UseComponentWidth="true">  <!-- 使用组件宽度进行响应式判断 -->
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

**触摸功能特性：**
- 长按触发上下文菜单
- 触摸滚动支持
- 响应式列显示/隐藏
- 触摸友好的按钮大小

### 15. 键盘导航支持

在Excel模式下支持完整的键盘导航：

```razor
<Table TItem="Foo" IsExcel="true"
       EnableKeyboardNavigationCell="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

**键盘快捷键：**
- `方向键` - 在单元格间导航
- `Tab` - 移动到下一个可编辑单元格
- `Shift + Tab` - 移动到上一个可编辑单元格
- `Enter` - 确认编辑并移动到下一行
- `Escape` - 取消编辑
- `F2` - 进入编辑模式
- `Delete` - 清空单元格内容

### 16. 列复制功能

支持列数据的快速复制：

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" ShowCopyColumn="true" />
        <TableColumn @bind-Field="@context.Address" ShowCopyColumn="true" />
    </TableColumns>
</Table>
```

**复制功能配置：**
- `ShowCopyColumn` - 显示复制按钮
- `ShowCopyColumnTooltip` - 显示复制提示
- `CopyColumnTooltipText` - 复制按钮提示文本
- `CopyColumnCopiedTooltipText` - 复制完成提示文本

### 17. 高级排序功能

支持多列排序和自定义排序：

```razor
<Table TItem="Foo" ShowAdvancedSort="true"
       SortString="@SortList" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180"
                     Sortable="true" DefaultSort="true"
                     DefaultSortOrder="SortOrder.Desc" />
        <TableColumn @bind-Field="@context.Name" Sortable="true" />
        <TableColumn @bind-Field="@context.Address" Sortable="true" />
    </TableColumns>
</Table>

@code {
    private string SortList = "DateTime desc, Name asc";
}
```

### 18. 列工具箱功能

支持列级别的工具箱操作：

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="120"
                     ShowCopyColumn="true" Align="@_dateTimeAlign">
            <ToolboxTemplate Context="column">
                <div class="custom-column-toolbox">
                    <Button Size="Size.ExtraSmall" Icon="fa-solid fa-align-left"
                            OnClick="() => SetAlign(column, Alignment.Left)"></Button>
                    <Button Size="Size.ExtraSmall" Icon="fa-solid fa-align-center"
                            OnClick="() => SetAlign(column, Alignment.Center)"></Button>
                    <Button Size="Size.ExtraSmall" Icon="fa-solid fa-align-right"
                            OnClick="() => SetAlign(column, Alignment.Right)"></Button>
                </div>
            </ToolboxTemplate>
        </TableColumn>
    </TableColumns>
</Table>

@code {
    private Alignment _dateTimeAlign = Alignment.Left;

    private void SetAlign(ITableColumn column, Alignment alignment)
    {
        _dateTimeAlign = alignment;
        StateHasChanged();
    }
}
```

### 19. 表格尺寸控制

支持不同的表格尺寸：

```razor
<!-- 紧凑模式 -->
<Table TItem="Foo" TableSize="TableSize.Compact"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

<!-- 正常模式 -->
<Table TItem="Foo" TableSize="TableSize.Normal"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 20. 客户端表格名称

支持为表格设置客户端名称，用于保存用户设置：

```razor
<Table TItem="Foo" ClientTableName="user-management-table"
       AllowDragColumn="true" AllowResizing="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

**客户端设置保存：**
- 列宽度设置
- 列顺序设置
- 列显示/隐藏状态
- 排序设置

## 事件回调完整列表

### 数据操作事件

| 事件名 | 类型 | 说明 |
|--------|------|------|
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | 查询数据 |
| OnAddAsync | Func<Task<bool>> | 新增数据 |
| OnSaveAsync | Func<TItem, ItemChangedType, Task<bool>> | 保存数据 |
| OnDeleteAsync | Func<IEnumerable<TItem>, Task<bool>> | 删除数据 |

### 交互事件

| 事件名 | 类型 | 说明 |
|--------|------|------|
| OnClickRowCallback | Func<TItem, Task> | 行点击事件 |
| OnDoubleClickRowCallback | Func<TItem, Task> | 行双击事件 |
| OnDoubleClickCellCallback | Func<string, TItem, object?, Task> | 单元格双击事件 |
| OnSelectedRowsChanged | Func<IEnumerable<TItem>, Task> | 选中行变化事件 |

### 渲染事件

| 事件名 | 类型 | 说明 |
|--------|------|------|
| OnBeforeRenderRow | Action<TItem> | 渲染行前回调 |
| OnAfterRenderCallback | Func<Table<TItem>, bool, Task> | 渲染完成回调 |

### 列操作事件

| 事件名 | 类型 | 说明 |
|--------|------|------|
| OnDragColumnEndAsync | Func<string, IEnumerable<ITableColumn>, Task> | 列拖拽结束 |
| OnResizeColumnAsync | Func<string, float, Task> | 列宽调整 |
| OnAutoFitContentAsync | Func<string, Task<float>> | 自动适应列宽 |

### 按钮状态控制

| 事件名 | 类型 | 说明 |
|--------|------|------|
| DisableAddButtonCallback | Func<List<TItem>, bool> | 新增按钮禁用控制 |
| DisableEditButtonCallback | Func<List<TItem>, bool> | 编辑按钮禁用控制 |
| DisableDeleteButtonCallback | Func<List<TItem>, bool> | 删除按钮禁用控制 |

### 高级功能事件

| 事件名 | 类型 | 说明 |
|--------|------|------|
| GetAdvancedSearchFilterCallback | Func<PropertyInfo, TItem, List<SearchFilterAction>?> | 高级搜索条件回调 |
| OnSort | Func<string, SortOrder, string> | 排序回调 |
| OnExportAsync | Func<IEnumerable<TItem>, QueryPageOptions, Task<bool>> | 导出回调 |

## 实用配置技巧

### 1. 表格性能优化配置

```razor
<!-- 大数据量优化配置 -->
<Table TItem="Foo"
       ScrollMode="ScrollMode.Virtual"
       RowHeight="45"
       OverscanCount="10"
       ShowSkeleton="true"
       IsPagination="true"
       PageItemsSource="new int[] {50, 100, 200}"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="120" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 2. 移动端友好配置

```razor
<!-- 移动端优化配置 -->
<Table TItem="Foo"
       RenderMode="TableRenderMode.Auto"
       ShowCardView="true"
       UseComponentWidth="true"
       HeaderTextWrap="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" TextWrap="true" />
        <TableColumn @bind-Field="@context.Address" TextEllipsis="true"
                     ShowTooltipWhenOverflow="true" />
        <TableColumn @bind-Field="@context.DateTime" Width="120"
                     FormatString="MM-dd" />
    </TableColumns>
</Table>
```

### 3. 企业级表格配置

```razor
<!-- 企业级功能完整配置 -->
<Table TItem="Foo"
       IsPagination="true"
       IsStriped="true"
       IsBordered="true"
       IsMultipleSelect="true"
       IsKeepSelectedRows="true"
       ShowToolbar="true"
       ShowExtendButtons="true"
       ShowSearch="true"
       ShowAdvancedSort="true"
       ShowColumnList="true"
       ShowExportButton="true"
       AllowResizing="true"
       AllowDragColumn="true"
       IsFixedHeader="true"
       Height="600"
       ClientTableName="enterprise-table"
       AutoScrollLastSelectedRowToView="true"
       OnQueryAsync="@OnQueryAsync"
       OnAddAsync="@OnAddAsync"
       OnSaveAsync="@OnSaveAsync"
       OnDeleteAsync="@OnDeleteAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180"
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Name" Width="120"
                     Sortable="true" Filterable="true" Searchable="true" />
        <TableColumn @bind-Field="@context.Address"
                     Sortable="true" Filterable="true" Searchable="true" />
        <TableColumn @bind-Field="@context.Count" Width="100"
                     Sortable="true" Filterable="true" Align="Alignment.Right" />
    </TableColumns>
</Table>
```

### 4. Excel风格配置

```razor
<!-- Excel风格表格配置 -->
<Table TItem="Foo"
       IsExcel="true"
       EnableKeyboardNavigationCell="true"
       IsBordered="true"
       IsFixedHeader="true"
       Height="500"
       ShowToolbar="true"
       ShowExtendButtons="true"
       OnQueryAsync="@OnQueryAsync"
       OnSaveAsync="@OnSaveAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="120" />
        <TableColumn @bind-Field="@context.Address" Width="200" />
        <TableColumn @bind-Field="@context.Count" Width="100"
                     Align="Alignment.Right" />
    </TableColumns>
</Table>
```

## 常用模板参考

### 1. 完整的搜索模板

```razor
<Table TItem="Foo" ShowSearch="true" SearchMode="SearchMode.Top"
       CustomerSearchModel="@SearchModel" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Searchable="true">
            <SearchTemplate Context="model">
                <div class="col-12 col-sm-6">
                    <BootstrapInput @bind-Value="@model!.Name"
                                    ShowLabel="true" DisplayText="姓名" />
                </div>
            </SearchTemplate>
        </TableColumn>
        <TableColumn @bind-Field="@context.DateTime" Width="180">
            <SearchTemplate Context="model">
                <div class="col-12 col-sm-6">
                    <DateTimeRange @bind-Value="@model!.DateTimeRange"
                                   ShowLabel="true" DisplayText="日期范围" />
                </div>
            </SearchTemplate>
        </TableColumn>
    </TableColumns>
</Table>

@code {
    private FooSearchModel SearchModel = new();

    public class FooSearchModel : Foo
    {
        public DateTimeRangeValue? DateTimeRange { get; set; }
    }
}
```

### 2. 复杂的编辑模板

```razor
<TableColumn @bind-Field="@context.Education">
    <EditTemplate Context="value">
        <Select @bind-Value="@value!.Value" Items="@EducationItems"
                ShowLabel="true" DisplayText="学历" />
    </EditTemplate>
    <ValidateTemplate Context="value">
        <RequiredValidator />
    </ValidateTemplate>
</TableColumn>

<TableColumn @bind-Field="@context.Hobby">
    <EditTemplate Context="value">
        <CheckboxList @bind-Value="@value!.Value" Items="@HobbyItems"
                      ShowLabel="true" DisplayText="爱好" />
    </EditTemplate>
</TableColumn>

<TableColumn @bind-Field="@context.Avatar">
    <Template Context="value">
        <img src="@value.Value" class="bb-avatar" />
    </Template>
    <EditTemplate Context="value">
        <Upload @bind-Value="@value!.Value" ShowLabel="true"
                DisplayText="头像" Accept="image/*" />
    </EditTemplate>
</TableColumn>
```

### 3. 高级过滤模板

```razor
<TableColumn @bind-Field="@context.Count" Filterable="true">
    <FilterTemplate>
        <NumberFilter Min="0" Max="1000" />
    </FilterTemplate>
</TableColumn>

<TableColumn @bind-Field="@context.DateTime" Filterable="true">
    <FilterTemplate>
        <DateTimeFilter />
    </FilterTemplate>
</TableColumn>

<TableColumn @bind-Field="@context.Education" Filterable="true">
    <FilterTemplate>
        <EnumFilter />
    </FilterTemplate>
</TableColumn>
```

## 完整功能清单

### ✅ 基础功能
- [x] 数据展示
- [x] 分页功能
- [x] 排序功能
- [x] 过滤功能
- [x] 搜索功能
- [x] 多选功能
- [x] 工具栏
- [x] 扩展按钮

### ✅ 编辑功能
- [x] 行内编辑
- [x] 弹窗编辑
- [x] 抽屉编辑
- [x] 双击编辑
- [x] Excel模式编辑
- [x] 批量编辑
- [x] 数据验证

### ✅ 高级功能
- [x] 虚拟滚动
- [x] 固定表头/表尾
- [x] 固定列
- [x] 列拖拽排序
- [x] 列宽调整
- [x] 多表头
- [x] 详情行
- [x] 树形表格
- [x] 卡片视图

### ✅ 交互功能
- [x] 键盘导航
- [x] 上下文菜单
- [x] 触摸支持
- [x] 拖拽操作
- [x] 自动刷新
- [x] 数据追踪

### ✅ 导出功能
- [x] Excel导出
- [x] CSV导出
- [x] PDF导出
- [x] 自定义导出

### ✅ 性能优化
- [x] 骨架屏
- [x] 加载状态
- [x] 懒加载
- [x] 数据缓存
- [x] 响应式设计

### ✅ 国际化
- [x] 多语言支持
- [x] 本地化资源
- [x] 日期格式化
- [x] 数字格式化

## 最终总结

BootstrapBlazor 的 Table 组件是一个功能极其完整、性能卓越的企业级数据表格解决方案。通过本指南的详细介绍，我们可以看到它涵盖了从基础数据展示到复杂业务场景的所有需求。

**🎯 核心优势：**
1. **功能全面** - 涵盖数据管理的所有场景
2. **性能卓越** - 支持大数据量和虚拟滚动
3. **高度可定制** - 丰富的模板和配置选项
4. **用户体验优秀** - 响应式设计和交互友好
5. **开发效率高** - 简洁的API和完善的文档
6. **企业级特性** - 完整的权限控制和数据验证

**🚀 适用场景：**
- 企业管理系统
- 数据分析平台
- 内容管理系统
- 电商后台管理
- 财务管理系统
- 人力资源系统
- 项目管理工具

**💡 使用建议：**
1. 根据数据量选择合适的渲染模式
2. 充分利用搜索和过滤功能提升用户体验
3. 在移动端考虑使用卡片视图
4. 合理使用虚拟滚动优化性能
5. 通过数据服务接口实现业务逻辑解耦
6. 添加适当的加载状态和错误处理

通过合理运用本指南中介绍的各种功能和最佳实践，开发者可以快速构建出功能强大、用户体验优秀的数据管理界面，大大提升开发效率和产品质量。
```
```

| 事件 | 类型 | 说明 |
|------|------|------|
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | 查询数据事件 |
| OnAddAsync | Func<Task<bool>> | 新增数据事件 |
| OnSaveAsync | Func<TItem, ItemChangedType, Task<bool>> | 保存数据事件 |
| OnDeleteAsync | Func<IEnumerable<TItem>, Task<bool>> | 删除数据事件 |
| OnClickRowCallback | Func<TItem, Task> | 行点击事件 |
| OnDoubleClickRowCallback | Func<TItem, Task> | 行双击事件 |
| OnSelectedRowsChanged | Func<IEnumerable<TItem>, Task> | 选中行变化事件 |

### 事件使用示例

```razor
<Table TItem="Foo" OnClickRowCallback="@OnRowClick"
       OnDoubleClickRowCallback="@OnRowDoubleClick"
       OnSelectedRowsChanged="@OnSelectionChanged">
    <!-- 表格列定义 -->
</Table>

@code {
    private Task OnRowClick(Foo item)
    {
        // 处理行点击
        return Task.CompletedTask;
    }

    private Task OnRowDoubleClick(Foo item)
    {
        // 处理行双击
        return Task.CompletedTask;
    }

    private Task OnSelectionChanged(IEnumerable<Foo> selectedItems)
    {
        // 处理选中项变化
        return Task.CompletedTask;
    }
}
```

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .table | 基础表格样式 |
| .table-striped | 斑马纹样式 |
| .table-bordered | 边框样式 |
| .table-hover | 悬停效果 |
| .table-sm | 紧凑样式 |

### 自定义样式

```css
.custom-table {
    --bs-table-bg: #f8f9fa;
    --bs-table-hover-bg: #e9ecef;
}

.custom-table .table-cell {
    padding: 0.75rem;
    vertical-align: middle;
}
```

## 注意事项

1. **泛型类型**：TItem 必须是引用类型（class）
2. **数据绑定**：使用 `@bind-Field` 进行双向绑定
3. **异步操作**：所有数据操作都应该是异步的
4. **内存管理**：大数据量时建议使用虚拟滚动
5. **响应式设计**：考虑移动端的显示效果
6. **性能优化**：合理使用分页和过滤功能

## 总结

BootstrapBlazor 的 Table 组件是一个功能完整、性能优秀的数据表格解决方案。它支持从简单的数据展示到复杂的数据管理场景，通过丰富的配置选项和扩展功能，可以满足绝大多数业务需求。在实际使用中，建议根据具体场景选择合适的功能组合，并注意性能优化和用户体验。
