# BootstrapBlazor Table 组件使用指南

## 概述

Table 组件是 BootstrapBlazor 组件库中最强大和功能丰富的数据展示组件之一。它提供了完整的数据表格解决方案，包括数据展示、分页、排序、过滤、搜索、编辑、导出、虚拟滚动、树形结构等功能。

## 基础配置

### 1. 基本引用

```razor
@using BootstrapBlazor.Components
```

### 2. 最简单的表格

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 核心属性配置

### 基础属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| TItem | 泛型 | - | 数据项类型 |
| Items | IEnumerable<TItem> | null | 静态数据源 |
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | null | 异步查询数据回调 |
| IsPagination | bool | false | 是否启用分页 |
| PageItemsSource | int[] | [10, 20, 50, 100] | 分页大小选项 |
| IsStriped | bool | false | 是否显示斑马纹 |
| IsBordered | bool | false | 是否显示边框 |
| IsMultipleSelect | bool | false | 是否支持多选 |

### 工具栏属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ShowToolbar | bool | false | 是否显示工具栏 |
| ShowDefaultButtons | bool | true | 是否显示默认按钮 |
| ShowAddButton | bool | true | 是否显示新增按钮 |
| ShowEditButton | bool | true | 是否显示编辑按钮 |
| ShowDeleteButton | bool | true | 是否显示删除按钮 |
| ShowExtendButtons | bool | false | 是否显示扩展按钮 |
| ShowRefresh | bool | true | 是否显示刷新按钮 |
| ShowColumnList | bool | false | 是否显示列显示/隐藏功能 |

### 搜索功能属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ShowSearch | bool | false | 是否显示搜索功能 |
| ShowSearchText | bool | true | 是否显示搜索文本框 |
| SearchMode | SearchMode | SearchMode.Popup | 搜索模式（弹窗/顶部） |
| SearchModel | object | null | 搜索模型 |
| CustomerSearchModel | object | null | 自定义搜索模型 |
| AutoSearchOnInput | bool | false | 是否输入时自动搜索 |

## 常用功能示例

### 1. 基础数据表格

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private List<Foo> Items = new();

    protected override void OnInitialized()
    {
        Items = GetData(); // 获取数据的方法
    }
}
```

### 2. 分页表格

```razor
<Table TItem="Foo" IsPagination="true" PageItemsSource="@PageItemsSource"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private int[] PageItemsSource = [10, 20, 50];

    private async Task<QueryData<Foo>> OnQueryAsync(QueryPageOptions options)
    {
        // 根据查询条件获取数据
        var items = await GetDataAsync(options);
        return new QueryData<Foo>()
        {
            Items = items,
            TotalCount = await GetTotalCountAsync(options),
            IsSorted = true,
            IsFiltered = true,
            IsSearch = true
        };
    }
}
```

### 3. 带工具栏的可编辑表格

```razor
<Table TItem="Foo" IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
       OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync"
       OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private async Task<bool> OnAddAsync()
    {
        // 新增逻辑
        return true;
    }

    private async Task<bool> OnSaveAsync(Foo item, ItemChangedType changedType)
    {
        // 保存逻辑
        return true;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<Foo> items)
    {
        // 删除逻辑
        return true;
    }
}
```

### 4. 搜索功能

```razor
<Table TItem="Foo" IsPagination="true" ShowToolbar="true" ShowSearch="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" Filterable="true" />
        <TableColumn @bind-Field="@context.Name" Searchable="true" />
        <TableColumn @bind-Field="@context.Address" Searchable="true" />
    </TableColumns>
</Table>
```

### 5. 排序和过滤

```razor
<Table TItem="Foo" IsPagination="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180"
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Name"
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Address"
                     Sortable="true" Filterable="true" />
    </TableColumns>
</Table>
```

## TableColumn 列配置

### 基础列属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| Field | Expression<Func<TItem, object>> | - | 绑定字段表达式 |
| Text | string | null | 列标题文本 |
| Width | int | 0 | 列宽度 |
| Fixed | bool | false | 是否固定列 |
| Visible | bool | true | 是否可见 |
| Readonly | bool | false | 是否只读 |
| Align | Alignment | Alignment.None | 对齐方式 |

### 功能属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| Sortable | bool | false | 是否可排序 |
| Filterable | bool | false | 是否可过滤 |
| Searchable | bool | false | 是否可搜索 |
| ShowCopyColumn | bool | false | 是否显示复制按钮 |
| TextWrap | bool | false | 是否自动换行 |
| TextEllipsis | bool | false | 是否显示省略号 |

### 格式化属性

| 属性 | 类型 | 说明 |
|------|------|------|
| FormatString | string | 格式化字符串 |
| Formatter | Func<object?, Task<string>> | 自定义格式化方法 |
| Template | RenderFragment<TableColumnContext<TItem, TType>> | 自定义模板 |

## 高级功能

### 1. 自定义列模板

```razor
<TableColumn @bind-Field="@context.Name" Width="220">
    <Template Context="value">
        <div class="d-flex">
            <div>
                <img src="@GetAvatarUrl(value.Row.Id)" class="bb-avatar" />
            </div>
            <div class="ps-2">
                <div>@value.Value</div>
                <div class="bb-sub">@value.Row.Address</div>
            </div>
        </div>
    </Template>
</TableColumn>
```

### 2. 详情行模板

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
    <DetailRowTemplate>
        <div>详细信息：@context.Education</div>
    </DetailRowTemplate>
</Table>
```

### 3. 虚拟滚动

```razor
<Table TItem="Foo" class="table-virtualize-demo"
       IsBordered="true" IsStriped="true"
       Items="Items" ScrollMode="ScrollMode.Virtual"
       ShowFooter="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 4. 树形表格

```razor
<Table TItem="TreeFoo" IsBordered="true" IsStriped="true"
       Items="@TreeItems" IsTree="true"
       TreeNodeConverter="@TreeNodeConverter"
       OnTreeExpand="@OnTreeExpand">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Width="360" />
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private static TreeFoo TreeNodeConverter(TreeFoo foo)
    {
        return new TreeFoo(foo) { Text = foo.Name };
    }
}
```

### 5. 固定表头和表尾

```razor
<Table TItem="Foo" ShowFooter="true" IsFixedHeader="true"
       IsFixedFooter="true" Height="400"
       IsPagination="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Count" />
    </TableColumns>
    <FooterTemplate>
        <tr>
            <td colspan="3">
                <div style="text-align: right;">
                    <span>总计：@Items.Sum(x => x.Count)</span>
                </div>
            </td>
        </tr>
    </FooterTemplate>
</Table>
```

## 数据服务集成

### 使用 DataService

```razor
<Table TItem="Foo" DataService="@CustomerDataService"
       IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" ShowExtendButtons="true"
       AutoGenerateColumns="true">
</Table>

@code {
    [Inject]
    [NotNull]
    private IDataService<Foo>? CustomerDataService { get; set; }
}
```

在 `Program.cs` 中注册服务：

```csharp
builder.Services.AddTableDemoDataService();
```

## 导出功能

### 安装导出包

```bash
dotnet add package BootstrapBlazor.TableExport
```

### 配置导出服务

```csharp
// Program.cs
builder.Services.AddBootstrapBlazorTableExportService();
```

### 使用导出功能

```razor
<Table TItem="Foo" IsPagination="true" ShowToolbar="true"
       ShowExportButton="true" ShowExportCsvButton="true"
       ShowExportPdfButton="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 性能优化

### 1. 骨架屏加载

```razor
<Table TItem="Foo" ShowSkeleton="true" OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

### 2. 加载状态

```razor
<Table TItem="Foo" ShowLoading="true" ShowLoadingInFirstRender="false"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

### 3. 虚拟滚动优化

```razor
<Table TItem="Foo" ScrollMode="ScrollMode.Virtual"
       RowHeight="50" OverscanCount="5"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

## 事件处理

### 常用事件

| 事件 | 类型 | 说明 |
|------|------|------|
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | 查询数据事件 |
| OnAddAsync | Func<Task<bool>> | 新增数据事件 |
| OnSaveAsync | Func<TItem, ItemChangedType, Task<bool>> | 保存数据事件 |
| OnDeleteAsync | Func<IEnumerable<TItem>, Task<bool>> | 删除数据事件 |
| OnClickRowCallback | Func<TItem, Task> | 行点击事件 |
| OnDoubleClickRowCallback | Func<TItem, Task> | 行双击事件 |
| OnSelectedRowsChanged | Func<IEnumerable<TItem>, Task> | 选中行变化事件 |

### 事件使用示例

```razor
<Table TItem="Foo" OnClickRowCallback="@OnRowClick"
       OnDoubleClickRowCallback="@OnRowDoubleClick"
       OnSelectedRowsChanged="@OnSelectionChanged">
    <!-- 表格列定义 -->
</Table>

@code {
    private Task OnRowClick(Foo item)
    {
        // 处理行点击
        return Task.CompletedTask;
    }

    private Task OnRowDoubleClick(Foo item)
    {
        // 处理行双击
        return Task.CompletedTask;
    }

    private Task OnSelectionChanged(IEnumerable<Foo> selectedItems)
    {
        // 处理选中项变化
        return Task.CompletedTask;
    }
}
```

## 高级搜索功能

### 1. 自定义搜索模板

```razor
<Table TItem="Foo" ShowSearch="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Searchable="true">
            <SearchTemplate Context="model">
                <div class="col-12 col-sm-6">
                    <Select Items="SearchItems" @bind-Value="@model!.Name"
                            ShowLabel="true" DisplayText="姓名"></Select>
                </div>
            </SearchTemplate>
        </TableColumn>
        <TableColumn @bind-Field="@context.Address" Searchable="true" />
    </TableColumns>
</Table>

@code {
    private List<SelectedItem> SearchItems = new()
    {
        new("张三", "张三"),
        new("李四", "李四"),
        new("王五", "王五")
    };
}
```

### 2. 顶部搜索模式

```razor
<Table TItem="Foo" SearchMode="SearchMode.Top" ShowSearch="true"
       CustomerSearchModel="@CustomerSearchModel" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Searchable="true" />
        <TableColumn @bind-Field="@context.Address" Searchable="true" />
    </TableColumns>
</Table>

@code {
    private Foo CustomerSearchModel = new();
}
```

### 3. 高级过滤功能

```razor
<Table TItem="Foo" ShowFilterHeader="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180"
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Name" Width="100"
                     Sortable="true" Filterable="true">
            <FilterTemplate>
                <StringFilter />
            </FilterTemplate>
        </TableColumn>
        <TableColumn @bind-Field="@context.Count" Width="100"
                     Sortable="true" Filterable="true">
            <FilterTemplate>
                <NumberFilter />
            </FilterTemplate>
        </TableColumn>
    </TableColumns>
</Table>
```

### 4. 多条件过滤

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Filterable="true">
            <FilterTemplate>
                <MultiFilter>
                    <FilterKeyValueAction Text="包含" Value="Contains" />
                    <FilterKeyValueAction Text="等于" Value="Equal" />
                    <FilterKeyValueAction Text="不等于" Value="NotEqual" />
                </MultiFilter>
            </FilterTemplate>
        </TableColumn>
    </TableColumns>
</Table>
```

## 编辑功能详解

### 1. 行内编辑

```razor
<Table TItem="Foo" IsStriped="true" IsBordered="true"
       ShowToolbar="true" ClickToSelect="true"
       DoubleClickToEdit="true" OnQueryAsync="@OnQueryAsync"
       OnSaveAsync="@OnSaveAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 2. 弹窗编辑

```razor
<Table TItem="Foo" ShowToolbar="true" ShowExtendButtons="true"
       EditDialogShowMaximizeButton="true"
       AddModalTitle="新增数据" EditModalTitle="编辑数据"
       OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync"
       OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 3. 自定义编辑模板

```razor
<TableColumn @bind-Field="@context.Hobby">
    <EditTemplate Context="value">
        <Select @bind-Value="@value!.Value" Items="@Hobbies" />
    </EditTemplate>
</TableColumn>

@code {
    private List<SelectedItem> Hobbies = new()
    {
        new("游泳", "游泳"),
        new("数学", "数学"),
        new("音乐", "音乐"),
        new("美术", "美术")
    };
}
```

## 数据验证

### 1. 使用数据注解

```csharp
public class Foo
{
    [Required(ErrorMessage = "姓名不能为空")]
    [StringLength(50, ErrorMessage = "姓名长度不能超过50个字符")]
    public string Name { get; set; } = "";

    [Required(ErrorMessage = "地址不能为空")]
    public string Address { get; set; } = "";

    [Range(0, 200, ErrorMessage = "年龄必须在0-200之间")]
    public int Age { get; set; }

    [EmailAddress(ErrorMessage = "请输入有效的邮箱地址")]
    public string Email { get; set; } = "";
}
```

### 2. 自定义验证规则

```razor
<TableColumn @bind-Field="@context.Name">
    <ValidateTemplate Context="value">
        <RequiredValidator />
        <StringLengthValidator Length="50" />
    </ValidateTemplate>
</TableColumn>
```

## 国际化支持

### 1. 配置本地化

```csharp
// Program.cs
builder.Services.AddBootstrapBlazor(localizationConfigure: options =>
{
    options.ResourcesPath = "Resources";
    options.SupportedUICultures = new[] { "zh-CN", "en-US" };
});
```

### 2. 使用本地化资源

```razor
@inject IStringLocalizer<Foo> LocalizerFoo

<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name"
                     Text="@LocalizerFoo[nameof(Foo.Name)]" />
        <TableColumn @bind-Field="@context.Address"
                     Text="@LocalizerFoo[nameof(Foo.Address)]" />
    </TableColumns>
</Table>
```

## 响应式设计

### 1. 响应式列显示

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address"
                     @bind-Visible="@IsDesktop" />
        <TableColumn @bind-Field="@context.Count"
                     @bind-Visible="@IsTablet" />
    </TableColumns>
</Table>

@code {
    private bool IsDesktop => true; // 根据屏幕尺寸判断
    private bool IsTablet => true;  // 根据屏幕尺寸判断
}
```

### 2. 移动端优化

```razor
<Table TItem="Foo" RenderMode="@GetRenderMode()" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private TableRenderMode GetRenderMode()
    {
        // 根据设备类型返回不同的渲染模式
        return IsMobile ? TableRenderMode.CardView : TableRenderMode.Table;
    }

    private bool IsMobile => false; // 实际项目中需要检测设备类型
}
```

## 数据追踪功能

### 1. 启用数据追踪

```razor
<Table TItem="Foo" IsTracking="true" @bind-Items="Items"
       ShowToastAfterSaveOrDeleteModel="false"
       IsBordered="true" IsMultipleSelect="true"
       ShowToolbar="true" ShowExtendButtons="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private List<Foo> Items = new();

    // 数据变化会自动追踪，无需手动处理
}
```

## 查找服务集成

### 1. 配置查找服务

```csharp
// Program.cs
builder.Services.AddSingleton<ILookupService, DemoLookupService>();
```

### 2. 使用查找服务

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Complete"
                     LookupServiceKey="Foo.Complete" />
    </TableColumns>
</Table>
```

## 工具栏自定义

### 1. 自定义工具栏按钮

```razor
<Table TItem="Foo" ShowToolbar="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <!-- 列定义 -->
    </TableColumns>
    <TableToolbarTemplate>
        <TableToolbarButton TItem="Foo" Color="Color.Primary"
                            Icon="fa-solid fa-plus" Text="自定义新增"
                            OnClick="@OnCustomAdd" />
        <TableToolbarButton TItem="Foo" Color="Color.Success"
                            Icon="fa-solid fa-download" Text="导出Excel"
                            OnClick="@OnExportExcel" />
    </TableToolbarTemplate>
</Table>

@code {
    private Task OnCustomAdd()
    {
        // 自定义新增逻辑
        return Task.CompletedTask;
    }

    private Task OnExportExcel()
    {
        // 导出Excel逻辑
        return Task.CompletedTask;
    }
}
```

### 2. 扩展按钮模板

```razor
<Table TItem="Foo" ShowExtendButtons="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <!-- 列定义 -->
    </TableColumns>
    <ExtendButtonColumnTemplate Context="item">
        <Button Size="Size.ExtraSmall" Icon="fa-solid fa-eye"
                Text="查看" OnClick="() => OnView(item)" />
        <Button Size="Size.ExtraSmall" Icon="fa-solid fa-copy"
                Text="复制" OnClick="() => OnCopy(item)" />
    </ExtendButtonColumnTemplate>
</Table>

@code {
    private Task OnView(Foo item)
    {
        // 查看详情逻辑
        return Task.CompletedTask;
    }

    private Task OnCopy(Foo item)
    {
        // 复制数据逻辑
        return Task.CompletedTask;
    }
}
```

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .table | 基础表格样式 |
| .table-striped | 斑马纹样式 |
| .table-bordered | 边框样式 |
| .table-hover | 悬停效果 |
| .table-sm | 紧凑样式 |

### 自定义样式

```css
.custom-table {
    --bs-table-bg: #f8f9fa;
    --bs-table-hover-bg: #e9ecef;
}

.custom-table .table-cell {
    padding: 0.75rem;
    vertical-align: middle;
}
```

### 表头样式

```razor
<Table TItem="Foo" HeaderStyle="TableHeaderStyle.Dark"
       IsBordered="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 常见问题和解决方案

### 1. 数据不刷新问题

**问题**：修改数据后表格不刷新

**解决方案**：
```csharp
// 方法1：使用 StateHasChanged
private async Task OnDataChanged()
{
    // 修改数据
    await ModifyDataAsync();

    // 通知组件刷新
    StateHasChanged();
}

// 方法2：使用表格的刷新方法
[NotNull]
private Table<Foo>? TableRef { get; set; }

private async Task RefreshTable()
{
    await TableRef.QueryAsync();
}
```

### 2. 分页数据丢失问题

**问题**：切换页面后选中的数据丢失

**解决方案**：
```razor
<Table TItem="Foo" IsKeepSelectedRows="true"
       @bind-SelectedRows="SelectedItems">
    <!-- 表格配置 -->
</Table>

@code {
    private List<Foo> SelectedItems = new();
}
```

### 3. 大数据量性能问题

**解决方案**：
```razor
<!-- 使用虚拟滚动 -->
<Table TItem="Foo" ScrollMode="ScrollMode.Virtual"
       RowHeight="50" OverscanCount="10">
    <!-- 表格配置 -->
</Table>

<!-- 或者使用服务端分页 -->
<Table TItem="Foo" IsPagination="true"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格配置 -->
</Table>
```

### 4. 列宽自适应问题

**解决方案**：
```razor
<Table TItem="Foo" AllowResizing="true"
       ExtendButtonColumnWidth="130">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Width="200" />
        <TableColumn @bind-Field="@context.Address" /> <!-- 自动宽度 -->
    </TableColumns>
</Table>
```

### 5. 文本换行问题

**解决方案**：
```razor
<Table TItem="Foo" HeaderTextWrap="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" TextWrap="true" />
        <TableColumn @bind-Field="@context.Address"
                     TextEllipsis="true" ShowTooltipWhenOverflow="true" />
    </TableColumns>
</Table>
```

## 最佳实践

### 1. 数据模型设计

```csharp
public class Foo : ITableSearchModel
{
    [Key]
    [AutoGenerateColumn(Ignore = true)]
    public int Id { get; set; }

    [Display(Name = "姓名")]
    [Required(ErrorMessage = "姓名不能为空")]
    public string Name { get; set; } = "";

    [Display(Name = "地址")]
    [AutoGenerateColumn(Order = 10, Filterable = true, Searchable = true)]
    public string Address { get; set; } = "";

    [Display(Name = "创建时间")]
    [AutoGenerateColumn(Width = 180, FormatString = "yyyy-MM-dd HH:mm:ss")]
    public DateTime DateTime { get; set; }

    // 实现搜索接口
    public IEnumerable<IFilterAction> GetSearches()
    {
        var ret = new List<IFilterAction>
        {
            new SearchFilterAction(nameof(Name), Name),
            new SearchFilterAction(nameof(Address), Address)
        };
        return ret;
    }

    // 实现重置接口
    public void Reset()
    {
        Name = "";
        Address = "";
    }
}
```

### 2. 服务层设计

```csharp
public class FooService : IDataService<Foo>
{
    public async Task<QueryData<Foo>> QueryAsync(QueryPageOptions options)
    {
        var items = await GetDataAsync(options);
        return new QueryData<Foo>()
        {
            Items = items,
            TotalCount = await GetTotalCountAsync(options),
            IsSorted = true,
            IsFiltered = true,
            IsSearch = true
        };
    }

    public async Task<bool> AddAsync(Foo model)
    {
        // 新增逻辑
        return await SaveToDatabase(model);
    }

    public async Task<bool> SaveAsync(Foo model, ItemChangedType changedType)
    {
        // 保存逻辑
        return await UpdateDatabase(model);
    }

    public async Task<bool> DeleteAsync(IEnumerable<Foo> models)
    {
        // 删除逻辑
        return await DeleteFromDatabase(models);
    }
}
```

### 3. 组件封装

```razor
@typeparam TItem where TItem : class, new()

<Table TItem="TItem" @attributes="AdditionalAttributes"
       IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" ShowExtendButtons="true"
       OnQueryAsync="OnQueryAsync" OnAddAsync="OnAddAsync"
       OnSaveAsync="OnSaveAsync" OnDeleteAsync="OnDeleteAsync">
    <TableColumns>
        @ChildContent
    </TableColumns>
</Table>

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public Func<QueryPageOptions, Task<QueryData<TItem>>>? OnQueryAsync { get; set; }
    [Parameter] public Func<Task<bool>>? OnAddAsync { get; set; }
    [Parameter] public Func<TItem, ItemChangedType, Task<bool>>? OnSaveAsync { get; set; }
    [Parameter] public Func<IEnumerable<TItem>, Task<bool>>? OnDeleteAsync { get; set; }
    [Parameter(CaptureUnmatchedValues = true)]
    public Dictionary<string, object>? AdditionalAttributes { get; set; }
}
```

### 4. 性能优化建议

```csharp
// 1. 使用异步方法
private async Task<QueryData<Foo>> OnQueryAsync(QueryPageOptions options)
{
    // 使用异步数据库查询
    var items = await _dbContext.Foos
        .Where(BuildWhereExpression(options))
        .OrderBy(BuildOrderExpression(options))
        .Skip((options.PageIndex - 1) * options.PageItems)
        .Take(options.PageItems)
        .ToListAsync();

    return new QueryData<Foo>
    {
        Items = items,
        TotalCount = await GetTotalCountAsync(options)
    };
}

// 2. 合理使用缓存
private readonly IMemoryCache _cache;

private async Task<List<Foo>> GetCachedDataAsync(string cacheKey)
{
    if (!_cache.TryGetValue(cacheKey, out List<Foo> items))
    {
        items = await LoadDataFromDatabaseAsync();
        _cache.Set(cacheKey, items, TimeSpan.FromMinutes(5));
    }
    return items;
}
```

## 注意事项

1. **泛型类型**：TItem 必须是引用类型（class）
2. **数据绑定**：使用 `@bind-Field` 进行双向绑定
3. **异步操作**：所有数据操作都应该是异步的
4. **内存管理**：大数据量时建议使用虚拟滚动
5. **响应式设计**：考虑移动端的显示效果
6. **性能优化**：合理使用分页和过滤功能
7. **错误处理**：在数据操作中添加适当的错误处理
8. **用户体验**：使用加载状态和骨架屏提升用户体验

## 扩展功能

### 1. 自动生成列

```razor
<Table TItem="Foo" AutoGenerateColumns="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <!-- 只需要自定义特殊列 -->
        <TableColumn @bind-Field="@context.Hobby" Items="@Hobbies" />
    </TableColumns>
</Table>
```

### 2. 列拖拽排序

```razor
<Table TItem="Foo" AllowDragColumn="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 3. 行拖拽排序

```razor
<Table TItem="Foo" AllowDragRow="true"
       OnQueryAsync="@OnQueryAsync" OnDragRowAsync="@OnDragRowAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private async Task<bool> OnDragRowAsync(Foo source, Foo target)
    {
        // 处理行拖拽逻辑
        return true;
    }
}
```

## 总结

BootstrapBlazor 的 Table 组件是一个功能完整、性能优秀的数据表格解决方案。它支持从简单的数据展示到复杂的数据管理场景，通过丰富的配置选项和扩展功能，可以满足绝大多数业务需求。

**主要特性总结：**
- 🎯 **功能丰富**：支持分页、排序、过滤、搜索、编辑、导出等完整功能
- 🚀 **性能优秀**：支持虚拟滚动、骨架屏、懒加载等性能优化
- 🎨 **高度可定制**：支持自定义模板、样式、工具栏等
- 📱 **响应式设计**：完美适配各种屏幕尺寸
- 🌍 **国际化支持**：内置多语言支持
- 🔧 **易于集成**：提供完整的数据服务接口
- 🛡️ **数据验证**：内置数据验证和错误处理
- 🎪 **交互丰富**：支持行内编辑、弹窗编辑、拖拽等交互方式

**使用建议：**
1. 根据数据量选择合适的渲染模式（普通表格 vs 虚拟滚动）
2. 合理使用分页功能，避免一次性加载大量数据
3. 充分利用搜索和过滤功能提升用户体验
4. 在移动端考虑使用卡片视图模式
5. 使用数据服务接口实现业务逻辑的解耦
6. 添加适当的加载状态和错误处理
7. 根据业务需求选择合适的编辑模式

通过合理的架构设计和最佳实践，可以构建出高质量、高性能的数据管理界面，为用户提供优秀的使用体验。
```
```

| 事件 | 类型 | 说明 |
|------|------|------|
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | 查询数据事件 |
| OnAddAsync | Func<Task<bool>> | 新增数据事件 |
| OnSaveAsync | Func<TItem, ItemChangedType, Task<bool>> | 保存数据事件 |
| OnDeleteAsync | Func<IEnumerable<TItem>, Task<bool>> | 删除数据事件 |
| OnClickRowCallback | Func<TItem, Task> | 行点击事件 |
| OnDoubleClickRowCallback | Func<TItem, Task> | 行双击事件 |
| OnSelectedRowsChanged | Func<IEnumerable<TItem>, Task> | 选中行变化事件 |

### 事件使用示例

```razor
<Table TItem="Foo" OnClickRowCallback="@OnRowClick"
       OnDoubleClickRowCallback="@OnRowDoubleClick"
       OnSelectedRowsChanged="@OnSelectionChanged">
    <!-- 表格列定义 -->
</Table>

@code {
    private Task OnRowClick(Foo item)
    {
        // 处理行点击
        return Task.CompletedTask;
    }

    private Task OnRowDoubleClick(Foo item)
    {
        // 处理行双击
        return Task.CompletedTask;
    }

    private Task OnSelectionChanged(IEnumerable<Foo> selectedItems)
    {
        // 处理选中项变化
        return Task.CompletedTask;
    }
}
```

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .table | 基础表格样式 |
| .table-striped | 斑马纹样式 |
| .table-bordered | 边框样式 |
| .table-hover | 悬停效果 |
| .table-sm | 紧凑样式 |

### 自定义样式

```css
.custom-table {
    --bs-table-bg: #f8f9fa;
    --bs-table-hover-bg: #e9ecef;
}

.custom-table .table-cell {
    padding: 0.75rem;
    vertical-align: middle;
}
```

## 注意事项

1. **泛型类型**：TItem 必须是引用类型（class）
2. **数据绑定**：使用 `@bind-Field` 进行双向绑定
3. **异步操作**：所有数据操作都应该是异步的
4. **内存管理**：大数据量时建议使用虚拟滚动
5. **响应式设计**：考虑移动端的显示效果
6. **性能优化**：合理使用分页和过滤功能

## 总结

BootstrapBlazor 的 Table 组件是一个功能完整、性能优秀的数据表格解决方案。它支持从简单的数据展示到复杂的数据管理场景，通过丰富的配置选项和扩展功能，可以满足绝大多数业务需求。在实际使用中，建议根据具体场景选择合适的功能组合，并注意性能优化和用户体验。
