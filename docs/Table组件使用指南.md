# BootstrapBlazor Table 组件使用指南

## 概述

Table 组件是 BootstrapBlazor 组件库中最强大和功能丰富的数据展示组件之一。它提供了完整的数据表格解决方案，包括数据展示、分页、排序、过滤、搜索、编辑、导出、虚拟滚动、树形结构等功能。

## 基础配置

### 1. 基本引用

```razor
@using BootstrapBlazor.Components
```

### 2. 最简单的表格

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 核心属性配置

### 基础属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| TItem | 泛型 | - | 数据项类型 |
| Items | IEnumerable<TItem> | null | 静态数据源 |
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | null | 异步查询数据回调 |
| IsPagination | bool | false | 是否启用分页 |
| PageItemsSource | int[] | [10, 20, 50, 100] | 分页大小选项 |
| IsStriped | bool | false | 是否显示斑马纹 |
| IsBordered | bool | false | 是否显示边框 |
| IsMultipleSelect | bool | false | 是否支持多选 |

### 工具栏属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ShowToolbar | bool | false | 是否显示工具栏 |
| ShowDefaultButtons | bool | true | 是否显示默认按钮 |
| ShowAddButton | bool | true | 是否显示新增按钮 |
| ShowEditButton | bool | true | 是否显示编辑按钮 |
| ShowDeleteButton | bool | true | 是否显示删除按钮 |
| ShowExtendButtons | bool | false | 是否显示扩展按钮 |
| ShowRefresh | bool | true | 是否显示刷新按钮 |
| ShowColumnList | bool | false | 是否显示列显示/隐藏功能 |

### 搜索功能属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| ShowSearch | bool | false | 是否显示搜索功能 |
| ShowSearchText | bool | true | 是否显示搜索文本框 |
| SearchMode | SearchMode | SearchMode.Popup | 搜索模式（弹窗/顶部） |
| SearchModel | object | null | 搜索模型 |
| CustomerSearchModel | object | null | 自定义搜索模型 |
| AutoSearchOnInput | bool | false | 是否输入时自动搜索 |

## 常用功能示例

### 1. 基础数据表格

```razor
<Table TItem="Foo" Items="@Items">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private List<Foo> Items = new();
    
    protected override void OnInitialized()
    {
        Items = GetData(); // 获取数据的方法
    }
}
```

### 2. 分页表格

```razor
<Table TItem="Foo" IsPagination="true" PageItemsSource="@PageItemsSource" 
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private int[] PageItemsSource = [10, 20, 50];
    
    private async Task<QueryData<Foo>> OnQueryAsync(QueryPageOptions options)
    {
        // 根据查询条件获取数据
        var items = await GetDataAsync(options);
        return new QueryData<Foo>()
        {
            Items = items,
            TotalCount = await GetTotalCountAsync(options),
            IsSorted = true,
            IsFiltered = true,
            IsSearch = true
        };
    }
}
```

### 3. 带工具栏的可编辑表格

```razor
<Table TItem="Foo" IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" IsMultipleSelect="true" ShowExtendButtons="true"
       OnQueryAsync="@OnQueryAsync" OnAddAsync="@OnAddAsync" 
       OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private async Task<bool> OnAddAsync()
    {
        // 新增逻辑
        return true;
    }
    
    private async Task<bool> OnSaveAsync(Foo item, ItemChangedType changedType)
    {
        // 保存逻辑
        return true;
    }
    
    private async Task<bool> OnDeleteAsync(IEnumerable<Foo> items)
    {
        // 删除逻辑
        return true;
    }
}
```

### 4. 搜索功能

```razor
<Table TItem="Foo" IsPagination="true" ShowToolbar="true" ShowSearch="true"
       OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" Filterable="true" />
        <TableColumn @bind-Field="@context.Name" Searchable="true" />
        <TableColumn @bind-Field="@context.Address" Searchable="true" />
    </TableColumns>
</Table>
```

### 5. 排序和过滤

```razor
<Table TItem="Foo" IsPagination="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" 
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Name" 
                     Sortable="true" Filterable="true" />
        <TableColumn @bind-Field="@context.Address" 
                     Sortable="true" Filterable="true" />
    </TableColumns>
</Table>
```

## TableColumn 列配置

### 基础列属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| Field | Expression<Func<TItem, object>> | - | 绑定字段表达式 |
| Text | string | null | 列标题文本 |
| Width | int | 0 | 列宽度 |
| Fixed | bool | false | 是否固定列 |
| Visible | bool | true | 是否可见 |
| Readonly | bool | false | 是否只读 |
| Align | Alignment | Alignment.None | 对齐方式 |

### 功能属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| Sortable | bool | false | 是否可排序 |
| Filterable | bool | false | 是否可过滤 |
| Searchable | bool | false | 是否可搜索 |
| ShowCopyColumn | bool | false | 是否显示复制按钮 |
| TextWrap | bool | false | 是否自动换行 |
| TextEllipsis | bool | false | 是否显示省略号 |

### 格式化属性

| 属性 | 类型 | 说明 |
|------|------|------|
| FormatString | string | 格式化字符串 |
| Formatter | Func<object?, Task<string>> | 自定义格式化方法 |
| Template | RenderFragment<TableColumnContext<TItem, TType>> | 自定义模板 |

## 高级功能

### 1. 自定义列模板

```razor
<TableColumn @bind-Field="@context.Name" Width="220">
    <Template Context="value">
        <div class="d-flex">
            <div>
                <img src="@GetAvatarUrl(value.Row.Id)" class="bb-avatar" />
            </div>
            <div class="ps-2">
                <div>@value.Value</div>
                <div class="bb-sub">@value.Row.Address</div>
            </div>
        </div>
    </Template>
</TableColumn>
```

### 2. 详情行模板

```razor
<Table TItem="Foo" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
    <DetailRowTemplate>
        <div>详细信息：@context.Education</div>
    </DetailRowTemplate>
</Table>
```

### 3. 虚拟滚动

```razor
<Table TItem="Foo" class="table-virtualize-demo"
       IsBordered="true" IsStriped="true"
       Items="Items" ScrollMode="ScrollMode.Virtual"
       ShowFooter="true">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

### 4. 树形表格

```razor
<Table TItem="TreeFoo" IsBordered="true" IsStriped="true"
       Items="@TreeItems" IsTree="true" 
       TreeNodeConverter="@TreeNodeConverter" 
       OnTreeExpand="@OnTreeExpand">
    <TableColumns>
        <TableColumn @bind-Field="@context.Name" Width="360" />
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>

@code {
    private static TreeFoo TreeNodeConverter(TreeFoo foo)
    {
        return new TreeFoo(foo) { Text = foo.Name };
    }
}
```

### 5. 固定表头和表尾

```razor
<Table TItem="Foo" ShowFooter="true" IsFixedHeader="true" 
       IsFixedFooter="true" Height="400"
       IsPagination="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" />
        <TableColumn @bind-Field="@context.Count" />
    </TableColumns>
    <FooterTemplate>
        <tr>
            <td colspan="3">
                <div style="text-align: right;">
                    <span>总计：@Items.Sum(x => x.Count)</span>
                </div>
            </td>
        </tr>
    </FooterTemplate>
</Table>
```

## 数据服务集成

### 使用 DataService

```razor
<Table TItem="Foo" DataService="@CustomerDataService"
       IsPagination="true" IsStriped="true" IsBordered="true"
       ShowToolbar="true" ShowExtendButtons="true" 
       AutoGenerateColumns="true">
</Table>

@code {
    [Inject]
    [NotNull]
    private IDataService<Foo>? CustomerDataService { get; set; }
}
```

在 `Program.cs` 中注册服务：

```csharp
builder.Services.AddTableDemoDataService();
```

## 导出功能

### 安装导出包

```bash
dotnet add package BootstrapBlazor.TableExport
```

### 配置导出服务

```csharp
// Program.cs
builder.Services.AddBootstrapBlazorTableExportService();
```

### 使用导出功能

```razor
<Table TItem="Foo" IsPagination="true" ShowToolbar="true"
       ShowExportButton="true" ShowExportCsvButton="true" 
       ShowExportPdfButton="true" OnQueryAsync="@OnQueryAsync">
    <TableColumns>
        <TableColumn @bind-Field="@context.DateTime" Width="180" />
        <TableColumn @bind-Field="@context.Name" Width="100" />
        <TableColumn @bind-Field="@context.Address" />
    </TableColumns>
</Table>
```

## 性能优化

### 1. 骨架屏加载

```razor
<Table TItem="Foo" ShowSkeleton="true" OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

### 2. 加载状态

```razor
<Table TItem="Foo" ShowLoading="true" ShowLoadingInFirstRender="false"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

### 3. 虚拟滚动优化

```razor
<Table TItem="Foo" ScrollMode="ScrollMode.Virtual" 
       RowHeight="50" OverscanCount="5"
       OnQueryAsync="@OnQueryAsync">
    <!-- 表格列定义 -->
</Table>
```

## 事件处理

### 常用事件

| 事件 | 类型 | 说明 |
|------|------|------|
| OnQueryAsync | Func<QueryPageOptions, Task<QueryData<TItem>>> | 查询数据事件 |
| OnAddAsync | Func<Task<bool>> | 新增数据事件 |
| OnSaveAsync | Func<TItem, ItemChangedType, Task<bool>> | 保存数据事件 |
| OnDeleteAsync | Func<IEnumerable<TItem>, Task<bool>> | 删除数据事件 |
| OnClickRowCallback | Func<TItem, Task> | 行点击事件 |
| OnDoubleClickRowCallback | Func<TItem, Task> | 行双击事件 |
| OnSelectedRowsChanged | Func<IEnumerable<TItem>, Task> | 选中行变化事件 |

### 事件使用示例

```razor
<Table TItem="Foo" OnClickRowCallback="@OnRowClick" 
       OnDoubleClickRowCallback="@OnRowDoubleClick"
       OnSelectedRowsChanged="@OnSelectionChanged">
    <!-- 表格列定义 -->
</Table>

@code {
    private Task OnRowClick(Foo item)
    {
        // 处理行点击
        return Task.CompletedTask;
    }
    
    private Task OnRowDoubleClick(Foo item)
    {
        // 处理行双击
        return Task.CompletedTask;
    }
    
    private Task OnSelectionChanged(IEnumerable<Foo> selectedItems)
    {
        // 处理选中项变化
        return Task.CompletedTask;
    }
}
```

## 样式定制

### CSS 类名

| 类名 | 说明 |
|------|------|
| .table | 基础表格样式 |
| .table-striped | 斑马纹样式 |
| .table-bordered | 边框样式 |
| .table-hover | 悬停效果 |
| .table-sm | 紧凑样式 |

### 自定义样式

```css
.custom-table {
    --bs-table-bg: #f8f9fa;
    --bs-table-hover-bg: #e9ecef;
}

.custom-table .table-cell {
    padding: 0.75rem;
    vertical-align: middle;
}
```

## 注意事项

1. **泛型类型**：TItem 必须是引用类型（class）
2. **数据绑定**：使用 `@bind-Field` 进行双向绑定
3. **异步操作**：所有数据操作都应该是异步的
4. **内存管理**：大数据量时建议使用虚拟滚动
5. **响应式设计**：考虑移动端的显示效果
6. **性能优化**：合理使用分页和过滤功能

## 总结

BootstrapBlazor 的 Table 组件是一个功能完整、性能优秀的数据表格解决方案。它支持从简单的数据展示到复杂的数据管理场景，通过丰富的配置选项和扩展功能，可以满足绝大多数业务需求。在实际使用中，建议根据具体场景选择合适的功能组合，并注意性能优化和用户体验。
